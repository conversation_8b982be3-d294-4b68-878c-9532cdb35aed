/* eslint-disable @typescript-eslint/no-var-requires */
const fs = require('fs')
const path = require('path')

//删除release文件夹所有内容
function deleteFolder(path) {
  if (fs.existsSync(path)) {
    fs.readdirSync(path).forEach((file) => {
      const curPath = path + '/' + file
      if (fs.lstatSync(curPath).isDirectory()) {
        deleteFolder(curPath)
      } else {
        fs.unlinkSync(curPath)
      }
    })
    fs.rmdirSync(path)
  }
}
deleteFolder(path.resolve(__dirname, '../dist'))
