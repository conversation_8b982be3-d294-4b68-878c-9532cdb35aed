import React from 'react'

/**
 * 聊天计时工具函数
 * 用于计算和格式化最新聊天持续时间
 */

export interface ChatTimerOptions {
  effectiveTime: number // 有效时间戳
  onUpdate?: (formattedTime: string) => void // 时间更新回调
  interval?: number // 更新间隔，默认1000ms
}

export class ChatTimer {
  private timerId: NodeJS.Timeout | null = null
  private effectiveTime: number
  private onUpdate?: (formattedTime: string) => void
  private interval: number

  constructor(options: ChatTimerOptions) {
    this.effectiveTime = options.effectiveTime
    this.onUpdate = options.onUpdate
    this.interval = options.interval || 1000
  }

  /**
   * 计算时间差并格式化
   * @param effectiveTime 有效时间戳
   * @returns 格式化的时间字符串
   */
  static formatDuration(effectiveTime: number): string {
    if (!effectiveTime) return ''
    
    const now = Date.now()
    const diffMs = now - effectiveTime
    
    // 如果时间差为负数或0，不显示
    if (diffMs <= 0) return ''
    
    const diffSeconds = Math.floor(diffMs / 1000)
    
    if (diffSeconds < 60) {
      // 1分钟以内显示秒数
      return `${diffSeconds}S`
    } else {
      // 超过1分钟显示分钟数
      const diffMinutes = Math.floor(diffSeconds / 60)
      return `${diffMinutes}M`
    }
  }

  /**
   * 开始计时
   */
  start(): void {
    this.stop() // 先停止之前的计时器
    
    // 立即执行一次
    const formattedTime = ChatTimer.formatDuration(this.effectiveTime)
    this.onUpdate?.(formattedTime)
    
    // 设置定时器
    this.timerId = setInterval(() => {
      const formattedTime = ChatTimer.formatDuration(this.effectiveTime)
      this.onUpdate?.(formattedTime)
    }, this.interval)
  }

  /**
   * 停止计时
   */
  stop(): void {
    if (this.timerId) {
      clearInterval(this.timerId)
      this.timerId = null
    }
  }

  /**
   * 更新有效时间并重新开始计时
   * @param newEffectiveTime 新的有效时间戳
   */
  updateEffectiveTime(newEffectiveTime: number): void {
    this.effectiveTime = newEffectiveTime
    if (this.timerId) {
      // 如果正在计时，重新开始
      this.start()
    }
  }

  /**
   * 销毁计时器
   */
  destroy(): void {
    this.stop()
    this.onUpdate = undefined
  }
}

/**
 * React Hook 用于聊天计时
 * @param effectiveTime 有效时间戳
 * @returns 格式化的时间字符串
 */
export const useChatTimer = (effectiveTime: number): string => {
  const [formattedTime, setFormattedTime] = React.useState('')
  const timerRef = React.useRef<ChatTimer | null>(null)

  React.useEffect(() => {
    if (!effectiveTime) {
      setFormattedTime('')
      return
    }

    // 创建计时器实例
    timerRef.current = new ChatTimer({
      effectiveTime,
      onUpdate: setFormattedTime
    })

    // 开始计时
    timerRef.current.start()

    // 清理函数
    return () => {
      timerRef.current?.destroy()
      timerRef.current = null
    }
  }, [effectiveTime])

  return formattedTime
}
