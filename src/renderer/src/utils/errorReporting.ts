/**
 * <AUTHOR>
 * @param 飞书群机器人webhook
 * @description 将报错信息发送到飞书群
 * @data 2023-07-19
 */
import { useState, useEffect } from 'react'

const ErrorBoundary = ({ children }) => {
  console.log(import.meta.env.MODE)
  const [hasError, setHasError] = useState(false)
  const [error, setError] = useState<ErrorEvent>(null)

  const getOS = () => {
    const platform = navigator.platform
    const userAgent = navigator.userAgent

    if (platform.startsWith('Win')) {
      if (userAgent.includes('Windows NT 10.0')) {
        return 'Windows 10'
      } else if (userAgent.includes('Windows NT 6.2')) {
        return 'Windows 8'
      } else if (userAgent.includes('Windows NT 6.1')) {
        return 'Windows 7'
      } else {
        return 'Windows'
      }
    } else if (platform.startsWith('Mac')) {
      return 'MacOS'
    } else if (platform.startsWith('Linux')) {
      return 'Linux'
    } else if (platform.startsWith('iPad') || userAgent.includes('iPhone')) {
      return 'iOS'
    } else {
      return 'Unknown'
    }
  }

  useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      setHasError(true)
      setError(error)
      console.log(error)
      console.error(error)
    }
    //捕获未处理的promise错误
    const handleUnhandledRejection = (e) => {
      console.log(e)
      setHasError(true)
      setError(e.reason)
      console.error(e.reason)
      e.preventDefault()
    }

    try {
      window.addEventListener('error', handleError)
      window.addEventListener('unhandledrejection', handleUnhandledRejection)
      console.log('error')
    } catch (e) {
      console.log(e)
    }

    return () => {
      window.removeEventListener('error', handleError)
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [])

  if (hasError) {
    console.log('hasError', error?.error?.stack ?? error)
    const token = window.localStorage.getItem('token')
    const currentVersion = localStorage.getItem('currentVersion')
    const userInfo = localStorage.getItem('userInfo') || '{}'
    const requestBody = {
      msg_type: 'interactive',
      card: {
        config: {
          wide_screen_mode: true,
          enable_forward: true,
          update_multi: true
        },
        header: {
          template: 'red',
          title: {
            content: 'Live Chat客户端报错\n',
            tag: 'plain_text'
          }
        },
        elements: [
          {
            tag: 'div',
            text: {
              content: `报错页面链接：${window.location.href}`,
              tag: 'lark_md'
            }
          },
          {
            tag: 'hr'
          },
          {
            tag: 'div',
            text: {
              content: `报错内容: ${error?.error?.stack ?? error}`,
              tag: 'plain_text'
            }
          },
          {
            tag: 'hr'
          },
          {
            tag: 'div',
            text: {
              content: `用户的token: ${token}`,
              tag: 'plain_text'
            }
          },
          {
            tag: 'div',
            text: {
              content: `用户姓名: ${JSON.parse(userInfo)?.nickname}`,
              tag: 'plain_text'
            }
          },
          {
            tag: 'hr'
          },
          {
            tag: 'div',
            text: {
              content: `用户应用版本号: ${currentVersion}`,
              tag: 'plain_text'
            }
          },
          {
            tag: 'hr'
          },
          {
            tag: 'div',
            text: {
              content: `用户的操作系统: ${getOS()}`,
              tag: 'plain_text'
            }
          }
        ]
      }
    }
    const webhookUrl =
      'https://open.feishu.cn/open-apis/bot/v2/hook/eae60ab9-123d-4f54-9c45-9aa12b389466'
    //url中包含localhost或者是开发环境，不发送错误信息
    if (
      window.location.href.includes('localhost') ||
      import.meta.env.MODE != 'production'
    )
      return children
    fetch(webhookUrl, {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }

  return children
}

export default ErrorBoundary
