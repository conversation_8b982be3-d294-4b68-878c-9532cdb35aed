//localForage存储
import localForage from 'localforage'
interface StoreData {
  data: any
  timestamp: number //存储时间
  expire: number //过期时间
}
type OrdinaryStore = Omit<StoreData, 'timestamp'>
type MessageStore = Omit<StoreData, 'data'>
type MessageStoreItem = MessageStore & {
  chatId: string //会话id
  message: any // 消息内容
}
//聊天记录存储
const indexDBStore = localForage.createInstance({
  name: 'messageStore'
})
//普通存储
const ordinaryStore = localForage.createInstance({
  name: 'ordinaryStore'
})
export const getStoreData = async (key: string) => {
  const storeData: StoreData = await ordinaryStore.getItem(key)
  if (!storeData) return null
  if (new Date().getTime() - storeData.timestamp > storeData.expire) {
    await ordinaryStore.removeItem(key)
    return null
  }
  return storeData.data
}
//存储一般数据
export const setStoreData = async (key: string, value: OrdinaryStore) => {
  const { expire = null, data } = value
  const storeData: StoreData = {
    data,
    timestamp: new Date().getTime(),
    expire: expire || 1000 * 60 * 60 * 24 * 7 //设置默认过期时间为7天
  }
  return await ordinaryStore.setItem(key, storeData)
}
//存储聊天记录
export const setMessageStoreData = async (
  key: string,
  value: Array<MessageStoreItem>
) => {
  const storeData: Array<MessageStoreItem> = await indexDBStore.getItem(key)
  if (!storeData) {
    return await indexDBStore.setItem(key, value)
  } else {
    const newData = [...storeData, ...value]
    return await indexDBStore.setItem(key, newData)
  }
}
//获取聊天记录
export const getMessageStoreData = async (key: string) => {
  const storeData: Array<MessageStoreItem> = await indexDBStore.getItem(key)
  if (!storeData) return null
  const newData = storeData.filter(
    (item) => item.timestamp + item.expire > new Date().getTime()
  )
  return newData
}
//清除普通过期数据
export const clearStoreData = async () => {
  const keys = await indexDBStore.keys()
  keys.forEach(async (key) => {
    const storeData: StoreData = await indexDBStore.getItem(key)
    if (new Date().getTime() - storeData.timestamp > storeData.expire) {
      await indexDBStore.removeItem(key)
    }
  })
}
//清除过期聊天记录
export const clearMessageStoreData = async () => {
  const keys = await indexDBStore.keys()
  keys.forEach(async (key) => {
    const messageData: Array<MessageStoreItem> = await indexDBStore.getItem(key)
    if (!messageData) return
    const newData = messageData.map(
      (item) => item.timestamp + item.expire > new Date().getTime()
    )
    await indexDBStore.setItem(key, newData)
  })
}
