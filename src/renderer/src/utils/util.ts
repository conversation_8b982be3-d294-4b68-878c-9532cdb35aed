//转换菜单
export function transformMenu(data: Array<any>) {
  const result: any = []
  data?.forEach((item) => {
    const { children, isMenu, ...rest } = item
    if (children && children?.length > 0) {
      const childrenItem = transformMenu(children)
      if (item.isMenu) {
        result.push({
          ...rest,
          children: childrenItem
        })
      }
    } else {
      item.isMenu &&
        result.push({
          ...rest
        })
    }
  })
  return result
}
//上传文件签名生成，time、name
export const getSignature = (file: File) => {
  const date = new Date()
  const time = `${date.getFullYear()}${(date.getMonth() + 1 + '').padStart(
    2,
    '0'
  )}${(date.getDate() + '').padStart(2, '0')}${(date.getHours() + '').padStart(
    2,
    '0'
  )}${(date.getMinutes() + '').padStart(2, '0')}${(
    date.getSeconds() + ''
  ).padStart(2, '0')}`
  const name = `${time}${Math.random().toString(36).slice(-6)}.${file.name
    .split('.')
    .at(-1)}`
  return { time, name }
}

// // 文件上传
// export const uploadFile = (
//   file: File,
//   option: any,
//   uploadPath = import.meta.env.VITE_PATH
// ) => {
//   const { onProgress = () => {}, onError = () => {} } = option
// }
//生成uuid
export const getUuid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

// 站点颜色
export const siteColorMap: Record<string, string> = {
  cn: '#EE433D',
  hk: '#FFBE16',
  mo: '#0B5A87',
  tw: '#C316FF',
  sg: '#ED753D',
  de: '#EDA73D',
  'de-en': '#EDA73D',
  es: '#EAD83C',
  jp: '#CCE130',
  mx: '#92C43D',
  it: '#48C43D',
  uk: '#3DC4BB',
  au: '#3D9DEC',
  us: '#4E4BFF',
  ru: '#7D3DEC',
  fr: '#E33DEC',
  box: '#EC3D9D',
  nl: '#16A4FF',
  other: '#A2B4CD'
}
//获取浏览器信息
export const getBrowserInfo = () => {
  //当前浏览器类型
  const browser = {
    versions: (function () {
      const u = navigator.userAgent
      return {
        chrome: u.indexOf('Chrome') > -1,
        firefox: u.indexOf('Firefox') > -1,
        edge: u.indexOf('Edge') > -1,
        ie: u.indexOf('Trident') > -1,
        opera: u.indexOf('Presto') > -1,
        safari: u.indexOf('AppleWebKit') > -1,
        weixin: u.indexOf('MicroMessenger') > -1,
        qq: u.indexOf('QQBrowser') > -1
      }
    })()
  }
  return browser
}

// 格式化UTC时间
export const formatUTC = (utcTime: string) => {
  const nd = new Date(Number(utcTime))
  if (isNaN(nd.getTime())) return utcTime
  const today = new Date()
  const isToday = nd.toDateString() === today.toDateString()
  if (isToday) {
    // 格式化为12小时制的时分（am/pm）
    let hours = nd.getHours()
    const minutes = nd.getMinutes().toString().padStart(2, '0')
    const amPm = hours >= 12 ? 'PM' : 'AM'
    hours = hours % 12 || 12
    return `${hours}:${minutes}${amPm}`
  } else {
    // 格式化为年月日
    const year = nd.getFullYear()
    const month = (nd.getMonth() + 1).toString().padStart(2, '0')
    const day = nd.getDate().toString().padStart(2, '0')
    return `${year}-${month}-${day}`
  }
}
export const formatUTCToFull = (utcTime: string) => {
  const nd = new Date(Number(utcTime))
  if (isNaN(nd.getTime())) return utcTime
  const year = nd.getFullYear()
  const month = (nd.getMonth() + 1).toString().padStart(2, '0')
  const day = nd.getDate().toString().padStart(2, '0')
  let hours = nd.getHours()
  const minutes = nd.getMinutes().toString().padStart(2, '0')
  const amPm = hours >= 12 ? 'PM' : 'AM'
  hours = hours % 12 || 12
  return `${year}-${month}-${day} ${hours}:${minutes}${amPm}`
}
// 格式化本地时间，取时分，12小时制
export const formatLocalTime = () => {
  const nd = new Date()
  let hours = nd.getHours()
  const minutes = nd.getMinutes().toString().padStart(2, '0')
  const amPm = hours >= 12 ? 'PM' : 'AM'
  hours = hours % 12 || 12
  return `${hours}:${minutes}${amPm}`
}
// 将单位为秒钟的时间格式化为时分秒
export const formatSeconds = (seconds: number) => {
  const h = Math.floor(seconds / 3600)
  const m = Math.floor((seconds % 3600) / 60)
  const s = Math.floor((seconds % 3600) % 60)
  return `${h ? `${h}hour` : ''}${m ? `${m}min` : ''}${s ? `${s}s` : '0s'}`
}
//生成UUID
export const generateUUID = () => {
  let d = new Date().getTime()
  const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
    /[xy]/g,
    function (c) {
      const r = (d + Math.random() * 16) % 16 | 0
      d = Math.floor(d / 16)
      return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16)
    }
  )
  return uuid
}

// 获取当前时区
export const getCurrentTimeZone = () => {
  const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone
  return timeZone
}
//解析JWT
export const parseJwt = (token: string) => {
  const base64Url = token.split('.')[1]
  const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
  return JSON.parse(window.atob(base64))
}

export const downloadCSVFile = (data: string, fileName: string) => {
  const csvData = data
  const csvStr = `data:text/csv;charset=utf-8,${csvData}`
  const downloadLink = document.createElement('a')
  downloadLink.href = encodeURI(csvStr)
  downloadLink.download = fileName
  document.body.appendChild(downloadLink)
  downloadLink.click()
  document.body.removeChild(downloadLink)
}
