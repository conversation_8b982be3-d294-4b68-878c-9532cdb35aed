import ByteBuffer from './byteBuffer'
import Base64 from './base64'
const b64 = new Base64()

// q: getLen的作用是什么？
// a: getLen的作用是计算字符串的长度，一个汉字占3个字节，一个英文字符占1个字节
export function getLen(str: string) {
  let len = 0
  for (let i = 0; i < str.length; i++) {
    const c = str.charCodeAt(i)
    //单字节加1
    if ((c >= 0x0001 && c <= 0x007e) || (0xff60 <= c && c <= 0xff9f)) {
      len++
    } else {
      len += 3
    }
  }
  return len
}

//发送消息组装
export function sendMsg(msg: any, option: Record<string, any> = {}) {
  const { version = 1, messageType = 2, sequenceId = 0, seqType = 1 } = option
  const msgBuf = new ByteBuffer()
  const magic = [108, 105, 118, 101, 99, 104, 97, 116]
  const token = localStorage.getItem('live_chat_token')
  msg.headers = {
    Authorization: token
  }
  const s = b64.strToBase64(JSON.stringify(msg))
  const bodyLen = getLen(s)
  msgBuf
    .byteArray(magic, magic.length)
    .byte(version)
    .byte(seqType)
    .byte(messageType)
    .int64(sequenceId)
    .byte(0x1)
    .int32(bodyLen)
    .vstring(s, bodyLen)
  console.log('------sendmessage:发送消息', msg, option.messageType)
  return msgBuf.pack()
}

export const unpackMsg = (e: MessageEvent) => {
  // 消息解包
  // const formatData = checkDataType(e.data)
  const byteBuffer = new ByteBuffer(e.data)
  const read = byteBuffer
    .byteArray(null, 8, null)
    .byte()
    .byte()
    .byte()
    .int64()
    .byte()
    .int32()
    .unpack()
  const bodyLen = read[6]
  const data = byteBuffer.vstring(null, bodyLen).unpack()
  const base64decode = b64.base64ToStr(data[7])
  const parse = JSON.parse(base64decode)
  return parse
}
function checkDataType(data: any) {
  //转换类型
  return new Promise((resolve, reject) => {
    if (data instanceof ArrayBuffer) {
      resolve(data)
    } else if (data instanceof Blob) {
      const reader = new FileReader()
      reader.onloadend = function () {
        resolve(reader.result)
      }
      reader.readAsArrayBuffer(data)
    } else {
      reject()
    }
  })
}
