import store from '@/store'
import { sendMsg, unpackMsg } from './chat'
import { setSocketStatus } from '@/store/modules/common'
import crypto, { base64Encrypt } from './crypto'
import { message } from 'antd'
//web端websocket工具类
class Socket {
  static instance: Socket = null
  eventMap: Array<Record<string, string>> = []
  //单例
  static getInstance(params: any) {
    if (!Socket.instance) {
      Socket.instance = new Socket(params)
    }
    return Socket.instance
  }
  //websocket对象
  private ws: WebSocket //WebSocket.readyState 0：正在连接，1：连接成功，2：关闭连接，3：连接出错或者关闭
  //心跳定时器
  private heartTimer: any
  //心跳间隔
  private heartInterval = 50000
  //心跳消息
  private heartMsg = { messageType: 14 }
  //重连定时器
  private reconnectTimer = null
  //重连间隔
  private reconnectInterval = 5000
  //重连次数
  private reconnectCount = 0
  //重连最大次数
  private reconnectMaxCount = 30
  // 是否重连过
  private isReconnected = false
  //重连回调
  private reconnectCallback: (count: number) => void | null
  //消息回调
  private messageCallback: (e: any) => void | null
  //关闭回调
  private closeCallback: (e: any) => void | null
  //错误回调
  private errorCallback: (e: any) => void | null
  //连接成功回调
  private openCallback: () => void | null
  //网络在线回调
  private onlineCallback: () => void | null = () => {}
  //网络离线回调
  private offlineCallback: () => void | null = () => {}
  //网络状态
  private networkStatus: 'online' | 'offline' = 'online'
  //连接地址
  private url: string
  //连接参数
  private params: any
  //连接状态
  private status = 0
  //连接状态
  private statusMap: any = {
    0: '未连接',
    1: '连接中',
    2: '已连接',
    3: '连接关闭',
    4: '连接错误',
    5: '连接中断',
    6: '连接超时'
  }
  private clientToken = '' // 飞书登录之后客户端token
  private protocols = {}
  constructor(option: any) {
    this.url = option.url
    this.clientToken = option.requestHeader.token
    this.params = option?.params
    //事件映射
    this.eventMap = [
      {
        type: 'open',
        callback: 'openCallback'
      },
      {
        type: 'message',
        callback: 'messageCallback'
      },
      {
        type: 'close',
        callback: 'closeCallback'
      },
      {
        type: 'error',
        callback: 'errorCallback'
      },
      {
        type: 'reconnect',
        callback: 'reconnectCallback'
      }
    ]
    this.init()
  }
  //初始化
  private init() {
    this.createWebSocket()
    this.listenNetworkChange()
  }
  // 监听网络状态变化
  private listenNetworkChange() {
    window.addEventListener('online', () => {
      this.networkStatus = 'online'
      this.onlineCallback()
    })
    window.addEventListener('offline', () => {
      this.networkStatus = 'offline'
      console.log('-------offline:网络离线')
      this.offlineCallback()
    })
  }
  //创建websocket
  public createWebSocket() {
    this.status = 1
    //转换参数
    const searchParams = new URLSearchParams(this.params)
    const chatToken = localStorage.getItem('live_chat_token')
    this.protocols = {
      clientToken: this.clientToken,
      chatToken: chatToken,
      ...crypto
    }
    const queryString = searchParams.toString()
    const encryptText = base64Encrypt(JSON.stringify(this.protocols))
    this.ws = new WebSocket(
      `${this.url}?${queryString}`,
      encodeURIComponent(encryptText)
    )
    this.ws.binaryType = 'arraybuffer' // 设置数据格式为二进制
    this.ws.onopen = this.onOpen.bind(this)
    this.ws.onmessage = this.onMessage.bind(this)
    this.ws.onclose = this.onClose.bind(this)
    this.ws.onerror = this.onError.bind(this)
  }
  //连接成功
  private onOpen() {
    this.status = 2
    store.dispatch(setSocketStatus('open'))
    this.heartCheck()
    this.openCallback && this.openCallback()
  }
  //事件监听
  public on(type: string, callback: any) {
    this.eventMap.map((item: Record<string, string>) => {
      if (item.type === type) {
        this[item.callback as keyof Socket] = callback
      }
    })
  }
  //移除事件监听
  public off(type: string) {
    this.eventMap.map((item: Record<string, string>) => {
      if (item.type === type) {
        this[item.callback as keyof Socket] = null
      }
    })
  }
  //接收消息
  private onMessage(e: MessageEvent) {
    this.reconnectCount = 0 //重连次数重置
    try {
      const parse = unpackMsg(e)
      console.log('-----onmessage:接收消息', parse)
      this.messageCallback && this.messageCallback(parse)
      this.handleMessageType(parse)
    } catch (error) {
      console.log(error)
    }
  }
  //连接关闭
  private onClose(e: CloseEvent) {
    console.log('-------close:关闭聊天', e.code, e.reason, e.wasClean)
    store.dispatch(setSocketStatus('close'))
    this.closeCallback && this.closeCallback(e)
    this.reconnect()
    this.status = 3
  }
  //连接错误
  private onError(e: Event) {
    console.log('-------error:聊天错误', e)
    store.dispatch(setSocketStatus('error'))
    this.errorCallback && this.errorCallback(e)
    this.reconnect()
    this.status = 4
  }
  //心跳检测
  private heartCheck() {
    clearTimeout(this.heartTimer)
    this.heartTimer = null
    if (this.ws.readyState !== 1) return
    this.heartTimer = setTimeout(() => {
      this.emit({}, this.heartMsg)
      this.heartCheck()
    }, this.heartInterval)
  }
  //重连
  private reconnect() {
    console.log(
      '---重连---:ws状态',
      this.status,
      this.ws.readyState,
      '重连次数',
      this.reconnectCount
    )
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    console.log('-----reconnect---: 重连')
    if (this.reconnectCount < this.reconnectMaxCount) {
      this.reconnectCount++
      this.isReconnected = true
      this.reconnectTimer = setTimeout(() => {
        this.createWebSocket()
        this.reconnectCallback && this.reconnectCallback(this.reconnectCount)
      }, this.reconnectInterval)
    }
    this.status = 6
  }
  //发送消息
  public emit(msg: any, option: any) {
    if (this.networkStatus === 'offline' || this.ws.readyState !== 1)
      return message.error('network error, please check your network')
    this.ws.send(sendMsg(msg, option))
  }
  //关闭连接
  public close() {
    this.ws.close()
    this.status = 5
  }
  //获取连接状态
  public getStatus() {
    return this.statusMap[this.status]
  }
  private firstConnection() {
    //第一次连接成功直接发送22类型消息
    this.emit({ isReconnect: this.isReconnected }, { messageType: 22 })
  }
  private handleMessageType(mes: any) {
    if (mes.messageType === 35) {
      // window.localStorage &&
      //   window.localStorage.setItem('live_chat_id', mes.chatId)
      this.firstConnection()
    } else if (mes.messageType == 15) {
      window.localStorage &&
        window.localStorage.setItem('live_chat_token', mes.token)
    }
  }
}
export default Socket
