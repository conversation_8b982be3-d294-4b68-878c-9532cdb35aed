class ByteBuffer {
  private buffer: any
  private offset: number
  private list: any[]
  private islittleEndian: boolean
  private Type_Byte = 1
  private Type_Short = 2
  private Type_UShort = 3
  private Type_Int32 = 4
  private Type_UInt32 = 5
  private Type_String = 6 //变长字符串，前两个字节表示长度
  private Type_VString = 7 //定长字符串
  private Type_Int64 = 8
  private Type_Float = 9
  private Type_Double = 10
  private Type_ByteArray = 11

  constructor(arrayBuf?: Buffer, offset = 0) {
    this.buffer = arrayBuf
      ? arrayBuf.constructor == DataView
        ? arrayBuf
        : arrayBuf.constructor == Uint8Array
        ? new DataView(arrayBuf.buffer, offset)
        : new DataView(arrayBuf as any, offset)
      : new DataView(new Uint8Array([]).buffer)
    this.offset = offset
    this.islittleEndian = false
    this.list = []
    this.resetSlice()
  }
  //重写slice
  public resetSlice() {
    if (!ArrayBuffer.prototype.slice) {
      ArrayBuffer.prototype.slice = function (start, end) {
        const that = new Uint8Array(this)
        if (end == undefined) end = that.length
        const result = new ArrayBuffer(end - start)
        const resultArray = new Uint8Array(result)
        for (let i = 0; i < resultArray.length; i++)
          resultArray[i] = that[i + start]
        return result
      }
    }
  }
  public byte(val?: any, index?: any) {
    if (arguments.length == 0) {
      this.list.push(this.buffer.getUint8(this.offset, this.islittleEndian))
      this.offset += 1
    } else {
      this.list.splice(index != undefined ? index : this.list.length, 0, {
        t: this.Type_Byte,
        d: val,
        l: 1
      })
      this.offset += 1
    }
    return this
  }

  public short(val: any, index: any) {
    if (arguments.length == 0) {
      this.list.push(this.buffer.getInt16(this.offset, this.islittleEndian))
      this.offset += 2
    } else {
      this.list.splice(index != undefined ? index : this.list.length, 0, {
        t: this.Type_Short,
        d: val,
        l: 2
      })
      this.offset += 2
    }
    return this
  }
  public ushort(val: any, index: any) {
    if (arguments.length == 0) {
      this.list.push(this.buffer.getUint16(this.offset, this.islittleEndian))
      this.offset += 2
    } else {
      this.list.splice(index != undefined ? index : this.list.length, 0, {
        t: this.Type_UShort,
        d: val,
        l: 2
      })
      this.offset += 2
    }
    return this
  }
  public int32(val?: any, index?: any) {
    if (arguments.length == 0) {
      this.list.push(this.buffer.getInt32(this.offset, this.islittleEndian))
      this.offset += 4
    } else {
      this.list.splice(index != undefined ? index : this.list.length, 0, {
        t: this.Type_Int32,
        d: val,
        l: 4
      })
      this.offset += 4
    }
    return this
  }
  public uint32(val: any, index: any) {
    if (arguments.length == 0) {
      this.list.push(this.buffer.getUint32(this.offset, this.islittleEndian))
      this.offset += 4
    } else {
      this.list.splice(index != undefined ? index : this.list.length, 0, {
        t: this.Type_UInt32,
        d: val,
        l: 4
      })
      this.offset += 4
    }
    return this
  }
  /**
   * 新加的方法，获取bytebuffer的长度
   */
  public blength() {
    return this.offset
  }

  /**
   * 变长字符串 前4个字节表示字符串长度
   **/
  public string(val: any, index: any) {
    let len = 0
    if (arguments.length == 0) {
      len = this.buffer.getInt32(this.offset, this.islittleEndian)
      this.offset += 4
      this.list.push(this.utf8Read(this.buffer, this.offset, len))
      this.offset += len
    } else {
      len = 0
      if (val) {
        len = this.utf8Length(val)
      }
      this.list.splice(index != undefined ? index : this.list.length, 0, {
        t: this.Type_String,
        d: val,
        l: len
      })
      this.offset += len + 4
    }
    return this
  }

  /**
   * 定长字符串 val为null时，读取定长字符串（需指定长度len）
   **/
  public vstring(val: any, len: any, index?: any) {
    if (!len) {
      throw new Error('vstring must got len argument')
      return this
    }
    if (val == undefined || val == null) {
      let vlen = 0 //实际长度
      for (let i = this.offset; i < this.offset + len; i++) {
        if (this.buffer.getUint8(i) > 0) vlen++
      }
      this.list.push(this.utf8Read(this.buffer, this.offset, vlen))
      this.offset += len
    } else {
      this.list.splice(index != undefined ? index : this.list.length, 0, {
        t: this.Type_VString,
        d: val,
        l: len
      })
      this.offset += len
    }
    return this
  }

  public int64(val?: any, index?: any) {
    if (arguments.length == 0) {
      this.list.push(this.buffer.getFloat64(this.offset, this.islittleEndian))
      this.offset += 8
    } else {
      this.list.splice(index != undefined ? index : this.list.length, 0, {
        t: this.Type_Int64,
        d: val,
        l: 8
      })
      this.offset += 8
    }
    return this
  }
  public float(val: any, index: any) {
    if (arguments.length == 0) {
      this.list.push(this.buffer.getFloat32(this.offset, this.islittleEndian))
      this.offset += 4
    } else {
      this.list.splice(index != undefined ? index : this.list.length, 0, {
        t: this.Type_Float,
        d: val,
        l: 4
      })
      this.offset += 4
    }
    return this
  }
  public double(val: any, index: any) {
    if (arguments.length == 0) {
      this.list.push(this.buffer.getFloat64(this.offset, this.islittleEndian))
      this.offset += 8
    } else {
      this.list.splice(index != undefined ? index : this.list.length, 0, {
        t: this.Type_Double,
        d: val,
        l: 8
      })
      this.offset += 8
    }
    return this
  }
  /**
   * 写入或读取一段字节数组
   **/
  public byteArray = (val: any, len: any, index?: any) => {
    if (!len) {
      throw new Error('byteArray must got len argument')
      return this
    }
    if (val == undefined || val == null) {
      const arr = new Uint8Array(
        this.buffer.buffer.slice(this.offset, this.offset + len)
      )
      this.list.push(arr)
      this.offset += len
    } else {
      this.list.splice(index != undefined ? index : this.list.length, 0, {
        t: this.Type_ByteArray,
        d: val,
        l: len
      })
      this.offset += len
    }
    return this
  }
  /**
   * 解包成数据数组
   **/
  public unpack = () => {
    return this.list
  }

  /**
   * 打包成二进制,在前面加上4个字节表示包长
   **/
  public packWithHead = () => {
    return this.pack(true)
  }
  /**
   * 打包成二进制
   * @param ifHead 是否在前面加上4个字节表示包长
   **/
  public pack = (ifHead?: boolean) => {
    this.buffer = new DataView(
      new ArrayBuffer(ifHead ? this.offset + 4 : this.offset)
    )
    let offset = 0
    if (ifHead) {
      this.buffer.setUint32(offset, this.offset, this.islittleEndian)
      offset += 4
    }
    for (let i = 0; i < this.list.length; i++) {
      switch (this.list[i].t) {
        case this.Type_Byte:
          this.buffer.setInt8(offset, this.list[i].d)
          offset += this.list[i].l
          break
        case this.Type_Short:
          this.buffer.setInt16(offset, this.list[i].d, this.islittleEndian)
          offset += this.list[i].l
          break
        case this.Type_UShort:
          this.buffer.setUint16(offset, this.list[i].d, this.islittleEndian)
          offset += this.list[i].l
          break
        case this.Type_Int32:
          this.buffer.setInt32(offset, this.list[i].d, this.islittleEndian)
          offset += this.list[i].l
          break
        case this.Type_UInt32:
          this.buffer.setUint32(offset, this.list[i].d, this.islittleEndian)
          offset += this.list[i].l
          break
        case this.Type_String:
          //前4个字节表示字符串长度
          this.buffer.setUint32(offset, this.list[i].l, this.islittleEndian)
          offset += 4
          this.utf8Write(this.buffer, offset, this.list[i].d)
          offset += this.list[i].l
          break
        case this.Type_VString:
          this.utf8Write(this.buffer, offset, this.list[i].d)
          // eslint-disable-next-line no-case-declarations
          const vlen = this.utf8Length(this.list[i].d) //字符串实际长度
          //补齐\0
          for (let j = offset + vlen; j < offset + this.list[i].l; j++) {
            this.buffer.setUint8(j, 0)
          }
          offset += this.list[i].l
          break
        case this.Type_Int64:
          this.buffer.setFloat64(offset, this.list[i].d, this.islittleEndian)
          offset += this.list[i].l
          break
        case this.Type_Float:
          this.buffer.setFloat32(offset, this.list[i].d, this.islittleEndian)
          offset += this.list[i].l
          break
        case this.Type_Double:
          this.buffer.setFloat64(offset, this.list[i].d, this.islittleEndian)
          offset += this.list[i].l
          break
        case this.Type_ByteArray:
          // eslint-disable-next-line no-case-declarations
          let indx = 0
          for (let j = offset; j < offset + this.list[i].l; j++) {
            if (indx < this.list[i].d.length) {
              this.buffer.setUint8(j, this.list[i].d[indx])
            } else {
              //不够的话，后面补齐0x00
              this.buffer.setUint8(j, 0)
            }
            indx++
          }
          offset += this.list[i].l
          break
      }
    }
    return this.buffer.buffer
  }
  /**
   * 未读数据长度
   **/
  public getAvailable = () => {
    if (!this.buffer) return this.offset
    return this.buffer.buffer.byteLength - this.offset
  }

  //指定字节序 为BigEndian
  public bigEndian() {
    this.islittleEndian = false
    return this
  }

  //指定字节序 为LittleEndian
  public littleEndian() {
    this.islittleEndian = true
    return this
  }
  public utf8Write(view: any, offset: any, str: any) {
    let c = 0
    for (let i = 0, l = str.length; i < l; i++) {
      c = str.charCodeAt(i)
      if (c < 0x80) {
        view.setUint8(offset++, c)
      } else if (c < 0x800) {
        view.setUint8(offset++, 0xc0 | (c >> 6))
        view.setUint8(offset++, 0x80 | (c & 0x3f))
      } else if (c < 0xd800 || c >= 0xe000) {
        view.setUint8(offset++, 0xe0 | (c >> 12))
        view.setUint8(offset++, 0x80 | ((c >> 6) & 0x3f))
        view.setUint8(offset++, 0x80 | (c & 0x3f))
      } else {
        i++
        c = 0x10000 + (((c & 0x3ff) << 10) | (str.charCodeAt(i) & 0x3ff))
        view.setUint8(offset++, 0xf0 | (c >> 18))
        view.setUint8(offset++, 0x80 | ((c >> 12) & 0x3f))
        view.setUint8(offset++, 0x80 | ((c >> 6) & 0x3f))
        view.setUint8(offset++, 0x80 | (c & 0x3f))
      }
    }
  }
  public utf8Read(view: any, offset: any, length: any) {
    let string = '',
      chr = 0
    for (let i = offset, end = offset + length; i < end; i++) {
      const byte = view.getUint8(i)
      if ((byte & 0x80) === 0x00) {
        string += String.fromCharCode(byte)
        continue
      }
      if ((byte & 0xe0) === 0xc0) {
        string += String.fromCharCode(
          ((byte & 0x0f) << 6) | (view.getUint8(++i) & 0x3f)
        )
        continue
      }
      if ((byte & 0xf0) === 0xe0) {
        string += String.fromCharCode(
          ((byte & 0x0f) << 12) |
            ((view.getUint8(++i) & 0x3f) << 6) |
            ((view.getUint8(++i) & 0x3f) << 0)
        )
        continue
      }
      if ((byte & 0xf8) === 0xf0) {
        chr =
          ((byte & 0x07) << 18) |
          ((view.getUint8(++i) & 0x3f) << 12) |
          ((view.getUint8(++i) & 0x3f) << 6) |
          ((view.getUint8(++i) & 0x3f) << 0)
        if (chr >= 0x010000) {
          // surrogate pair
          chr -= 0x010000
          string += String.fromCharCode(
            (chr >>> 10) + 0xd800,
            (chr & 0x3ff) + 0xdc00
          )
        } else {
          string += String.fromCharCode(chr)
        }
        continue
      }
      throw new Error('Invalid byte ' + byte.toString(16))
    }
    return string
  }
  public utf8Length(str: string) {
    let c = 0,
      length = 0
    for (let i = 0, l = str.length; i < l; i++) {
      c = str.charCodeAt(i)
      if (c < 0x80) {
        length += 1
      } else if (c < 0x800) {
        length += 2
      } else if (c < 0xd800 || c >= 0xe000) {
        length += 3
      } else {
        i++
        length += 4
      }
    }
    return length
  }
}
export default ByteBuffer
