import { HmacSHA256 } from 'crypto-js'
import Base64 from './base64'

const b64 = new Base64()

const privateKey = '4c9fd5122688c7a4'
const publicKey = '87b579c7ab2ea5f8'
//生成随机字符串的方法
export const randomString = () => {
  const randomString = [...window.crypto.getRandomValues(new Uint8Array(8))]
    .map((x) => x.toString(16))
    .join('')
  return randomString
}

export const generateHmacString = () => {
  const timestamp = new Date().getTime()
  const nonce = randomString()
  const authToken = HmacSHA256(nonce + timestamp, privateKey).toString()
  return {
    authToken,
    nonce,
    timestamp
  }
}
export const base64Encrypt = (str: string) => {
  const base64 = b64.strToBase64(str)
  return base64
}
export default {
  publicKey,
  ...generateHmacString()
}
