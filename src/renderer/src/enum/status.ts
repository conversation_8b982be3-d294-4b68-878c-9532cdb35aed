export const settingStatus = [
  {
    label: 'Close',
    value: 0
  },
  {
    label: 'Open',
    value: 1
  },
  {
    label: 'All Status',
    value: 2
  }
]

export const ActivityStatus = [
  {
    label: 'Online',
    value: 2,
    icon: 'icon-zaixian',
    color: '#5BCC51'
  },
  {
    label: 'Supervisor',
    value: 1,
    icon: 'icon-zhidao',
    color: '#F74A44'
  },
  {
    label: 'Chatting',
    value: 0,
    icon: 'icon-liaotian',
    color: '#FFA84B'
  }
]
export const getListPropsValue = (props: any, key: string) => {
  const value = props.find((item: any) => item.value === key)
  return value
}
