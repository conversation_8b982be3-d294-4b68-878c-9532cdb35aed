import { Dropdown, Modal, Tooltip } from 'antd'
import { DownOutlined } from '@ant-design/icons'
import type { MenuProps } from 'antd'
import styles from './side.module.scss'
import { useContext, useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { transformMenu } from '@/utils/util'
import Menu from '../Menu'
import { useLocation, useNavigate } from 'react-router-dom'
import {
  setAsyncCustomerStatus,
  setCustomerLineStatus
} from '@renderer/store/modules/common'
import { useClickAway } from 'ahooks'
import FsAvatar from '@renderer/component/FsAvatar'
import { RootState } from '@renderer/store'
import { GlobalContext } from '@renderer/context/globalContext'
const { confirm } = Modal
export default function Side() {
  const [menuData, setMenuData] = useState<any>([])
  const [activePath, setActivePath] = useState<string>('chats')
  const footerRef = useRef<HTMLDivElement>(null)
  const userInfoRef = useRef<HTMLDivElement>(null)
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const location = useLocation()
  const { pathname } = location
  const userInfo = useSelector((state: any) => state.login.userInfo)
  const customerInfo = useSelector((state: any) => state.login.customerInfo)
  const customerStatus = useSelector(
    (state: any) => state.common.customerStatus
  )
  const currentMsg = useSelector(
    (state: RootState) => state.message.currentReceiveMsg
  )
  const { permissions } = userInfo
  const items: MenuProps['items'] = [
    {
      key: '1',
      label: 'Accepting'
    },
    {
      key: '2',
      label: 'Busy'
    }
  ]
  const statusClassMap = {
    1: styles.online_status_active,
    2: styles.online_status_busy
  }
  const { updateApp } = useContext(GlobalContext)
  const { updateStatus } = updateApp
  useEffect(() => {
    if (currentMsg.messageType === 74) {
      dispatch(setCustomerLineStatus(currentMsg.status))
    }
  }, [currentMsg])
  useEffect(() => {
    const menuData = transformMenu(permissions)
    //剔除settings、logs
    console.log(menuData)
    const newMenu = menuData.filter(
      (item: any) => !['settings', 'logs'].includes(item.path)
    )
    setMenuData(newMenu)
  }, [permissions])
  useClickAway(() => {
    if (userInfoRef.current) {
      userInfoRef.current.style.opacity = '0'
      userInfoRef.current.style.zIndex = '-1'
    }
  }, footerRef)
  useEffect(() => {
    const path = pathname.split('/')[1]
    setActivePath(path)
  }, [location])
  //进入设置页面
  const handleSetting = () => {
    navigate('/settings')
  }
  const handleNavigateLogs = () => {
    navigate('/logs')
  }
  // 设置chat状态
  const handleChatStatus = (key: string) => {
    console.log(customerInfo)
    dispatch(
      setAsyncCustomerStatus({
        statusId: Number(key),
        manageUserId: customerInfo.userId
      }) as any
    )
  }
  const userInfoClick = () => {
    if (userInfoRef.current) {
      userInfoRef.current.style.opacity = '1'
      userInfoRef.current.style.zIndex = '999'
    }
  }
  const loginOut = () => {
    confirm({
      title: 'notice',
      // icon: <ExclamationCircleFilled />,
      content: 'Confirm to sign out ?',
      onOk() {
        localStorage.clear()
        window.ipcRenderer.send('quitApp')
      },
      onCancel() {
        console.log('Cancel')
      }
    })
  }
  return (
    <div className={styles.side_container}>
      <div className={styles.side_header}>
        {/* chat状态图标 */}
        <div className={styles.chat_icon_box}>
          <i
            className={[
              'iconfont',
              'icon-zaixian1',
              customerStatus === 1 ? styles.online : styles.offline
            ].join(' ')}
          ></i>
        </div>
        <Dropdown
          menu={{
            items,
            selectable: true,
            selectedKeys: [customerStatus],
            onSelect: ({ key }) => {
              handleChatStatus(key)
            }
          }}
        >
          <div className={styles.side_chat_status}>
            {customerStatus === 1 ? 'Accepting' : 'Busy'}
            <DownOutlined />
          </div>
        </Dropdown>
      </div>
      <div className={styles.side_body}>
        <Menu menuList={menuData} activePath={activePath} />
      </div>
      <div className={styles.side_footer}>
        <div
          className={[
            styles.side_footer_item,
            styles.side_footer_setting,
            activePath === 'settings' ? styles.setting_active : ''
          ].join(' ')}
          onClick={handleSetting}
        >
          <i className="iconfont icon-shezhi"></i>
          <div className={styles.footer_menu}>Settings</div>
        </div>
        <Tooltip
          // getPopupContainer={(triggerNode) =>
          //   triggerNode.parentElement || document.body
          // }
          // overlayClassName={styles.tooltip}
          placement="right"
          title={
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <i
                className="iconfont icon-huojian"
                style={{ fontSize: '20px' }}
              ></i>
              <span>New Version</span>
            </div>
          }
          color="#EE433D"
          open={updateStatus === 'hasUpdate'}
        >
          <div
            className={[
              styles.side_footer_item,
              styles.side_footer_logs,
              activePath === 'logs' ? styles.setting_active : ''
            ].join(' ')}
            onClick={handleNavigateLogs}
          >
            <i className="iconfont icon-a-rongqi2613"></i>
            <div className={styles.footer_menu}>Logs</div>
          </div>
        </Tooltip>
        {/* 用户头像 */}
        <div
          className={styles.side_footer_user}
          ref={footerRef}
          onClick={userInfoClick}
        >
          <div
            className={styles.user_info_broader}
            onClick={(e) => {
              e.stopPropagation()
            }}
            ref={userInfoRef}
          >
            <div className={styles.user_info_box}>
              <div className={styles.user_info}>
                <FsAvatar
                  src={userInfo?.avatar}
                  onlineStatus={customerStatus}
                />
                <div className={styles.user_info_content}>
                  <div className={styles.user_info_name}>
                    {userInfo?.nickname}
                  </div>
                  <div className={styles.user_info_email}>
                    {userInfo?.email}
                  </div>
                </div>
              </div>
              <div className={styles.status_wrapper}>
                <div
                  className={[
                    styles.online_status_icon,
                    statusClassMap[customerStatus]
                  ].join(' ')}
                >
                  {items.some((item) => item.key == customerStatus) &&
                    (items.find((item) => item.key == customerStatus) as any)
                      .label}
                </div>
              </div>
              <div className={styles.user_exit} onClick={loginOut}>
                <div className={styles.user_exit_icon}>Logout</div>
              </div>
            </div>
          </div>
          <div className={styles.footer_user_avatar}>
            <img className={styles.user_icon} src={userInfo?.avatar} alt="" />
          </div>
        </div>
      </div>
    </div>
  )
}
