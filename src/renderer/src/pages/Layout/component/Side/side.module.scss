.side_container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 80px;
  box-sizing: border-box;
  .side_header {
    background-color: #00063e;
    height: 72px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 4px;
    box-sizing: border-box;
    cursor: pointer;
    .chat_icon_box {
      .online {
        color: #4ed142;
      }
      .offline {
        color: #f74a44;
      }
      i {
        font-size: 32px;
      }
    }
    .side_chat_status {
      font-size: 10px;
      color: #fff;
    }
  }
  .side_body {
    flex: 1;
    background-color: #222652;
    padding-top: 20px;
    box-sizing: border-box;
    color: #888bb3;
    font-size: 10px;
  }
  .side_footer {
    // height: 124px;
    padding: 12px 4px 20px 4px;
    box-sizing: border-box;
    background: linear-gradient(270deg, #444b90 0%, #303569 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    .tooltip {
    }
    .side_footer_item {
      text-align: center;
      color: #888bb3;
      width: 72px;
      height: 52px;
      border-radius: 6px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      cursor: pointer;
      &:hover {
        background-color: #4e80ff;
        color: #fff;
      }
      i {
        font-size: 16px;
      }
      .footer_menu {
        font-size: 10px;
        margin-top: 6px;
        font-weight: normal;
      }
    }
    .setting_active {
      background-color: #4e80ff;
      color: #fff;
    }
    .side_footer_logs {
      margin-bottom: 20px;
    }
    .side_footer_user {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      cursor: pointer;
      .user_info_broader {
        position: absolute;
        top: -140px;
        left: 68px;
        width: 260px;
        height: 160px;
        opacity: 0;
        box-sizing: border-box;
        background-color: #fff;
        transition: all 0.3s;
        // z-index: 10;
        box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.12);
        border-radius: 4px;
        padding: 16px;
        box-sizing: border-box;
        .user_info_box {
          .status_wrapper {
            margin: 8px 0 16px 0;
            .online_status_icon {
              padding: 6px 8px;
              border-radius: 4px;
              font-size: 12px;
              max-width: 74px;
              box-sizing: border-box;
              margin-left: 54px;
            }
            .online_status_active {
              border: 1px solid #67c23a;
              background: #f0f9eb;
              color: #67c23a;
            }
            .online_status_busy {
              border: 1px solid #f56c6c;
              background: #fef0f0;
              color: #f56c6c;
            }
          }
          .user_exit {
            border-top: 1px solid #f0f0f0;
            padding-top: 8px;
            .user_exit_icon {
              padding: 6px 8px;
              font-size: 14px;
              line-height: 22px;
              box-sizing: border-box;
              &:hover {
                background: #fafafa;
              }
            }
          }
          .user_info {
            display: flex;
            align-items: center;
            .user_info_content {
              margin-left: 12px;
              .user_info_name {
                font-size: 16px;
                color: #262626;
                font-weight: bold;
                line-height: 24px;
              }
              .user_info_email {
                font-size: 13px;
                color: #8c8c8c;
                line-height: 22px;
              }
            }
          }
        }
      }
      // &:hover {
      //   .user_info_broader {
      //     height: 160px;
      //     opacity: 1;
      //     left:68px;
      //     top:-140px
      //   }
      // }
      .footer_user_avatar {
        //移入展示user_info_broader
        .user_icon {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background-color: #fff;
        }
      }
    }
  }
}
