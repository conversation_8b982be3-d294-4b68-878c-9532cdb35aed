import { Outlet } from 'react-router-dom'
import { Spin } from 'antd'
import { useSelector } from 'react-redux'
import styles from './index.module.scss'
export default function Content() {
  const loading = useSelector((state: any) => state.common.loading)
  return (
    <div className={styles.content_wrapper}>
      <Spin spinning={loading} wrapperClassName={styles.spin_wrapper}>
        <Outlet />
      </Spin>
    </div>
  )
}
