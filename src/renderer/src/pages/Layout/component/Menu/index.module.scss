.menu_item_wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 52px;
  width: 72px;
  cursor: pointer;
  .menu_item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    border-radius: 6px;
    &:hover {
      background-color: #4e80ff;
      color: #fff;
    }
    &.active {
      background-color: #4e80ff;
      color: #fff;
    }
    .menu_icon {
      position: relative;
      i {
        font-size: 20px;
      }
      :global(.ant-badge) {
        position: absolute;
        right: -6px;
        top: -6px;
      }
    }
    .menu_text {
      font-size: 10px;
      margin-top: 6px;
    }
  }
}
.menu_wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
}
