import { RouterProps } from '@/router'
import styles from './index.module.scss'
import { useNavigate } from 'react-router-dom'
import { useEffect, useMemo } from 'react'
import { Badge } from 'antd'
import { useSelector } from 'react-redux'
import { RootState } from '@renderer/store'
interface MenuItemProps {
  item: RouterProps
  activePath: string
}

export default function MenuItem(props: MenuItemProps) {
  const navigate = useNavigate()
  const { item, activePath } = props
  // const [isShowMessageBadge, setIsShowMessageBadge] = useState(false)
  const messageCountMap = useSelector(
    (state: RootState) => state.chat.messageCountMap
  )
  // useEffect(() => {
  //   if (activePath === 'chats') {
  //     setIsShowMessageBadge(false)
  //   } else {
  //     setIsShowMessageBadge(true)
  //   }
  // }, [activePath])
  const messageCount = useMemo(() => {
    const valueList = Object.values(messageCountMap)
    if (valueList.length) {
      let totalCount = 0
      valueList.forEach((item) => {
        totalCount += item ?? 0
      })
      return totalCount
    } else {
      return 0
    }
  }, [messageCountMap])
  useEffect(() => {
    window.ipcRenderer.send('setTrayCount', messageCount)
  }, [messageCount])
  //配置菜单图标
  const menuIcon: Record<string, string> = {
    Chats: 'icon-a-tubiao-20px11',
    Supervised: 'icon-a-tubiao-20px1',
    Archives: 'icon-a-Archives1',
    Ticket: 'icon-a-222-01',
    Team: 'icon-a-Team-xuanzhong1',
    Reports: 'icon-shujubaogao'
  }
  //跳转路由
  const handleMenuClick = () => {
    navigate(item.path)
  }
  return (
    <div className={styles.menu_item_wrapper} onClick={handleMenuClick}>
      <div
        className={[
          styles['menu_item'],
          item.path === activePath ? styles.active : ''
        ].join(' ')}
      >
        <div className={styles['menu_icon']}>
          {item.path === 'chats' && (
            <Badge count={messageCount} size="small" overflowCount={99} />
          )}
          <i className={`iconfont ${menuIcon[item.name]}`}></i>
        </div>
        <div className={styles['menu_text']}>{props.item?.name}</div>
      </div>
    </div>
  )
}
