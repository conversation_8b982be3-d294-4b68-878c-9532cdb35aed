import { RouterProps } from '@/router'
import MenuItem from './MenuItem'
import styles from './index.module.scss'
import React from 'react'
interface MenuProps {
  menuList: Array<RouterProps>
  activePath: string
}
export default React.memo(function Menu(props: MenuProps) {
  console.log('render menu')
  const { menuList, activePath } = props
  return (
    <div className={styles.menu_wrapper}>
      {menuList.map((item) => {
        return <MenuItem key={item.name} item={item} activePath={activePath} />
      })}
    </div>
  )
})
