import { checkEmployeeId, loginByCode } from '@/api/common'
import React, { useEffect } from 'react'
import { useDispatch } from 'react-redux'
import { useNavigate, useSearchParams } from 'react-router-dom'
import style from './index.module.scss'
import { message } from 'antd'

export default function Preview() {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const [searchParams, _] = useSearchParams()
  console.log(searchParams.get('code'))
  const checkLogin = async () => {
    const code = searchParams.get('code')
    if (code) {
      const res: any = await loginByCode({ code, module: 1 })
      const data = res
      try {
        const isExist = await checkEmployeeId({ employeeId: data.id })
        console.log('----------', isExist)
        if (!isExist) {
          message.error('该用户不存在')
          navigate('/notAuth')
          throw new Error('该客服不存在')
          return
        }
        data.permissions = [
          { redirect: '/chats', index: true },
          ...data.permissions
        ]
        localStorage.setItem('userInfo', JSON.stringify(data))
        localStorage.setItem('token', data.token)
        dispatch({
          type: 'login/changeUserInfoAction',
          payload: data
        })
        navigate('/chats')
      } catch (error: any) {
        // console.log(error)
        throw new Error(error)
      }
      // const data = {
      //   avatar:
      //     'https://s1-imfile.feishucdn.com/static-resource/v1/v2_940c590d-556c-4adc-8a4b-f60da49b7d7g~?image_size=72x72&cut_type=&quality=&format=image&sticker_format=.webp',
      //   email: '',
      //   id: 0,
      //   job: '',
      //   name: '',
      //   nickname: 'David',
      //   openId: '',
      //   token: 'token',
      //   permission: [
      //     {
      //       redirect: '/chats',
      //       index: true
      //     },
      //     {
      //       path: 'chats',
      //       name: 'Chats',
      //       component: '/Chats', //这里是pages下组件的路径
      //       isMenu: true,
      //       meta: {
      //         title: 'Chats',
      //         needLogin: true
      //       }
      //     },
    } else {
      //跳转到登录页面
      navigate('/login')
    }
  }
  useEffect(() => {
    checkLogin()
  }, [])
  return (
    <div className={style.mask}>
      <div className={style.loading}>
        {[1, 2, 3, 4, 5, 6].map((item) => (
          <div
            className={style.item}
            style={{ '--i': item } as React.CSSProperties}
            key={item}
          ></div>
        ))}
      </div>
    </div>
  )
}
