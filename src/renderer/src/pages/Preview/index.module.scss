.mask {
  background-color: #ededed;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  .loading {
    display: flex;
    align-items: center;
    .item {
      width: 5px;
      height: 50px;
      border-radius: 10px;
      background-color: #fff;
      margin: 0 5px;
      animation: loading 0.6s infinite;
      animation-delay: calc(0.1s * var(--i));
    }
  }
}
@keyframes loading {
  0% {
    height: 50px;
  }
  50% {
    height: 0;
  }
  100% {
    height: 50px;
  }
}
