import { Button, Modal } from 'antd'
import styles from './index.module.scss'
import { useNavigate } from 'react-router-dom'
const { confirm } = Modal

export default function NotAuth() {
  const navigate = useNavigate()
  const handleBack = () => {
    navigate('/login')
  }
  const handleCancel = () => {
    confirm({
      title: 'notice',
      content: 'Confirm to close ?',
      onOk() {
        localStorage.clear()
        window.ipcRenderer.send('quitApp')
      },
      onCancel() {
        console.log('Cancel')
      }
    })
  }
  return (
    <div className={styles.not_auth_wrapper}>
      <div className={styles.not_auth_content}>
        <div className={styles.not_auth_image}>
          <img
            src="https://resource.fs.com/mall/generalImg/20230721112052cx63o8.svg"
            alt=""
          />
        </div>
        <div className={styles.not_auth_desc}>
          <p><PERSON><PERSON> failed</p>
          <p>Your account is not bound to the system, please try again.</p>
        </div>
        <div className={styles.not_auth_btn}>
          <Button type="primary" onClick={handleBack}>
            Log back in
          </Button>
          <Button onClick={handleCancel}>Close</Button>
        </div>
      </div>
    </div>
  )
}
