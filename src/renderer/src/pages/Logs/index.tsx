import style from './index.module.scss'
import { Outlet, useLocation } from 'react-router-dom'
import NavBar from './components/Navbar/index'
import { getVersionList } from '@renderer/api/logs'
import { useContext, useEffect, useState } from 'react'
import LogsList from './components/LogsList/index'
import { GlobalContext } from '@renderer/context/globalContext'

function Logs() {
  const location = useLocation()
  const [versionList, setVersionList] = useState<VersionList[]>([])
  const [isAdmin, setIsAdmin] = useState<boolean>(false)
  const { updateApp } = useContext(GlobalContext)
  const { updateStatus, info } = updateApp
  console.log(updateApp)
  const fetchVersionList = async () => {
    const data: any = await getVersionList()
    setVersionList(data.records)
    setIsAdmin(data.isAdmin)
  }
  useEffect(() => {
    if (location.pathname.includes('logs')) {
      fetchVersionList()
    }
  }, [location.pathname])
  return (
    <div className={style.page_wrap}>
      {location.pathname.includes('editLogs') && <Outlet />}
      <div className={style.logs_list_wrap}>
        <NavBar
          list={versionList}
          updateInfo={{ updateStatus, lastVersion: info?.version }}
        ></NavBar>
        <LogsList
          list={versionList}
          isAdmin={isAdmin}
          updateInfo={{ updateStatus, lastVersion: info?.version }}
        ></LogsList>
      </div>
    </div>
  )
}

export default Logs
