import style from './index.module.scss'
import '@wangeditor/editor/dist/css/style.css' // 引入 css

import { useState, useEffect, ChangeEvent } from 'react'
import { Editor, Toolbar } from '@wangeditor/editor-for-react'
import { IDomEditor, IEditorConfig, IToolbarConfig } from '@wangeditor/editor'
import { uploadFile } from '@renderer/api/common'
import { Button, Input, Space, message } from 'antd'
import { useLocation, useNavigate } from 'react-router-dom'
import {
  addVersion,
  getVersionDetail,
  updateVersionDetail
} from '@renderer/api/logs'

type InsertFnType = (url: string, alt: string, href: string) => void
type VideoInsertFnType = (url: string, poster: string | '') => void
function MyEditor() {
  const [versionNum, setVersionNum] = useState<string>('')
  const navigate = useNavigate()
  const location = useLocation()

  // editor 实例
  const [editor, setEditor] = useState<IDomEditor | null>(null) // TS 语法
  // const [editor, setEditor] = useState(null)                   // JS 语法

  // 编辑器内容
  const [html, setHtml] = useState('')

  // 工具栏配置
  const toolbarConfig: Partial<IToolbarConfig> = {} // TS 语法
  // const toolbarConfig = { }                        // JS 语法

  // 编辑器配置
  const editorConfig: Partial<IEditorConfig> = {
    // TS 语法
    // const editorConfig = {                         // JS 语法
    placeholder: '请输入内容...',
    MENU_CONF: {
      // 菜单配置
      uploadImage: {
        fieldName: 'file',
        server: 'https://platform-gateway.whgxwl.com/livechat/upload-file',
        async customUpload(file: File, insertFn: InsertFnType) {
          const fd = new FormData()
          fd.append('file', file)
          const data: any = await uploadFile(fd)
          const url = data.url
          const alt = data.originalFilename
          const href = data.url
          // TS 语法
          // async customUpload(file, insertFn) {                   // JS 语法
          // file 即选中的文件
          // 自己实现上传，并得到图片 url alt href
          // 最后插入图片
          insertFn(url, alt, href)
        }
      },
      uploadVideo: {
        fieldName: 'file',
        server: 'https://platform-gateway.whgxwl.com/livechat/upload-file',
        async customUpload(file: File, insertFn: VideoInsertFnType) {
          console.log(file)
          const fd = new FormData()
          fd.append('file', file)
          const data: any = await uploadFile(fd)
          const url = data.url
          insertFn(url, '')
        }
      }
    }
  }
  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    setVersionNum(e.target.value)
  }
  const handleCancel = () => {
    navigate(-1)
  }
  const handleSubmit = async () => {
    if (!versionNum) {
      message.error('请输入版本号')
      return
    }
    if (location.state?.id > 0) {
      const params = {
        id: location.state?.id,
        version: versionNum,
        description: html
      }
      const data: any = await updateVersionDetail(params)
      console.log(data)
      if (data.result) {
        message.success('修改成功', 2).then(() => {
          navigate(-1)
        })
      } else {
        message.error(data.message)
      }
      return
    } else {
      const params = {
        version: versionNum,
        description: html
      }
      const data: any = await addVersion(params)
      if (data.result) {
        message.success('添加成功', 2).then(() => {
          navigate(-1)
        })
      } else {
        message.error(data.message)
      }
    }
  }
  const fetchVersionDetail = async (id: string) => {
    const data: any = await getVersionDetail(id)
    setHtml(data.chatVersionVO.description)
    setVersionNum(data.chatVersionVO.version)
  }
  useEffect(() => {
    console.log(location.state?.id)
    if (location.state?.id && location.state?.id !== 0) {
      fetchVersionDetail(location.state?.id)
    }
  }, [location.state])
  // 及时销毁 editor ，重要！
  useEffect(() => {
    return () => {
      if (editor == null) return
      editor.destroy()
      setEditor(null)
    }
  }, [editor])

  return (
    <div className={style.editor_wrap}>
      <div className={style.content_wrap}>
        <div className={style.title}>Update content editing</div>
        <div className={style.editor_box}>
          <Toolbar
            editor={editor}
            defaultConfig={toolbarConfig}
            mode="default"
            style={{ borderBottom: '1px solid #ccc' }}
          />
          <Editor
            defaultConfig={editorConfig}
            value={html}
            onCreated={setEditor}
            onChange={(editor) => setHtml(editor.getHtml())}
            mode="default"
            style={{ height: '500px', overflowY: 'hidden' }}
          />
        </div>
        <div className={style.input_wrap}>
          <div className={style.label}>Editing version:</div>
          <Input
            size="large"
            placeholder="输入版本号"
            style={{ width: '380px' }}
            value={versionNum}
            onChange={handleInputChange}
          />
        </div>
        {/* <div style={{ marginTop: '15px' }}>{html}</div> */}
      </div>
      <div className={style.form_footer}>
        <Space>
          <Button onClick={handleCancel}>Cancel</Button>
          <Button type="primary" onClick={handleSubmit}>
            Submit
          </Button>
        </Space>
      </div>
    </div>
  )
}

export default MyEditor
