import { But<PERSON>, Divider, <PERSON> } from 'antd'
import { useNavigate } from 'react-router-dom'
import style from './index.module.scss'
import { useEffect, useMemo, useRef, useState } from 'react'
import ImgPreview from '../../../../component/ImgPreview/index'
type LogsListProps = {
  list?: VersionList[]
  isAdmin?: boolean
  updateInfo?: {
    updateStatus: StatusType
    lastVersion: string
  }
}

function LogsList(props: LogsListProps) {
  const { list, isAdmin, updateInfo } = props
  const [visible, setVisible] = useState(false)
  const imgInfo = useRef({
    src: '',
    fileInfo: {
      fileUrl: '',
      fileName: ''
    }
  })
  const editorBox = useRef<HTMLDivElement>(null)
  const navigate = useNavigate()
  const editById = (id: number) => () => {
    if (id === 0) return navigate('/logs/editLogs')
    navigate('/logs/editLogs', { state: { id } })
  }
  const updateApp = () => {
    const ipcRenderer = window.ipcRenderer
    ipcRenderer.send('userUpdateApp')
  }
  const clickImg = (e) => {
    const target = e.target as HTMLImageElement
    if (target.tagName === 'IMG') {
      imgInfo.current.src = target.src
      imgInfo.current.fileInfo.fileUrl = target.src
      imgInfo.current.fileInfo.fileName = target.alt
      setVisible(true)
    }
  }
  const noLastVersion = useMemo(() => {
    if (updateInfo?.lastVersion) {
      const hasLastVersion = list?.some(
        (item) => item.version === updateInfo?.lastVersion
      )
      console.log(hasLastVersion)
      return !hasLastVersion
    } else {
      return false
    }
  }, [list])
  useEffect(() => {
    editorBox.current.addEventListener('click', clickImg)
    return () => {
      editorBox.current?.removeEventListener('click', clickImg)
    }
  }, [])
  return (
    <div className={style.logs_list_wrap}>
      {/* LogsList
      <Button onClick={() => navigate('/logs/editLogs')}>click</Button> */}
      <div className={style.content} ref={editorBox}>
        <div className={style.title}>
          <span>Software Update</span>
          {isAdmin && (
            <Button
              type="primary"
              size="large"
              onClick={() => navigate('/logs/editLogs', { state: { id: 0 } })}
            >
              Add New Version
            </Button>
          )}
        </div>
        {updateInfo?.updateStatus === 'hasUpdate' && (
          <div className={style.update_tip}>
            <span>Discover a new version：{updateInfo.lastVersion}</span>
            <Button type="primary" onClick={updateApp}>
              Update Now
            </Button>
          </div>
        )}
        <div className={style.logs_content_list} id="content">
          {noLastVersion && (
            <div className={style.logs_item}>
              <div className={style.version_title}>
                Update Details：v{updateInfo?.lastVersion}
                {isAdmin && (
                  <i
                    className="iconfont icon-bianjixinxi"
                    onClick={editById(0)}
                  ></i>
                )}
              </div>
              <div
                dangerouslySetInnerHTML={{
                  __html:
                    '<p>The update details are currently being edited. Please wait. You can upgrade the software now and check the update content later.</p><p>更新内容正在编辑中，请稍等。您可先升级软件，稍后查看更新内容</p>'
                }}
              ></div>
              <Divider style={{ color: '#E4E7ED' }}></Divider>
            </div>
          )}
          {list.map((item) => (
            <div className={style.logs_item} key={item.id} id={`${item.id}`}>
              <div className={style.version_title}>
                Update Details：v{item.version}
                {isAdmin && (
                  <i
                    className="iconfont icon-bianjixinxi"
                    onClick={editById(item.id)}
                  ></i>
                )}
              </div>
              <div dangerouslySetInnerHTML={{ __html: item.description }}></div>
              <Divider style={{ color: '#E4E7ED' }}></Divider>
            </div>
          ))}
        </div>
      </div>
      <ImgPreview
        src={imgInfo.current.src}
        visible={visible}
        setVisible={(value) => setVisible(value)}
        style={{ display: 'none' }}
      />
    </div>
  )
}

export default LogsList
