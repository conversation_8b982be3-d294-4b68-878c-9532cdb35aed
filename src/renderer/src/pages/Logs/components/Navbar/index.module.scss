.nav_wrap {
  width: 250px;
  height: 100%;
  background-color: #fff;

  .title {
    padding: 28px 0 28px 20px;
    @include font16;
    font-weight: 500;
  }

  .nav_list {
    display: flex;
    flex-direction: column;
    overflow: scroll;
    height: 100%;
    .nav_item {
      display: flex;
      justify-content: space-between;
      padding: 16px 20px;
      min-width: 120px;
      .version {
        @include font14;
      }

      .update_tag {
        display: flex;
        align-items: center;
        color: #ee433d;
        padding: 4px 8px;
        gap: 4px;
        background: rgba(255, 81, 75, 0.05);
        box-sizing: border-box;
        border: 1px solid rgba(255, 81, 75, 0.302);
      }

      cursor: pointer;

      a {
        color: #595959;
      }

      &:hover {
        background-color: #f5f5f5;
      }

      &.active {
        background-color: #f0f7ff;
      }
    }
  }
}
