import { useState } from 'react'
import style from './index.module.scss'

type NavBarProps = {
  list: VersionList[]
  updateInfo?: {
    updateStatus: StatusType
    lastVersion: string
  }
}

function NavBar(props: NavBarProps) {
  const { list, updateInfo } = props
  const [activity, setActivity] = useState<number>(0)

  const handlerClick = (e, id: number) => {
    setActivity(id)
    e.preventDefault()
    setTimeout(() => {
      const element = document.getElementById(`${id}`)
      if (element) element.scrollIntoView()
    }, 0)
  }

  return (
    <div className={style.nav_wrap}>
      <div className={style.title}>Update Log</div>
      <div className={style.nav_list}>
        {list.map((item) => (
          <div
            className={`${style.nav_item} ${
              activity === item.id ? style.active : ''
            }`}
            key={item.id}
            onClick={(e) => handlerClick(e, item.id)}
          >
            <span className={style.version}>V{item.version}</span>
            {updateInfo.updateStatus === 'hasUpdate' &&
              updateInfo.lastVersion === `${item.version}` && (
                <div className={style.update_tag}>
                  <i className="iconfont icon-huojian"></i>
                  <span>New Version</span>
                </div>
              )}
          </div>
        ))}
      </div>
    </div>
  )
}

export default NavBar
