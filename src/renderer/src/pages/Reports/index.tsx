import { lazy, Suspense, useState } from 'react'
import style from './index.module.scss'
import NavBar from './components/NavBar'
import { Spin } from 'antd'

const modules = import.meta.glob('../Reports/*/*.tsx')

const pageList = [
  {
    path: './TotalChats/index.tsx',
    element: lazy(modules['./TotalChats/index.tsx'] as any)
  },
  {
    path: './ChatSatisfaction/index.tsx',
    element: lazy(modules['./ChatSatisfaction/index.tsx'] as any)
  },
  {
    path: './ChatAvailability/index.tsx',
    element: lazy(modules['./ChatAvailability/index.tsx'] as any)
  },
  {
    path: './AgentActivity/index.tsx',
    element: lazy(modules['./AgentActivity/index.tsx'] as any)
  },
  {
    path: './ChatDuration/index.tsx',
    element: lazy(modules['./ChatDuration/index.tsx'] as any)
  },
  {
    path: './ChatResponseTimes/index.tsx',
    element: lazy(modules['./ChatResponseTimes/index.tsx'] as any)
  },
  {
    path: './ResolutionTime/index.tsx',
    element: lazy(modules['./ResolutionTime/index.tsx'] as any)
  },
  {
    path: './SolvedTickets/index.tsx',
    element: lazy(modules['./SolvedTickets/index.tsx'] as any)
  },
  {
    path: './TicketResponseTime/index.tsx',
    element: lazy(modules['./TicketResponseTime/index.tsx'] as any)
  }
]
const index = () => {
  const [activePage, setActivePage] = useState('./TotalChats/index.tsx')
  const getNavBarChange = (path: string) => {
    setActivePage(path)
  }
  return (
    <div className={style.report_wrap}>
      <NavBar getNavBarChange={getNavBarChange} />
      <div className={style.page_wrap}>
        <Suspense
          fallback={
            <div
              style={{
                width: '100%',
                height: '100%',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center'
              }}
            >
              <Spin spinning={true} />
            </div>
          }
        >
          {pageList.map(
            (item) =>
              activePage === item.path && <item.element key={item.path} />
          )}
        </Suspense>
      </div>
    </div>
  )
}

export default index
