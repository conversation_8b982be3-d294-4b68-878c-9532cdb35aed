import { But<PERSON>, Switch, Tooltip } from 'antd'
import FilterHeader from '../components/FilterHeader'
import style from './index.module.scss'
import FsAvatar from '@/component/FsAvatar'
import { exportChartsData, getAgentActivity } from '@renderer/api/report'
import { useEffect, useState } from 'react'
import { downloadCSVFile } from '@renderer/utils/util'

const timeList = ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
function AgentActivity() {
  const [filterData, setFilterData] = useState<AgentActivityParams>({})
  const [data, setData] = useState<any>([])
  const fetchAgentActivity = async () => {
    const params = {
      ...filterData,
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
    }
    const data: any = await getAgentActivity(params)
    setData(data)
  }

  const onFilterChange = (params: AgentActivityParams) => {
    setFilterData(params)
  }
  const downloadCSV = async () => {
    const path = 'agent-activity-count'
    const response: any = await exportChartsData(path, filterData)
    downloadCSVFile(response.data, path)
  }

  const hideAgents = () => {}

  useEffect(() => {
    fetchAgentActivity()
  }, [filterData])
  return (
    <div className={style.agent_activity_wrap}>
      <FilterHeader
        type={'agentActivity'}
        onFilterChange={onFilterChange}
      ></FilterHeader>
      <div className={style.content_wrap}>
        <div className={style.base_info_card}>
          <div className={style.common_wrap}>
            <div className={style.title}>
              <div className={style.title_right}>
                <span>Agent activity</span>
                <Tooltip
                  title={
                    'See when your agents were available for a chat during a given day. Keep in mind, that agents without any activity are hidden by default.'
                  }
                  placement="bottomLeft"
                  arrow
                >
                  <i className="icon-tishi iconfont"></i>
                </Tooltip>
              </div>
              <Button
                type="primary"
                ghost
                icon={
                  <i
                    className="icon-xiazai iconfont"
                    style={{ marginRight: 4 }}
                  ></i>
                }
                onClick={downloadCSV}
              >
                Export CSV
              </Button>
            </div>
          </div>
          <div className={style.common_wrap}>
            <div className={style.chart_wrap}>
              {/* 表头 */}
              <div className={style.chart_title}>
                <div className={style.filter}>
                  {/* <Switch size="small" onChange={hideAgents} />
                  HIDE INACTIVE AGENTS */}
                </div>
                <div className={style.time_day}>
                  {timeList.map((time, index) => (
                    <div className={style.time_item} key={index}>
                      {time}
                    </div>
                  ))}
                </div>
              </div>
              {/* 内容 */}
              <div className={style.chart_content}>
                {data.length > 0 &&
                  data?.map((item, index) => (
                    <div className={style.user_wrap} key={index}>
                      <div className={style.user_item}>
                        <FsAvatar
                          src={item.avatar}
                          style={{
                            width: '36px',
                            height: '36px',
                            flexShrink: '0'
                          }}
                        ></FsAvatar>
                        <div className={style.info}>
                          <p>{item.name}</p>
                          {/* <span><EMAIL></span> */}
                        </div>
                      </div>
                      <div className={style.user_activity_wrap}>
                        {item?.agentActivityVOS.map((time, index) => (
                          <div
                            className={`${style.activity_chart} ${
                              time.status === 3
                                ? style.logged
                                : time.status === 1
                                ? style.accept
                                : style.no_accept
                            }`}
                            key={index}
                            style={{
                              width: `${(time.ratioTime * 100).toFixed(2)}%`
                            }}
                          >
                            <Tooltip
                              getPopupContainer={(triggerNode) =>
                                triggerNode.parentNode as HTMLElement
                              }
                              color="#fff"
                              placement="top"
                              title={
                                <div className={style.tooltip_warp}>
                                  <div className={style.top}>
                                    {`${time.startTime} - ${time.endTime}`}
                                  </div>
                                  <div className={style.bottom}>
                                    {`${
                                      time.status === 3
                                        ? 'Logged out:'
                                        : time.status === 1
                                        ? 'Accepting chats:'
                                        : 'Not accepting chats:'
                                    } ${time.totalTime}`}
                                  </div>
                                </div>
                              }
                            >
                              <div className={style.tooltip_content}></div>
                            </Tooltip>
                          </div>
                        ))}
                        <div className={style.legend}>
                          <div className={style.legend_item}>
                            <i
                              className={[style.legend_icon, style.accept].join(
                                ' '
                              )}
                            ></i>
                            <span className={style.text}>
                              Accepting chats:{item.acceptingTime}
                            </span>
                          </div>
                          <div className={style.legend_item}>
                            <i
                              className={[
                                style.legend_icon,
                                style.not_accept
                              ].join(' ')}
                            ></i>
                            <span className={style.text}>
                              Not accepting chats:{item.notAcceptingTime}
                            </span>
                          </div>
                          <div className={style.legend_item}>
                            <i
                              className={[style.legend_icon, style.logged].join(
                                ' '
                              )}
                            ></i>
                            <span className={style.text}>
                              Logged out:{item.loggedOutTime}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AgentActivity
