@import '../mixin.scss';

.agent_activity_wrap {
  @include wrap_common;

  .content_wrap {
    flex: 1;

    .base_info_card {
      @include base_info_card;

      .common_wrap {
        @include common_wrap;

        .title {
          @include title;
        }

        .chart_wrap {
          display: flex;
          flex-direction: column;
          margin: 0 -20px;
          .chart_title {
            display: flex;
            height: 60px;
            margin: 0 20px;
            justify-content: center;
            align-content: center;
            border-bottom: 1px solid #eeeeee;

            .filter {
              width: 120px;
              text-align: end;
              line-height: 62px;
              // border-right: 1px solid #eeeeee;
              font-size: 12px;
              padding-right: 8px;
            }

            .time_day {
              flex: 1;
              display: flex;

              .time_item {
                flex: 1;
                text-align: center;
                line-height: 62px;
                &:first-child {
                  text-align: start;
                }
                &:last-child {
                  text-align: end;
                }
              }
            }
          }

          .chart_content {
            height: calc(100vh - 80px);
            overflow-y: auto;

            .user_wrap {
              height: 114px;
              display: flex;
              align-items: center;
              border-bottom: 1px solid #eeeeee;
              padding: 0 20px;
              .user_item {
                width: 120px;
                display: flex;
                align-items: center;
                // padding: 0 20px 0 20px;
                .info {
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  margin-left: 8px;
                  p {
                    @include font12;
                    margin-right: 4px;
                  }
                  span {
                    @include font12;
                    color: #8c8c8c;
                  }
                }
              }

              .user_activity_wrap {
                flex: 1;
                height: 100%;
                display: flex;
                align-items: center;
                position: relative;
                .activity_chart {
                  height: 8px;
                  .tooltip_content {
                    height: 100%;
                  }
                  &.logged {
                    background-color: #abb9ce;
                  }

                  &.accept {
                    background: #48c43d;
                  }

                  &.no_accept {
                    background: #ee433d;
                  }
                  .tooltip_warp {
                    color: #000;
                    text-align: center;
                    .top {
                      padding: 8px 0;
                      @include font13;
                      // background-color: #FAFAFA;
                    }
                    .bottom {
                      padding: 8px 0;
                      @include font13;
                      color: #595959;
                    }
                  }
                }

                .legend {
                  position: absolute;
                  left: 50%;
                  transform: translate(-50%, 0);
                  bottom: 8px;
                  display: flex;
                  align-items: center;
                  gap: 20px;
                  .legend_item {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    .legend_icon {
                      flex-shrink: 0;
                      width: 8px;
                      height: 8px;
                      border-radius: 50%;
                      &.accept {
                        background-color: #48c43d;
                      }
                      &.not_accept {
                        background-color: #ee433d;
                      }
                      &.logged {
                        background-color: #abb9ce;
                      }
                    }
                    .text {
                      @include font12;
                      white-space: nowrap;
                    }
                  }
                }
              }
            }
          }

          .chart_legend {
            height: 62px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            padding-right: 20px;
            margin-bottom: -20px;

            .legend_item {
              &:first-child {
                margin-right: 32px;
              }

              .outer {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                background: #00b42a;
                position: relative;
                display: inline-block;
                margin-right: 8px;

                .inner {
                  position: absolute;
                  top: 3px;
                  left: 3px;
                  width: 6px;
                  height: 6px;
                  border-radius: 50%;
                  background: #fff;
                }

                &.g {
                  background: #fdaa01;
                }
              }
            }
          }
        }
      }
    }
  }
}
