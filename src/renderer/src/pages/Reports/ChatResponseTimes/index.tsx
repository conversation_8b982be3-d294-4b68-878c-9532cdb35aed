import style from './index.module.scss'
import FilterHeader from '../components/FilterHeader'
import { Tooltip } from 'antd'
import DiffTypeChart from '../components/DiffTypeChart/index'
import { exportChartsData, getFirstResponseTime } from '@renderer/api/report'
import { useEffect, useState } from 'react'
import { DualAxesConfig } from '@ant-design/plots'
import { downloadCSVFile, formatSeconds } from '@renderer/utils/util'
const ChatResponseTimes = () => {
  const [emptyStatus, setEmptyStatus] = useState(false)
  const [filterData, setFilterData] = useState<ReportDataParams>({})
  const [legendData, setLegendData] = useState([])
  const [tableData, setTableData] = useState<any>([])
  const [chartConfig, setChartConfig] = useState<DualAxesConfig>({
    data: [[], []],
    xField: 'timerShaft',
    yField: ['time', 'preTime'],
    yAxis: {
      time: {
        label: {
          formatter: (val: string, item: any) => {
            return formatSeconds(Number(val))
          }
        },
        tickCount: 5
      },
      preTime: {
        label: {
          formatter: (val: string, item: any) => {
            return formatSeconds(Number(val))
          }
        },
        tickCount: 5
      }
    },
    geometryOptions: [
      {
        geometry: 'line',
        color: '#4B7EFF'
      },
      {
        geometry: 'line',
        color: '#EAD83C'
      }
    ],
    tooltip: {
      formatter: (datum) => {
        if (datum?.time !== undefined) {
          return { name: 'time', value: formatSeconds(datum.time) }
        } else {
          return { name: 'preTime', value: formatSeconds(datum.preTime) }
        }
      }
    },
    legend: {
      itemName: {
        formatter: (text: string) => {
          return text === 'time' ? 'First time' : 'Previous period first time'
        }
      }
    }
  })
  const fetchFirstResponseTime = async () => {
    const data: any = await getFirstResponseTime(filterData)
    if (!data.firstResponseTimeResList.length) {
      return setEmptyStatus(true)
    } else {
      setEmptyStatus(false)
    }
    setLegendData([
      {
        title: data.days,
        total: data.firstResponseTimeFormat
      },
      {
        title: data.preDays,
        total: data.preFirstResponseTimeFormat
      }
    ])
    setChartConfig({
      ...chartConfig,
      data: [data.firstResponseTimeResList, data.firstResponseTimeResList]
    })
    setTableData(data.firstResponseTimeResList)
  }
  const onFilterChange = (params: ReportDataParams) => {
    setFilterData(params)
  }
  const downloadCSV = async () => {
    const path = 'chat/response-time'
    const res: any = await exportChartsData(path, filterData)
    downloadCSVFile(res.data, path)
  }
  useEffect(() => {
    fetchFirstResponseTime()
  }, [filterData])
  return (
    <div className={style.chat_responese_wrap}>
      <FilterHeader onFilterChange={onFilterChange}></FilterHeader>
      <div className={style.content_wrap}>
        <div className={style.base_info_card}>
          <div className={style.common_wrap}>
            <div className={style.title}>
              <div className={style.title_right}>
                <span>First response time</span>
                <Tooltip
                  title={
                    <div>
                      This chart shows how much time on average it takes an
                      agent to reply to the first visitor message. In case of
                      transferred chats it's counted from the moment an agent
                      gets assigned to the chat.
                    </div>
                  }
                  placement="bottomLeft"
                  arrow
                >
                  <i className="icon-tishi iconfont"></i>
                </Tooltip>
              </div>
            </div>
            <DiffTypeChart
              legendData={legendData}
              chartConfig={chartConfig}
              tableData={tableData}
              tableTitle="First response time"
              downloadCSV={downloadCSV}
              emptyStatus={emptyStatus}
            ></DiffTypeChart>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ChatResponseTimes
