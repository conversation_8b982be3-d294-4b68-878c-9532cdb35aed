import style from './index.module.scss'
import FilterHeader from '../components/FilterHeader'
import { Tooltip } from 'antd'
import DiffTypeChart from '../components/DiffTypeChart/index'
import { exportChartsData, getSyncTickets } from '@renderer/api/report'
import { useEffect, useState } from 'react'
import { DualAxesConfig } from '@ant-design/plots'
import { downloadCSVFile } from '@renderer/utils/util'
const SolvedTickets = () => {
  const [filterData, setFilterData] = useState<ReportDataParams>({})
  const [legendData, setLegendData] = useState([])
  const [tableData, setTableData] = useState<any>([])
  const [chartConfig, setChartConfig] = useState<DualAxesConfig>({
    data: [[], []],
    xField: 'timerShaft',
    yField: ['total', 'preTotal'],
    geometryOptions: [
      {
        geometry: 'line',
        color: '#4B7EFF'
      },
      {
        geometry: 'line',
        color: '#EAD83C'
      }
    ],
    legend: {
      itemName: {
        formatter: (text: string) => {
          return text === 'total' ? 'First time' : 'Previous period first time'
        }
      }
    }
  })
  const fetchSyncTickets = async () => {
    const data: any = await getSyncTickets(filterData)
    setLegendData([
      {
        title: data.days,
        total: data.total
      },
      {
        title: data.preDays,
        total: data.preTotal
      }
    ])
    setChartConfig({
      ...chartConfig,
      data: [data.list, data.list]
    })
    setTableData(data.list)
  }
  const onFilterChange = (params: ReportDataParams) => {
    setFilterData(params)
  }

  const downloadCSV = async () => {
    const path = 'sync-tickets'
    const res: any = await exportChartsData(path, filterData)
    downloadCSVFile(res.data, path)
  }
  useEffect(() => {
    fetchSyncTickets()
  }, [filterData])
  return (
    <div className={style.chat_responese_wrap}>
      <FilterHeader onFilterChange={onFilterChange}></FilterHeader>
      <div className={style.content_wrap}>
        <div className={style.base_info_card}>
          <div className={style.common_wrap}>
            <div className={style.title}>
              <div className={style.title_right}>
                <span>Solved tickets</span>
                <Tooltip
                  title={
                    'The number of tickets solved by agents in a given time-frame.'
                  }
                  placement="bottomLeft"
                  arrow
                >
                  <i className="icon-tishi iconfont"></i>
                </Tooltip>
              </div>
            </div>
            <DiffTypeChart
              legendData={legendData}
              chartConfig={chartConfig}
              tableData={tableData}
              tableTitle="Solved tickets"
              downloadCSV={downloadCSV}
            ></DiffTypeChart>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SolvedTickets
