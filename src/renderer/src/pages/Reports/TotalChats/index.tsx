import style from './index.module.scss'

import FilterHeader from '../components/FilterHeader'

import TotalChatsLine from './components/TotalChatsLine'
import TotalChatsHeatmap from './components/TotalChatsHeatmap'
import { useState } from 'react'

const TotalChats = () => {
  const [filterData, setFilterData] = useState<ReportDataParams>({
    customerServiceId: undefined,
    siteId: undefined,
    startTime: undefined,
    endTime: undefined
  })
  const onFilterChange = (params: ReportDataParams) => {
    setFilterData(params)
  }
  return (
    <div className={style.total_chat_wrap}>
      <FilterHeader onFilterChange={onFilterChange}></FilterHeader>
      <div className={style.content_wrap}>
        <TotalChatsLine filterData={filterData} />
        {/* 下半部分 */}
        <TotalChatsHeatmap filterData={filterData} />
      </div>
    </div>
  )
}

export default TotalChats
