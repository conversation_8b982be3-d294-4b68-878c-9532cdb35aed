import { useEffect, useState } from 'react'
import { <PERSON><PERSON><PERSON>, But<PERSON> } from 'antd'

import { Heatmap, HeatmapConfig } from '@ant-design/plots'

import styles from './index.module.scss'
import { exportChartsData, getTotalHeatmap } from '@renderer/api/report'
import { downloadCSVFile } from '@renderer/utils/util'
import Empty from '@renderer/component/Empty'
const toolTipString = () => (
  <div>
    This chart shows you an average amount of time it takes until a client
    receives the first answer to a ticket created via email or ticket form.
  </div>
)

type TotalChatsHeatmapProps = {
  filterData: ReportDataParams
}

type Data = {
  date: string
  hours: string
  value: number
}

const TotalChatsHeatmap = (props: TotalChatsHeatmapProps) => {
  const { filterData } = props
  const [data, setData] = useState<Data[]>([])
  const [emptyStatus, setEmptyStatus] = useState(false)
  const config: HeatmapConfig = {
    data,
    xField: 'date',
    yField: 'hours',
    autoFit: true,
    height: 500,
    colorField: 'value',
    sizeField: 'value',
    reflect: 'x',
    shape: 'boundary-polygon',
    color: ['#EEF3FD', '#BCCEF5', '#7395EC', '#4E80FF'],
    tooltip: {
      title: 'time',
      showMarkers: false,
      customContent: (title: string, data: any) => {
        if (data.length && title) {
          return (
            <div>
              <p
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px',
                  padding: '10px 0'
                }}
              >
                <span>{data[0].data.date}</span>
                <span>{data[0].data.time}</span>
              </p>
              <p style={{ paddingBottom: '10px' }}>
                Total chats: {data[0].value}
              </p>
            </div>
          )
        }
      }
    },
    interactions: [
      {
        type: 'element-active'
      }
    ],
    yAxis: {
      label: {
        formatter: (val: string) => {
          // 每过3小时，显示一次
          if (parseInt(val) % 3 === 0) {
            return val
          }
        }
      }
    },
    xAxis: {
      label: {
        formatter: (val: string) => {
          // 只显示每月1，5，9，13，17，21，25，29，31
          if (
            parseInt(val) === 1 ||
            parseInt(val) === 5 ||
            parseInt(val) === 9 ||
            parseInt(val) === 13 ||
            parseInt(val) === 17 ||
            parseInt(val) === 21 ||
            parseInt(val) === 25 ||
            parseInt(val) === 29 ||
            parseInt(val) === 31
          ) {
            return val
          }
        }
      }
    },
    heatmapStyle: {
      stroke: '#fff',
      lineWidth: 4
    },
    legend: {
      layout: 'horizontal',
      position: 'bottom-right'
    }
  }
  const fetchTotalHeatmapData = async () => {
    const data: any = await getTotalHeatmap(filterData)
    data.length === 0 ? setEmptyStatus(true) : setEmptyStatus(false)
    setData(data)
  }
  const downloadTotalHeatmapCSV = async () => {
    const path = 'total-charts/heatmap'
    const response: any = await exportChartsData(path, filterData)
    downloadCSVFile(response.data, path)
  }
  useEffect(() => {
    fetchTotalHeatmapData()
  }, [filterData])
  return (
    <div className={styles.base_info_card}>
      <div className={styles.common_wrap}>
        <div className={styles.title}>
          <div className={styles.title_right}>
            <span>Total chats heatmap</span>
            <Tooltip title={toolTipString} placement="bottomLeft" arrow>
              <i className="icon-tishi iconfont"></i>
            </Tooltip>
          </div>
          <Button
            type="primary"
            ghost
            icon={
              <i
                className="icon-xiazai iconfont"
                style={{ marginRight: 4 }}
              ></i>
            }
            onClick={downloadTotalHeatmapCSV}
          >
            Export CSV
          </Button>
        </div>
        {/* eslint-disable-next-line @typescript-eslint/ban-ts-comment */}
        {/* @ts-ignore */}
        {emptyStatus ? <Empty /> : <Heatmap {...config} />}
      </div>
    </div>
  )
}

export default TotalChatsHeatmap
