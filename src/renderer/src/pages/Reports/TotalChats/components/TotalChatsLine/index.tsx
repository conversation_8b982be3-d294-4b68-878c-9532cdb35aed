import { useEffect, useState } from 'react'
import { Switch, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Button } from 'antd'
import { Line } from '@ant-design/plots'

import styles from './index.module.scss'
import { exportChartsData, getTotalCharts } from '@renderer/api/report'
import { downloadCSVFile } from '@renderer/utils/util'
import Empty from '@renderer/component/Empty'
type TotalChatsLineProps = {
  filterData: ReportDataParams
}

const toolTipString = () => (
  <div>
    <p>This chart shows the number of all chats.</p>
    <p>RECOMMENDATION</p>
    <p>
      Visitors who chat are more likely to convert into customers. See how to
      engage with visitors and get more chats.
    </p>
  </div>
)

const TotalChatsLine = (props: TotalChatsLineProps) => {
  const { filterData } = props
  const [data, setData] = useState([])
  const [totalNum, setTotalNum] = useState(0)
  const [emptyStatus, setEmptyStatus] = useState(false)
  const config = {
    data,
    xField: 'days',
    yField: 'total',
    xAxis: {
      // type: 'timeCat',
      tickCount: 5
    },
    smooth: true,
    point: {
      size: 5,
      style: {
        fill: 'white',
        stroke: '#5B8FF9',
        lineWidth: 2
      }
    }
  }
  const fetchTotalChats = async () => {
    const data: any = await getTotalCharts(filterData)
    data.lineChartList.length === 0
      ? setEmptyStatus(true)
      : setEmptyStatus(false)
    setData(data.lineChartList)
    setTotalNum(data.total)
  }

  const downloadTotalLineCSV = async () => {
    const path = 'total-charts/line-chart'
    const response: any = await exportChartsData(path, filterData)
    downloadCSVFile(response.data, path)
  }
  useEffect(() => {
    fetchTotalChats()
  }, [filterData])
  return (
    <div className={styles.base_info_card}>
      <div className={styles.common_wrap}>
        <div className={styles.title}>
          <div className={styles.title_right}>
            <span>Total chats</span>
            <Tooltip title={toolTipString} placement="bottomLeft" arrow>
              <i className="icon-tishi iconfont"></i>
            </Tooltip>
          </div>
          {/* <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            24-hour distrubution: <Switch onChange={handleChange} />
          </span> */}
        </div>
        <div className={styles.total_num}>
          <Statistic value={totalNum} />
        </div>
        {/* eslint-disable-next-line @typescript-eslint/ban-ts-comment */}
        {/* @ts-ignore */}
        {emptyStatus ? <Empty /> : <Line {...config} />}
      </div>
      {emptyStatus ? null : (
        <div className={styles.common_wrap}>
          <div className={styles.title}>
            <div className={styles.title_right}>
              <span>Total chats breakdown</span>
            </div>
            <Button
              type="primary"
              ghost
              icon={
                <i
                  className="icon-xiazai iconfont"
                  style={{ marginRight: 4 }}
                ></i>
              }
              onClick={downloadTotalLineCSV}
            >
              Export CSV
            </Button>
          </div>
          <div className={styles.my_table}>
            <table>
              <tbody>
                <tr>
                  <th>Series</th>
                  {data.map((item, index) => (
                    <td key={index}>{item.days}</td>
                  ))}
                </tr>
                <tr>
                  <th>Total</th>
                  {data.map((item, index) => (
                    <td key={index}>{item.total}</td>
                  ))}
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  )
}

export default TotalChatsLine
