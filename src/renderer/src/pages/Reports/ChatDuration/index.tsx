import { useEffect, useState } from 'react'
import style from './index.module.scss'
import FilterHeader from '../components/FilterHeader'
import { Button, Tooltip } from 'antd'
import { Line } from '@ant-design/plots'
import { exportChartsData, getChatDuration } from '@renderer/api/report'
import { downloadCSVFile } from '@renderer/utils/util'
import Empty from '@renderer/component/Empty'

//以秒为单位的时间，返回以分秒为单位的字符串，不满一分钟的不显示分钟
function formatTime(time: number) {
  const minute = Math.floor(time / 60)
  const second = Math.floor(time % 60)
  if (!minute && !second) return '0s'
  return `${minute ? minute + 'm' : ''}${second ? second + 's' : ''}`
}

const ChatDuration = () => {
  const [emptyStatus, setEmptyStatus] = useState(false)
  const [filterData, setFilterData] = useState<ReportDataParams>({})
  const [data, setData] = useState<any>([])
  const [tableData, setTableData] = useState<any>([])
  const [timeText, setTimeText] = useState({
    averageAgentTime: '',
    averageBotsTime: ''
  })
  const config = {
    data: data,
    xField: 'days',
    yField: 'times',
    seriesField: 'series',
    yAxis: {
      label: {
        formatter: (val: string) => {
          return formatTime(parseInt(val))
        }
      }
    },
    tooltip: {
      formatter: (datum: any) => {
        return { name: datum.series, value: formatTime(parseInt(datum.times)) }
      }
    },
    color: ['#4B7EFF', '#EAD83C']
  }
  const fetchChatDuration = async () => {
    const data: any = await getChatDuration(filterData)
    if (!data.agentTimeList.length && !data.botsTimeList.length) {
      setEmptyStatus(true)
      return
    } else {
      setEmptyStatus(false)
    }
    setTableData(data)
    data.agentTimeList.forEach((item) => {
      item.series = 'agent'
    })
    data.botsTimeList.forEach((item) => {
      item.series = 'bots'
    })
    setTimeText({
      averageAgentTime: data.averageAgentTime,
      averageBotsTime: data.averageBotsTime
    })
    setData([...data.agentTimeList, ...data.botsTimeList])
  }

  const onFilterChange = (params: ReportDataParams) => {
    setFilterData(params)
  }

  const downloadCSV = async () => {
    const path = 'chat-duration'
    const response: any = await exportChartsData(path, filterData)
    downloadCSVFile(response.data, path)
  }
  useEffect(() => {
    fetchChatDuration()
  }, [filterData])
  return (
    <div className={style.chat_duration_wrap}>
      <FilterHeader onFilterChange={onFilterChange}></FilterHeader>
      <div className={style.content_wrap}>
        <div className={style.base_info_card}>
          <div className={style.common_wrap}>
            <div className={style.title}>
              <div className={style.title_right}>
                <span>Average chat duration</span>
                <Tooltip
                  title={
                    <>
                      <div>
                        This chart compares agent chatting time with the entire
                        chat duration.
                      </div>
                      <div>
                        Agent chatting time – shows the amount of time an agent
                        spends chatting with customers.
                      </div>
                      <div>
                        Bot's chatting time– Shows how long the bot has been
                        chatting with the customer.
                      </div>
                    </>
                  }
                  placement="bottomLeft"
                  arrow
                >
                  <i className="icon-tishi iconfont"></i>
                </Tooltip>
              </div>
              {/* <span>
                24-hour distrubution: <Switch />
              </span> */}
            </div>
            <div className={style.rate_wrap}>
              <div className={[style.rate_item, style.good].join(' ')}>
                <span className={style.rate_icon}></span>
                <div className={style.info_box}>
                  <div className={style.title_}>Agent chatting time</div>
                  <div className={style.num_box}>
                    <span className={style.total}>
                      {timeText.averageAgentTime}
                    </span>
                  </div>
                </div>
              </div>
              <div className={[style.rate_item, style.bad].join(' ')}>
                <span className={style.rate_icon}></span>
                <div className={style.info_box}>
                  <div className={style.title_}>Bot's chatting time</div>
                  <div className={style.num_box}>
                    <span className={style.total}>
                      {timeText.averageBotsTime}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            {emptyStatus ? <Empty /> : <Line {...config}></Line>}
          </div>
          {emptyStatus ? null : (
            <div className={style.common_wrap}>
              <div className={style.title}>
                <div className={style.title_right}>
                  <span>Chat satisfaction breakdown</span>
                </div>
                <Button
                  type="primary"
                  ghost
                  icon={
                    <i
                      className="icon-xiazai iconfont"
                      style={{ marginRight: 4 }}
                    ></i>
                  }
                  onClick={downloadCSV}
                >
                  Export CSV
                </Button>
              </div>
              <div className={style.my_table}>
                <table>
                  <tbody>
                    <tr>
                      <th>Series</th>
                      {tableData?.agentTimeList?.map((item, index) => (
                        <td key={index}>{item.days}</td>
                      ))}
                    </tr>
                    <tr>
                      <th>Agent chatting time</th>
                      {tableData?.agentTimeList?.map((item, index) => (
                        <td key={index}>{item.timesFormat}</td>
                      ))}
                    </tr>
                    <tr>
                      <th>Bot's chatting time</th>
                      {tableData?.botsTimeList?.map((item, index) => (
                        <td key={index}>{item.timesFormat}</td>
                      ))}
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ChatDuration
