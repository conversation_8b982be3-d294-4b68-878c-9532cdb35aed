@import '../mixin.scss';

.chat_duration_wrap {
  @include wrap_common;
  .content_wrap {
    flex: 1;
    .base_info_card {
      @include base_info_card;
      .common_wrap {
        @include common_wrap;
        .title {
          @include title;
        }
        .total_num {
          margin: 26px 0 20px;
        }
        .my_table {
          @include table;
        }
        .rate_wrap {
          display: flex;
          align-items: center;
          margin-bottom: 20px;
          .rate_item {
            display: flex;
            align-items: baseline;
            justify-content: center;
            .rate_icon {
              width: 8px;
              height: 8px;
            }
            .info_box {
              margin-left: 4px;
              .title_ {
                font-size: 12px;
                line-height: 20px;
                color: #434343;
              }
              .desc {
                font-size: 12px;
                line-height: 20px;
                color: #8C8C8C;
                margin-bottom: 4px;
              }
              .num_box {
                display: flex;
                .total {
                  font-size: 18px;
                  font-weight: 500;
                  line-height: 26px;
                  color: #262626;
                }
                .percent {
                  font-size: 10px;
                  line-height: 10px;
                  margin-left: 4px;
                  // transform: scale(0.8, 0.8);
                }
              }
            }
            &.good {
              margin-right: 98px;
              .rate_icon {
                background-color: #4B7EFF;
              }
            }
            &.bad {
              .rate_icon {
                background-color: #EAD83C;
              }
            }
          }
        }
      }
    }
  }
}