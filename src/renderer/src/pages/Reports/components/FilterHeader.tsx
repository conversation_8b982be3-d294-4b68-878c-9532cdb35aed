import { getFilterItem, getManageList } from '@renderer/api/report'
import { DatePicker, Input, Select, Space } from 'antd'
import { useEffect, useRef, useState } from 'react'

type FilterHeaderProps = {
  onFilterChange?: (params: ReportDataParams) => void
  type?: 'default' | 'agentActivity'
}

const FilterHeader = (props: FilterHeaderProps) => {
  const { onFilterChange, type } = props
  const [assigneeList, setAssigneeList] = useState([])
  const [siteList, setSiteList] = useState([])
  const defaultParams = useRef<ReportDataParams & AgentActivityParams>({
    customerServiceId: undefined,
    siteId: undefined,
    startTime: undefined,
    endTime: undefined,
    day: undefined
  })

  const fetchFilterItem = async () => {
    if (type === 'agentActivity') {
      const data: any = await getManageList()
      setAssigneeList((pre) => {
        return data.listAssignee.map((item: any) => {
          return {
            label: item.name,
            value: item.id
          }
        })
      })
    } else {
      const data: any = await getFilterItem()
      setAssigneeList((pre) => {
        return data.listAssignee.map((item: any) => {
          return {
            label: item.name,
            value: item.id
          }
        })
      })
      setSiteList((pre) => {
        return data.listSite.map((item: any) => {
          return {
            label: item.name,
            value: item.id
          }
        })
      })
    }
  }
  const handleAssigneeChange = (value: number) => {
    defaultParams.current.customerServiceId = value
    onFilterChange({
      ...defaultParams.current
    })
  }
  const handleSiteChange = (value: number) => {
    defaultParams.current.siteId = value
    onFilterChange({
      ...defaultParams.current
    })
  }
  const handleDateChange = (time: any, timeString: [string, string]) => {
    defaultParams.current.startTime = timeString[0]
    defaultParams.current.endTime = timeString[1]
    onFilterChange({
      ...defaultParams.current
    })
  }
  const handleSingeDate = (time: any, timeString: string) => {
    defaultParams.current.day = timeString
    onFilterChange({
      ...defaultParams.current
    })
  }
  useEffect(() => {
    fetchFilterItem()
  }, [])

  return (
    <div style={{ marginBottom: 20 }}>
      <Space>
        <Select
          allowClear
          showSearch
          style={{ width: 150 }}
          size="large"
          placeholder="All Agent"
          options={assigneeList}
          filterOption={(input, option) => {
            return (
              option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0
            )
          }}
          onChange={handleAssigneeChange}
        />
        {type !== 'agentActivity' && (
          <Select
            allowClear
            style={{ width: 150 }}
            size="large"
            placeholder="All site"
            options={siteList}
            onChange={handleSiteChange}
          />
        )}
        <>
          {type === 'agentActivity' ? (
            <DatePicker onChange={handleSingeDate} size="large" />
          ) : (
            <DatePicker.RangePicker size="large" onChange={handleDateChange} />
          )}
        </>
      </Space>
    </div>
  )
}

export default FilterHeader
