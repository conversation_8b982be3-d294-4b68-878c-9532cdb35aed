import { useState } from 'react'
import style from '../index.module.scss'

const navList = [
  {
    name: 'Total Chats',
    path: './TotalChats/index.tsx'
  },
  {
    name: 'Chat satisfaction',
    path: './ChatSatisfaction/index.tsx'
  },
  {
    name: 'Chat availability',
    path: './ChatAvailability/index.tsx'
  },
  {
    name: 'Chat duration',
    path: './ChatDuration/index.tsx'
  },
  {
    name: 'Chat response times',
    path: './ChatResponseTimes/index.tsx'
  },
  {
    name: 'Agent activity',
    path: './AgentActivity/index.tsx'
  },
  {
    name: 'Ticket First response time',
    path: './TicketResponseTime/index.tsx'
  },
  {
    name: 'Synchronized tickets',
    path: './SolvedTickets/index.tsx'
  },
  {
    name: 'Synchronized time',
    path: './ResolutionTime/index.tsx'
  }
]

type PropsType = {
  getNavBarChange: (path: string) => void
}
const NavBar = (props: PropsType) => {
  const [active, setActive] = useState('./TotalChats/index.tsx')
  const { getNavBarChange } = props
  const handlerNavChange = (path: string) => {
    getNavBarChange(path)
    setActive(path)
  }
  return (
    <div className={style.nav_bar_wrap}>
      <div className={style.nav_bar_content}>
        <div className={style.nav_list}>
          {navList.map((item) => (
            <div
              className={[
                style['list_item'],
                active === item.path ? style['active_list'] : ''
              ].join(' ')}
              key={item.path}
              onClick={() => handlerNavChange(item.path)}
            >
              <div>{item.name}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default NavBar
