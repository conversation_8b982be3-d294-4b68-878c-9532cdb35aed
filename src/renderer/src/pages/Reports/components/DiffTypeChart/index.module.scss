@import '../../mixin.scss';

.legend_wrap {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  .legend_item {
    display: flex;
    align-items: baseline;
    justify-content: center;

    .legend_icon {
      width: 8px;
      height: 8px;
    }

    .info_box {
      margin-left: 4px;

      .title_ {
        font-size: 12px;
        line-height: 20px;
        color: #434343;
      }

      .desc {
        font-size: 12px;
        line-height: 20px;
        color: #8c8c8c;
        margin-bottom: 4px;
      }

      .num_box {
        display: flex;

        .total {
          font-size: 18px;
          font-weight: 500;
          line-height: 26px;
          color: #262626;
        }

        .percent {
          font-size: 10px;
          line-height: 10px;
          margin-left: 4px;
          // transform: scale(0.8, 0.8);
        }
      }
    }

    &:first-child {
      margin-right: 98px;

      .legend_icon {
        background-color: #4b7eff;
      }
    }

    &:last-child {
      .legend_icon {
        background-color: #ead83c;
      }
    }

    // &.good {
    //   margin-right: 98px;

    //   .legend_icon {
    //     background-color: #4B7EFF;
    //   }
    // }

    // &.bad {
    //   .legend_icon {
    //     background-color: #EAD83C;
    //   }
    // }
  }
}

.common_wrap {
  @include common_wrap;

  .title {
    @include title;
  }

  .total_num {
    margin: 26px 0 20px;
  }

  .my_table {
    @include table;

    span {
      display: inline-block;
      text-align: center;
    }
  }
}
