import { Button } from 'antd'
import style from './index.module.scss'
import { DualAxes } from '@ant-design/plots'
import Empty from '@renderer/component/Empty'

type DiffTypeChartProps = {
  legendData: LegendItemProps[]
  chartConfig: any
  tableData: any
  tableTitle: string
  downloadCSV?: () => void
  emptyStatus?: boolean
}

type LegendItemProps = {
  title: string
  total: number
  color?: string
}
function DiffTypeChart(props: DiffTypeChartProps) {
  const {
    legendData,
    chartConfig,
    tableData,
    tableTitle,
    downloadCSV,
    emptyStatus
  } = props
  const config = chartConfig
  return emptyStatus ? (
    <Empty />
  ) : (
    <>
      <div className={style.common_wrap}>
        <div className={style.legend_wrap}>
          {legendData.map((item, index) => (
            <div className={style.legend_item} key={index}>
              <span className={style.legend_icon}></span>
              <div className={style.info_box}>
                <div className={style.title_}>{item.title}</div>
                <div className={style.num_box}>
                  <span className={style.total}>{item.total}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
        <DualAxes {...config}></DualAxes>
      </div>
      <div className={style.common_wrap}>
        <div className={style.title}>
          <div className={style.title_right}>
            <span>{tableTitle} breakdown</span>
          </div>
          <Button
            type="primary"
            ghost
            icon={
              <i
                className="icon-xiazai iconfont"
                style={{ marginRight: 4 }}
              ></i>
            }
            onClick={downloadCSV}
          >
            Export CSV
          </Button>
        </div>
        <div className={style.my_table}>
          <table>
            <tbody>
              <tr>
                <th>Series</th>
                {tableData.map((item, index) => (
                  <td key={index}>
                    <span>{item.days}</span>
                    <span
                      style={{
                        fontSize: '12px',
                        color: '#BFBFBF',
                        margin: '0 8px'
                      }}
                    >
                      VS
                    </span>
                    <span>{item.preDays}</span>
                  </td>
                ))}
              </tr>
              <tr>
                <th>{tableTitle}</th>
                {tableData.map((item, index) => (
                  <td key={index}>
                    <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                      <span style={{ width: '50%' }}>
                        <span>{item.timeFormat ?? item.total}</span>
                        {item.diffTime && (
                          <div
                            style={{
                              color: item.diffTime.includes('-')
                                ? '#48C43D'
                                : '#EE433D'
                            }}
                          >
                            {item.diffTime.includes('-')
                              ? item.diffTime
                              : '+' + item.diffTime}
                          </div>
                        )}
                        {Object.prototype.hasOwnProperty.call(
                          item,
                          'diffNumber'
                        ) && (
                          <div
                            style={{
                              color: item.diffNumber < 0 ? '#48C43D' : '#EE433D'
                            }}
                          >
                            {item.diffNumber < 0
                              ? '-' + item.diffNumber
                              : '+' + item.diffNumber}
                          </div>
                        )}
                      </span>
                      <span style={{ width: '50%' }}>
                        {item.preTimeFormat
                          ? item.preTimeFormat
                          : item.preTotal}
                      </span>
                    </div>
                  </td>
                ))}
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </>
  )
}

export default DiffTypeChart
