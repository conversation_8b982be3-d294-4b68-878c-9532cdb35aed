import style from './index.module.scss'
import FilterHeader from '../components/FilterHeader'
import { Tooltip } from 'antd'
import DiffTypeChart from '../components/DiffTypeChart/index'
import { exportChartsData, getResolutionTime } from '@renderer/api/report'
import { useEffect, useState } from 'react'
import { DualAxesConfig } from '@ant-design/plots'
import { downloadCSVFile, formatSeconds } from '@renderer/utils/util'
const ChatResponseTimes = () => {
  const [filterData, setFilterData] = useState<ReportDataParams>({})
  const [legendData, setLegendData] = useState([])
  const [tableData, setTableData] = useState<any>([])
  const [chartConfig, setChartConfig] = useState<DualAxesConfig>({
    data: [[], []],
    xField: 'timerShaft',
    yField: ['time', 'preTime'],
    yAxis: {
      time: {
        label: {
          formatter: (val: string) => {
            return formatSeconds(Number(val))
          }
        },
        tickCount: 10
      },
      preTime: {
        label: {
          formatter: (val: string) => {
            return formatSeconds(Number(val))
          }
        },
        tickCount: 10
      }
    },
    geometryOptions: [
      {
        geometry: 'line',
        color: '#4B7EFF'
      },
      {
        geometry: 'line',
        color: '#EAD83C'
      }
    ],
    tooltip: {
      formatter: (datum) => {
        if (datum?.time !== undefined) {
          return { name: 'time', value: formatSeconds(datum.time) }
        } else {
          return { name: 'preTime', value: formatSeconds(datum.preTime) }
        }
      }
    },
    legend: {
      itemName: {
        formatter: (text: string) => {
          return text === 'time' ? 'First time' : 'Previous period first time'
        }
      }
    }
  })
  const fetchResolutionTime = async () => {
    const data: any = await getResolutionTime(filterData)
    setLegendData([
      {
        title: data.days,
        total: data.timeFormat
      },
      {
        title: data.preDays,
        total: data.preTimeFormat
      }
    ])
    setChartConfig({
      ...chartConfig,
      data: [data.resolutionTimeList, data.resolutionTimeList]
    })
    setTableData(data.resolutionTimeList)
  }

  const onFilterChange = (params: ReportDataParams) => {
    setFilterData(params)
  }

  const downloadCSV = async () => {
    const path = 'resolution-time'
    const res: any = await exportChartsData(path, filterData)
    downloadCSVFile(res.data, path)
  }
  useEffect(() => {
    fetchResolutionTime()
  }, [filterData])
  return (
    <div className={style.chat_responese_wrap}>
      <FilterHeader onFilterChange={onFilterChange}></FilterHeader>
      <div className={style.content_wrap}>
        <div className={style.base_info_card}>
          <div className={style.common_wrap}>
            <div className={style.title}>
              <div className={style.title_right}>
                <span>Resolution time</span>
                <Tooltip
                  title={
                    'This chart shows you an average amount of time it took for an agent to resolve a ticket.'
                  }
                  placement="bottomLeft"
                  arrow
                >
                  <i className="icon-tishi iconfont"></i>
                </Tooltip>
              </div>
            </div>
            <DiffTypeChart
              legendData={legendData}
              chartConfig={chartConfig}
              tableData={tableData}
              tableTitle="Resolution time"
              downloadCSV={downloadCSV}
            ></DiffTypeChart>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ChatResponseTimes
