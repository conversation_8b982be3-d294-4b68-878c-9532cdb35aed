@import '../mixin.scss';

.chat_satisfaction_wrap {
  @include wrap_common;
  .content_wrap {
    flex: 1;
    .base_info_card {
      @include base_info_card;
      .common_wrap {
        @include common_wrap;
        .title {
          @include title;
        }
        .rate_percent {
          display: flex;
          align-items: center;
          .desc {
            margin-left: 16px;
          }
        }
        .rate_wrap {
          display: flex;
          align-items: center;
          margin-bottom: 20px;
          .rate_item {
            display: flex;
            align-items: baseline;
            justify-content: center;
            .rate_icon {
              width: 8px;
              height: 8px;
            }
            .info_box {
              margin-left: 4px;
              .title {
                font-size: 12px;
                line-height: 20px;
                color: #434343;
                margin-bottom: 4px;
              }
              .num_box {
                display: flex;
                align-items: flex-start;
                justify-content: center;
                .total {
                  font-size: 18px;
                  font-weight: 500;
                  line-height: 26px;
                  color: #262626;
                }
                .percent {
                  font-size: 10px;
                  line-height: 10px;
                  margin-left: 4px;
                  // transform: scale(0.8, 0.8);
                }
              }
            }
            &.good {
              margin-right: 98px;
              .rate_icon {
                background-color: #48c43d;
              }
              .percent {
                color: #48c43d;
              }
            }
            &.bad {
              .rate_icon {
                background-color: #EE433D;
              }
              .percent {
                color: #EE433D;
              }
            }
          }
        }
        .my_table {
          @include table;
        }
        .ant_table {
          .avatar_wrap {
            display: flex;
            align-items: center;
            .avatar {
              display: flex;
              align-items: center;
              margin-left: 16px;
              img {
                display: block;
                width: 42px;
                height: 42px;
                border-radius: 50%;
              }
              .info {
                margin-left: 8px;
                & >:first-child { 
                  color: #262626;
                  line-height: 22px;
                }
                &:not(:first-child) {
                  font-size: 12px;
                  line-height: 20px;
                  color: #8C8C8C;
                }
              }
            }
          }
          .icon-haoping {
            
          }
        }
      }
    }
  }
}
