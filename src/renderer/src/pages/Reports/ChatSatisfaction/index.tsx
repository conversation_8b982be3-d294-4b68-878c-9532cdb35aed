import { Button, Divider, Progress, Switch, Table, Tooltip } from 'antd'
import React, { useEffect, useState } from 'react'
import style from './index.module.scss'
import FilterHeader from '../components/FilterHeader'
import { Line, LineConfig } from '@ant-design/plots'
import FsTableSelf from '@renderer/component/FsTableSelf'
import {
  exportChartsData,
  getSatisfaction,
  getSatisfactionAgents
} from '@renderer/api/report'
import _ from 'lodash'
import { downloadCSVFile } from '@renderer/utils/util'
import Empty from '@renderer/component/Empty'

type SatisfactionData = {
  good: number
  goodAverage: number
  bad: number
  badAverage: number
}
type DataItem = {
  days?: string
  good?: number
  bad?: number
}
type TableDataItem = {
  avatar: string
  bad: number
  badAverage: number
  email: string
  good: number
  goodAverage: number
  name: string
  total: number
  userId: number
}
const seriesKey = 'series'
const valueKey = 'value'
function processData(data, yFields) {
  const result = []
  data.forEach((d) => {
    yFields.forEach((yField) => {
      const name = yField
      result.push({
        ...d,
        date: d.date,
        [seriesKey]: name,
        [valueKey]: d[yField]
      })
    })
  })
  return result
}

const tableColumns = [
  {
    title: 'Agents',
    key: 'agents',
    render: (_: string, record: any, index: number) => (
      <div className={style.avatar_wrap}>
        <span>{index + 1}</span>
        <div className={style.avatar}>
          <img src={record.avatar} />
          <div className={style.info}>
            <div>{record.name}</div>
            <div>{record.email}</div>
          </div>
        </div>
      </div>
    )
  },
  {
    title: 'Rated Chats',
    dataIndex: 'total',
    key: 'total'
  },
  {
    title: 'Rated good',
    key: 'good',
    render: (_: string, record: any) => (
      <div>
        <i
          className="icon-a-rongqi19301 iconfont"
          style={{ color: '#10A300' }}
        ></i>
        {`${record.good} (${Math.round(record.goodAverage * 100)}%)`}
      </div>
    )
  },
  {
    title: 'Rated bad',
    key: 'bad',
    render: (_: string, record: any) => (
      <div>
        <i className="icon-a-rongqi1 iconfont" style={{ color: '#EE433D' }}></i>
        {`${record.bad} (${Math.round(record.badAverage * 100)}%)`}
      </div>
    )
  }
]

const ChatSatisfaction = () => {
  const [data, setData] = useState<DataItem[]>([])
  const [emptyStatus, setEmptyStatus] = useState(false)
  const [filterData, setFilterData] = useState<ReportDataParams>({})
  const [tableData, setTableData] = useState<TableDataItem[]>()
  const [satisfactionData, setSatisfactionData] = useState<SatisfactionData>({
    good: 0,
    goodAverage: 100,
    bad: 0,
    badAverage: 0
  })
  const config: LineConfig = {
    data: processData(data, ['good', 'bad']),
    xField: 'days',
    yField: valueKey,
    seriesField: seriesKey,
    color: ['#48C43D', '#EE433D'],
    smooth: true,
    point: {
      size: 5,
      color: ['#48C43D', '#EE433D'],
      style: {
        lineWidth: 2,
        fillOpacity: 1
      },
      shape: (item: any) => {
        if (item[seriesKey] === 'good') {
          return 'circle'
        }

        return 'diamond'
      }
    }
  }
  const fetchSatisfaction = async () => {
    const data: any = await getSatisfaction(filterData)
    data.totalFeedbackList.length === 0
      ? setEmptyStatus(true)
      : setEmptyStatus(false)
    setSatisfactionData({
      good: data.good,
      bad: data.bad,
      goodAverage: data.goodAverage,
      badAverage: data.badAverage
    })
    setData(data.totalFeedbackList)
  }
  const fetchTableData = async () => {
    const data: any = await getSatisfactionAgents(filterData)
    setTableData(data)
  }
  const onFilterChange = (params: ReportDataParams) => {
    setFilterData(params)
  }
  const downloadCSV = async () => {
    const path = 'satisfaction/line-chart'
    const response: any = await exportChartsData(path, filterData)
    downloadCSVFile(response.data, path)
  }
  useEffect(() => {
    fetchSatisfaction()
    fetchTableData()
  }, [filterData])

  return (
    <div className={style.chat_satisfaction_wrap}>
      <FilterHeader onFilterChange={onFilterChange}></FilterHeader>
      <div className={style.content_wrap}>
        {/* 上半部分 */}
        <div className={style.base_info_card}>
          <div className={style.common_wrap}>
            <div className={style.title}>
              <div className={style.title_right}>
                <span>Chat satisfaction</span>
                <Tooltip
                  title="This chart shows how your customer service is performing."
                  placement="bottomLeft"
                  arrow
                >
                  <i className="icon-tishi iconfont"></i>
                </Tooltip>
              </div>
              {/* <span
                style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
              >
                <span>24-hour distrubution:</span>
                <Switch onChange={handleChange} />
              </span> */}
            </div>
            <div className={style.rate_percent}>
              <Progress
                type="circle"
                percent={Math.round(satisfactionData.goodAverage * 100)}
                strokeLinecap="butt"
                strokeColor="#48C43D"
                trailColor="#EEFFEA"
                strokeWidth={15}
                format={(percent) => `${percent}%`}
              />
              <span className={style.desc}>Average satisfaction rate</span>
            </div>
            <Divider />
          </div>
          <div className={style.common_wrap}>
            <div className={style.rate_wrap}>
              <div className={[style.rate_item, style.good].join(' ')}>
                <span className={style.rate_icon}></span>
                <div className={style.info_box}>
                  <div className={style.title}>Rated good</div>
                  <div className={style.num_box}>
                    <span className={style.total}>{satisfactionData.good}</span>
                    <span className={style.percent}>
                      {Math.round(satisfactionData.goodAverage * 100)}%
                    </span>
                  </div>
                </div>
              </div>
              <div className={[style.rate_item, style.bad].join(' ')}>
                <span className={style.rate_icon}></span>
                <div className={style.info_box}>
                  <div className={style.title}>Rated bad</div>
                  <div className={style.num_box}>
                    <span className={style.total}>{satisfactionData.bad}</span>
                    <span className={style.percent}>
                      {Math.round(satisfactionData.badAverage * 100)}%
                    </span>
                  </div>
                </div>
              </div>
            </div>
            {emptyStatus ? <Empty /> : <Line {...config} />}
          </div>
          {emptyStatus ? null : (
            <div className={style.common_wrap}>
              <div className={style.title}>
                <div className={style.title_right}>
                  <span>Chat satisfaction breakdown</span>
                </div>
                <Button
                  type="primary"
                  ghost
                  icon={
                    <i
                      className="icon-xiazai iconfont"
                      style={{ marginRight: 4 }}
                    ></i>
                  }
                  onClick={downloadCSV}
                >
                  Export CSV
                </Button>
              </div>
              <div className={style.my_table}>
                <table>
                  <tbody>
                    <tr>
                      <th>Series</th>
                      {data.map((item, index) => (
                        <td key={index}>{item.days}</td>
                      ))}
                    </tr>
                    <tr>
                      <th>Rated good</th>
                      {data.map((item, index) => (
                        <td key={index}>{item.good}</td>
                      ))}
                    </tr>
                    <tr>
                      <th>Rated bad</th>
                      {data.map((item, index) => (
                        <td key={index}>{item.bad}</td>
                      ))}
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
        {/* 下半部分 */}
        <div className={style.base_info_card}>
          <div className={style.common_wrap}>
            <div className={style.title}>
              <div className={style.title_right}>
                <span>Total rated agents</span>
                <Tooltip
                  title="This is the chat performance list of the agents. The ranking is based on Wilson scores."
                  placement="bottomLeft"
                  arrow
                >
                  <i className="icon-tishi iconfont"></i>
                </Tooltip>
              </div>
            </div>
            <FsTableSelf
              dataSource={tableData}
              columns={tableColumns}
              className={style.ant_table}
              rowKey={(record) => record.name}
              pagination={false}
            ></FsTableSelf>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ChatSatisfaction
