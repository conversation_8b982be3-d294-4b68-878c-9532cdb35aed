@mixin wrap_common {
  height: 100%;
  display: flex;
  flex-direction: column;
}
@mixin filter_head {
  margin-bottom: 20px;
}
@mixin content_wrap {
  flex: 1;
}
@mixin base_info_card {
  padding: 20px;
  background-color: #fff;
  margin-bottom: 20px;
}

@mixin common_wrap {
  margin-bottom: 20px;
  &:last-child {
    margin: 0;
  }
}
@mixin title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  .title_right {
    span {
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      color: #262626;
      margin-right: 4px;
    }
    i {
      font-size: 12px;
      color: #d9d9d9;
      cursor: pointer;
    }
  }
  button {
    display: flex;
    align-items: center;
  }
}
@mixin table {
  max-width: 100%;
  overflow: scroll;
  white-space: nowrap;
  table {
    // width: 100%;
    & > tbody > tr {
      &:first-child {
        background: #f0f0f0;
      }
      &:nth-child(n + 2) {
        & > td:nth-child(2n) {
          background: #fafafa;
        }
      }
    }
    & > tbody > tr > th,
    & > tbody > tr > td {
      padding: 7px 8px;
      border: 1px solid #d9d9d9;
      text-align: center;
    }

    & > tbody > tr > th {
      font-size: 14px;
      line-height: 22px;
      color: #262626;
      font-weight: normal;
    }
  }
}
