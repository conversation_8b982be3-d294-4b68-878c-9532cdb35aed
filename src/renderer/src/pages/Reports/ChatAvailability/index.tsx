import React, { useEffect, useMemo, useRef, useState } from 'react'
import style from './index.module.scss'
import FilterHeader from '../components/FilterHeader'
import { Button, Switch, Tooltip } from 'antd'
import { Chart } from '@antv/g2'
import { Line, DualAxes } from '@ant-design/plots'
import {
  exportChartsData,
  getChatAvailability,
  getChatNumberStatus
} from '@renderer/api/report'
import { downloadCSVFile, formatSeconds } from '@renderer/utils/util'

const toolTipString =
  "This chart shows you an overview of your team's availability." // eslint-disable-line
const ChatAvailability = () => {
  const [legendData, setLegendData] = useState({
    onlineNumber: 0,
    offlineNumber: 0
  })
  const [data2, setData2] = useState<any[]>([])
  const [data, setData] = useState<any[]>([])
  const [filterData, setFilterData] = useState<ReportDataParams>({})
  const [totalTime, setTotalTime] = useState(0)
  const config1 = {
    data,
    xField: 'days',
    yField: 'time',
    xAxis: {
      // type: 'timeCat',
      tickCount: 5
    },
    yAxis: {
      label: {
        formatter: (val: string) => {
          return formatSeconds(Number(val))
        }
      }
    },
    tooltip: {
      formatter: (datum) => {
        return { name: 'time', value: formatSeconds(datum.time) }
      }
    },
    smooth: true,
    point: {
      size: 5,
      style: {
        fill: 'white',
        stroke: '#5B8FF9',
        lineWidth: 2
      }
    }
  }
  const config2 = {
    data: [data2, data2],
    xField: 'day',
    yField: ['online', 'offline'],
    geometryOptions: [
      {
        geometry: 'line',
        color: '#48C43D'
      },
      {
        geometry: 'line',
        color: '#EE433D'
      }
    ]
  }
  const fetchChatAvailability = async () => {
    const data: any = await getChatAvailability(filterData)
    setData(data.chatAvailabilityList)
    setTotalTime(data.timeFormat)
  }

  const fetchChatNumberStatus = async () => {
    const data: any = await getChatNumberStatus(filterData)
    setLegendData({
      onlineNumber: data.onlineNumber,
      offlineNumber: data.offlineNumber
    })
    setData2(data.groupStatusList)
  }
  const firstPercent = useMemo(() => {
    if (!isNaN(legendData.onlineNumber)) {
      return (
        (legendData.onlineNumber /
          (legendData.onlineNumber + legendData.offlineNumber)) *
        100
      ).toFixed(2)
    } else {
      return 0
    }
  }, [legendData.onlineNumber, legendData.offlineNumber])

  const onFilterChange = (params: ReportDataParams) => {
    setFilterData(params)
  }
  const downloadCSV = (path: string) => async () => {
    const res: any = await exportChartsData(path, filterData)
    downloadCSVFile(res.data, path)
  }

  useEffect(() => {
    fetchChatAvailability()
    fetchChatNumberStatus()
  }, [filterData])
  return (
    <div className={style.chat_availability_wrap}>
      <FilterHeader onFilterChange={onFilterChange}></FilterHeader>
      <div className={style.content_wrap}>
        {/* 上半部分 */}
        <div className={style.base_info_card}>
          <div className={style.common_wrap}>
            <div className={style.title}>
              <div className={style.title_right}>
                <span>Chat availability</span>
                <Tooltip title={toolTipString} placement="bottomLeft" arrow>
                  <i className="icon-tishi iconfont"></i>
                </Tooltip>
              </div>
            </div>
            <div className={style.total_time}>{totalTime}</div>
            <Line {...config1}></Line>
          </div>
          <div className={style.common_wrap}>
            <div className={style.title}>
              <div className={style.title_right}>
                <span>Chat availability breakdown</span>
                {/* <Tooltip
                  title={toolTipString}
                  placement="bottomLeft"
                  arrow
                >
                  <i className="icon-tishi iconfont"></i>
                </Tooltip> */}
              </div>
              <Button
                type="primary"
                ghost
                icon={
                  <i
                    className="icon-xiazai iconfont"
                    style={{ marginRight: 4 }}
                  ></i>
                }
                onClick={downloadCSV('chat/availability')}
              >
                Export CSV
              </Button>
            </div>
            <div className={style.my_table}>
              <table>
                <tbody>
                  <tr>
                    <th>Series</th>
                    {data.map((item, index) => (
                      <td key={index}>{item.days}</td>
                    ))}
                  </tr>
                  <tr>
                    <th>Availabitity</th>
                    {data.map((item, index) => (
                      <td key={index}>{item.timeFormat}</td>
                    ))}
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        {/* 下半部分 */}
        <div className={style.base_info_card}>
          <div className={style.common_wrap}>
            <div className={style.title}>
              <div className={style.title_right}>
                <span>Number of chats by status</span>
                <Tooltip
                  title={
                    'This chart show you how many chats were started when status of given group was set to Online or Offline.'
                  }
                  placement="bottomLeft"
                  arrow
                >
                  <i className="icon-tishi iconfont"></i>
                </Tooltip>
              </div>
              {/* <span>
                24-hour distrubution: <Switch />
              </span> */}
            </div>
            <div className={style.rate_wrap}>
              <div className={[style.rate_item, style.good].join(' ')}>
                <span className={style.rate_icon}></span>
                <div className={style.info_box}>
                  <div className={style.title_}>Online</div>
                  <div className={style.desc}>Accepting chats</div>
                  <div className={style.num_box}>
                    <span className={style.total}>
                      {legendData.onlineNumber}
                    </span>
                    <span className={style.percent}>{firstPercent}%</span>
                  </div>
                </div>
              </div>
              <div className={[style.rate_item, style.bad].join(' ')}>
                <span className={style.rate_icon}></span>
                <div className={style.info_box}>
                  <div className={style.title_}>Offline</div>
                  <div className={style.desc}>Not accepting chats</div>
                  <div className={style.num_box}>
                    <span className={style.total}>
                      {legendData.offlineNumber}
                    </span>
                    <span className={style.percent}>
                      {100 - Number(firstPercent)}%
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <DualAxes {...config2}></DualAxes>
          </div>
          <div className={style.common_wrap}>
            <div className={style.title}>
              <div className={style.title_right}>
                <span>Number of chats by status breakdown</span>
                {/* <Tooltip
                title={toolTipString}
                placement="bottomLeft"
                arrow
              >
                <i className="icon-tishi iconfont"></i>
              </Tooltip> */}
              </div>
              <Button
                type="primary"
                ghost
                icon={
                  <i
                    className="icon-xiazai iconfont"
                    style={{ marginRight: 4 }}
                  ></i>
                }
                onClick={downloadCSV('chat/number/status')}
              >
                Export CSV
              </Button>
            </div>
            <div className={style.my_table}>
              <table>
                <tbody>
                  <tr>
                    <th>Series</th>
                    {data2.map((item, index) => (
                      <td key={index}>{item.day}</td>
                    ))}
                  </tr>
                  <tr>
                    <th>Total</th>
                    {data2.map((item, index) => (
                      <td key={index}>{item.total}</td>
                    ))}
                  </tr>
                  <tr>
                    <th>Online</th>
                    {data2.map((item, index) => (
                      <td key={index}>{item.online}</td>
                    ))}
                  </tr>
                  <tr>
                    <th>Offline</th>
                    {data2.map((item, index) => (
                      <td key={index}>{item.offline}</td>
                    ))}
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ChatAvailability
