import { useEffect, useState } from 'react'

import styles from './index.module.scss'

type PropsTypes = {
  getSelect: (val: number) => void
  navData: NavListType[]
  tabIndex: number
}

export default function NavBar(props: PropsTypes) {
  const { getSelect, navData, tabIndex } = props
  const [select, setSelect] = useState<number>(tabIndex)
  const handleClick = (index: number) => {
    setSelect(index)
    //将选择的id传递给父组件
    getSelect(data[index].id)
  }
  const [data, setdata] = useState<NavListType[]>([])

  useEffect(() => {
    console.log(navData)
    if (navData && navData.length) {
      setdata(navData)
    } else {
      setdata([])
    }
  }, [navData])

  return (
    <div className={styles.nav}>
      <h1 className={styles.title}>Tickets</h1>
      <ul>
        {data.map((item, index) => {
          return (
            <li key={item.id} className={styles.list}>
              <div
                className={select === index ? styles.active : ''}
                onClick={() => handleClick(index)}
              >
                <span>{item.name}</span>
                <span>{item.number}</span>
              </div>
            </li>
          )
        })}
      </ul>
    </div>
  )
}
