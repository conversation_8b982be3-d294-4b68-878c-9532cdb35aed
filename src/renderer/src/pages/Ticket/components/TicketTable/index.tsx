import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Table, Tag, Button } from 'antd'
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table'
import type { TableRowSelection } from 'antd/es/table/interface'
import { siteColorMap } from '@/utils/util'

import styles from './index.module.scss'
import FsTableSelf from '@renderer/component/FsTableSelf'

interface DataType {
  key: string
  ticket: string
  requester: Record<string, string | number>
  email: string
  assignee: string
  siteSource: string
  status: Record<string, string | number>
  lastMessage: string
  isDisabled?: boolean
}

type PropsTypes = {
  isMerge: boolean
  getMergeData: (val: number[]) => void
  getPagination: (current: number, size: number) => void
  tableData: Record<string, any>
}

export default function TicketTable(props: PropsTypes) {
  const { isMerge, getMergeData, getPagination, tableData } = props
  console.log(isMerge)
  const [rowSelection, setRowSelection] = useState<
    TableRowSelection<DataType> | undefined
  >(undefined)
  const navigate = useNavigate()

  const columns: ColumnsType<DataType> = [
    {
      title: 'Ticket',
      dataIndex: 'ticket',
      key: 'ticket'
    },
    {
      title: 'Requester',
      dataIndex: 'requester',
      key: 'requester',
      render: (text: Record<string, string | number>) => {
        return text.name
      }
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email'
    },
    {
      title: 'Assignee',
      dataIndex: 'assignee',
      key: 'assignee'
    },
    {
      title: 'Site Source',
      dataIndex: 'siteSource',
      key: 'siteSource',
      render: (text: string) => {
        return (
          <Tag color={siteColorMap[text.toLowerCase()]}>
            {text.toUpperCase()}
          </Tag>
        )
      }
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (_, { status }) => {
        const colorMap: Record<string, string> = {
          1: 'green',
          2: 'red',
          3: 'orange',
          4: 'default'
        }
        return <Tag color={colorMap[status.id]}>{status.text}</Tag>
      }
    },
    {
      title: 'Last Message',
      dataIndex: 'lastMessage',
      key: 'lastMessage'
    },
    {
      title: 'Action',
      key: 'action',
      render: (_, record) => {
        return (
          <Button type="link" onClick={() => handleGoToDetail(record.ticket)}>
            See details
          </Button>
        )
      }
    }
  ]

  console.log(tableData)

  // 初始化table数据
  const [data, setData] = useState<DataType[]>([])
  useEffect(() => {
    if (tableData.records && tableData.records.length) {
      const initData: DataType[] = tableData.records.map((item: any) => {
        return {
          key: item.id,
          ticket: item.idStr,
          requester: {
            name: item.customerName,
            id: item.customersId
          },
          email: item.customerEmail,
          assignee: item.customersServiceName,
          siteSource: item.siteCode,
          status: {
            text: item.ticketStatusStr,
            id: item.ticketStatus
          },
          lastMessage: item.lastMessageTime,
          isDisabled: false
        }
      })
      console.log(initData)
      setData(initData)
      console.log(tableData.total, '123')
      setPagination({
        ...pagination,
        size: tableData.size,
        current: tableData.current,
        total: tableData.total
      })
    } else {
      setData([])
      setPagination({
        ...pagination,
        current: 1,
        total: 0
      })
    }
  }, [tableData, isMerge])

  // 当进入合并ticket状态时，将不同requester的ticket禁用
  useEffect(() => {
    if (isMerge) {
      setRowSelection({
        type: 'checkbox',
        hideSelectAll: true,
        getCheckboxProps: (record) => ({
          disabled: record.status.id !== 1 || record.isDisabled
        }),
        onChange: (selectedRowKeys, selectedRows) => {
          if (selectedRowKeys.length) {
            console.log(selectedRows)
            const newData = data
            newData?.forEach((item) => {
              if (
                selectedRows.length &&
                selectedRows[0].requester.id &&
                selectedRows[0].requester.id !== item.requester.id
              ) {
                item.isDisabled = true
              } else {
                item.isDisabled = false
              }
            })
            setData([...newData])
          } else {
            const newData = data
            newData?.forEach((item) => {
              item.isDisabled = false
            })
            setData([...newData])
          }
          const mergeData: number[] = []
          selectedRows.map((item) => mergeData.push(parseInt(item.ticket)))

          getMergeData(mergeData)
        }
      })
    } else {
      setRowSelection(undefined)
    }
  }, [isMerge])

  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 1,
    hideOnSinglePage: true,
    showSizeChanger: true,
    showTotal: (total) => `Total ${total} items`
  })

  const handleTableChange = (pagination: TablePaginationConfig) => {
    console.log(pagination)
    setPagination(pagination)
    getPagination(pagination.current, pagination.pageSize)
  }

  const handleGoToDetail = (id: string) => {
    navigate(`/ticket/ticketDetail/${id}`)
  }

  return (
    <div className={styles.table}>
      <FsTableSelf
        columns={columns}
        dataSource={data}
        rowSelection={rowSelection}
        pagination={pagination}
        onChange={handleTableChange}
      />
    </div>
  )
}
