import { Tag } from 'antd'

import styles from './index.module.scss'

type PropsType = {
  ticketStatus: Record<string, any>
}

export default function Status(props: PropsType) {
  const { ticketStatus } = props
  console.log(ticketStatus, '111')
  const StatusTag = () => {
    const colorMap: Record<string, string> = {
      1: 'green',
      2: 'red',
      3: 'default',
      4: 'blue'
    }
    if (ticketStatus && ticketStatus.ticketStatus) {
      return (
        <Tag color={colorMap[ticketStatus.ticketStatus]}>
          {ticketStatus.ticketStatusStr}
        </Tag>
      )
    }
    return <Tag color={colorMap[0]}>Waitting</Tag>
  }

  return (
    <div className={styles.status}>
      <h2 className={styles.title}>Ticket Status</h2>
      <StatusTag />
    </div>
  )
}
