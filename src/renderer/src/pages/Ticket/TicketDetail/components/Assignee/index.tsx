import { useState, useEffect } from 'react'

import { Select, message } from 'antd'

import { putChangeTicketCustomer } from '@/api/ticket'

import styles from './index.module.scss'
import { useSelector } from 'react-redux'
import { RootState } from '@renderer/store'

type PropsTypes = {
  customerList: any[]
  assignee: AssigneeTypes
  customerServiceDetail: Record<string, any>
}

type ChangeAssigneeTypes = Omit<AssigneeTypes, 'name'> & { userId: string }

export default function Assignee(props: PropsTypes) {
  const { customerList, assignee, customerServiceDetail } = props
  const customerInfo = useSelector(
    (state: RootState) => state.login.customerInfo
  )
  console.log(assignee, '112233')
  const [changeAssignee, setChangeAssignee] = useState<ChangeAssigneeTypes>({
    userId: '',
    id: '',
    ticketStatus: 0
  })

  const handleChangeAssignee = (value: string) => {
    setChangeAssignee({
      userId: value,
      id: assignee.id,
      ticketStatus: 1
    })
  }

  const fetchChangeAssignee = async () => {
    const res: any = await putChangeTicketCustomer(changeAssignee)
    if (res.result) {
      message.success(res.message)
    } else {
      message.error(res.message)
    }
  }

  useEffect(() => {
    if (changeAssignee.userId.length && changeAssignee.userId !== assignee.id) {
      fetchChangeAssignee()
    }
  }, [changeAssignee])

  return (
    <div className={styles.assignee}>
      <div className={styles.title}>
        <h2>Assignee</h2>
        {(assignee.ticketStatus === 2 ||
          (assignee.ticketStatus === 1 &&
            customerInfo?.userId === customerServiceDetail?.userId)) && (
          <Select
            showSearch
            placeholder="change"
            style={{ minWidth: '150px' }}
            options={customerList}
            fieldNames={{ label: 'name', value: 'idStr' }}
            onChange={(value: string) => handleChangeAssignee(value)}
          />
        )}
      </div>
      <p className={styles.font}>{assignee.name || 'Unaassignee'}</p>
    </div>
  )
}
