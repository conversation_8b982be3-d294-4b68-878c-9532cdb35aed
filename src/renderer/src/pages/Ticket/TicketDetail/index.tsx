import { useEffect, useMemo, useRef, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { Button, Space, message } from 'antd'
import { formatUTC, getCurrentTimeZone } from '@/utils/util'

import { postTicketDetail } from '@/api/ticket'

import styles from './index.module.scss'

import ChatView from '@/component/ChatView'
import General from '@/component/General'
import Status from './components/Status'
import Assignee from './components/Assignee'
import OtherInfo from '@/component/OtherInfo'
import useChatInfo from '@renderer/hooks/useChatInfo'
import { TicketBtnProps } from '@renderer/component/ChatView/component/MessageItem'
import { updateTicketStatus } from '@renderer/api/chat'
import { useSelector } from 'react-redux'
import { RootState } from '@renderer/store'

type StatusType = {
  ticketStatus: number
  ticketStatusStr: string
}

export default function TicketDetail() {
  const params = useParams()
  console.log(params.id, '123')
  // ticket详情聊天翻页参数
  const [pagination, setPagination] = useState({
    current: 1,
    size: 20
  })
  // const [ticketDetail, setTicketDetail] = useState<any>({})

  // 定义详情右边侧边栏数据变量
  const [customerDetail, setCustomerDetail] = useState<UserDetail>({})
  // ticket状态
  const [ticketStatus, setTicketStatus] = useState<StatusType>()
  // 客服列表
  const [customerList, setCustomerList] = useState<any[]>([])
  // ticket处理人信息
  const [assignee, setAssignee] = useState<AssigneeTypes>({
    name: '',
    id: '0',
    ticketStatus: 0
  })

  // 定义聊天内容数据变量
  const [chatList, setChatList] = useState<any>({})
  const { setChatDetail } = useChatInfo()
  const customerServiceDetail = useRef<any>({})
  const [ticketBtn, setTicketBtn] = useState<any>(null)
  const customerInfo = useSelector(
    (state: RootState) => state.login.customerInfo
  )
  const showTickerBtn = useMemo(() => {
    if (ticketBtn) {
      return (
        ticketStatus.ticketStatus === 2 ||
        (ticketStatus.ticketStatus === 1 &&
          customerServiceDetail.current?.userId === customerInfo?.userId)
      )
    }
    return false
  }, [ticketBtn, customerServiceDetail, ticketStatus])
  const fetchTicketDetail = async (id: string) => {
    const timeZone = getCurrentTimeZone()
    setLoading(true)
    const res: any = await postTicketDetail({ id, ...pagination, timeZone })
    res.chatMsgDetail.records.forEach((item) => {
      item.messageTime = formatUTC(item.messageTime)
    })
    setChatList(res.chatMsgDetail)
    customerServiceDetail.current = res.customerServiceDetail
    setLoading(false)
    if (customerDetail.userId !== res.customerDetail.userId) {
      setTicketBtn(res.ticketButton)
      setChatDetail(res.customerDetail)
      setCustomerDetail(res.customerDetail)
      setTicketStatus({
        ticketStatus: res.ticketStatus,
        ticketStatusStr: res.ticketStatusStr
      })
      setCustomerList(res.assigneeList)
      setAssignee({
        name: res.customerServiceDetail?.name,
        id: res.id.toString(),
        ticketStatus: res.ticketStatus
      })
    }
  }

  // 聊天内容加载更多
  const loadMore = (el: React.MutableRefObject<HTMLDivElement>) => {
    const { scrollTop } = el.current
    console.log(scrollTop)
    if (scrollTop < 10 && pagination.size < chatList.total) {
      setPagination({
        ...pagination,
        size: pagination.size + 10
      })
    }
  }
  // 聊天窗口滚动加载loading变量
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    const id = params.id
    fetchTicketDetail(id)
  }, [pagination])

  const navigate = useNavigate()

  const changeTicket = async (item: TicketBtnProps) => {
    const data: any = await updateTicketStatus({
      ticketStatus: item.type,
      id: `${ticketBtn.ticketId}`,
      userId: `${customerInfo.userId}`
    })
    if (data.result) {
      setTicketBtn(data.ticketButtonVO)
    } else {
      message.error(data.message)
    }
  }
  // 聊天头部
  const headerBox = (
    <div className={styles['chat-header']}>
      <div className={styles['chat-header-back']} onClick={() => navigate(-1)}>
        <i className="iconfont icon-zuo"></i>
        <span>Back</span>
      </div>
    </div>
  )

  return (
    <div className={styles.detail}>
      <div className={`${styles.left} ${showTickerBtn && styles.has_btn}`}>
        <ChatView
          headerBox={headerBox}
          messageData={chatList.records}
          loadMore={loadMore}
          historyLoading={loading}
        />
        {showTickerBtn && (
          <div className={styles.ticketButton}>
            {ticketBtn.isClick ? (
              ticketBtn.clickable.map((item: TicketBtnProps) => (
                <div className={styles.btn_item} key={item.type}>
                  <Button
                    type="primary"
                    ghost
                    onClick={() => changeTicket(item)}
                  >
                    {item.name}
                  </Button>
                </div>
              ))
            ) : (
              <div className={styles.btn_item}>
                <Button type="primary" ghost disabled>
                  {ticketBtn.unClickable.name}
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
      <div className={styles.right}>
        <Space direction="vertical" size={28} className={styles.space}>
          <h2 className={styles.title}>Details</h2>
          <General
            userId={customerDetail.userId}
            colour={customerDetail.colour}
            latitude={customerDetail.latitude}
            longitude={customerDetail.longitude}
            address={customerDetail.address}
            email={customerDetail.email}
            name={customerDetail.name}
            time={customerDetail.time}
            mobile={customerDetail.mobile}
          />
          <Status ticketStatus={ticketStatus} />
          <Assignee
            customerList={customerList}
            assignee={assignee}
            customerServiceDetail={customerServiceDetail.current}
          />
          <OtherInfo
            id={parseFloat(params.id)}
            ipAddress={customerDetail.ipAddress}
            fromPage={customerDetail.fromPage}
            isoCode={customerDetail.isoCode}
            os={customerDetail.os}
            browser={customerDetail.browser}
            isBlacklist={customerDetail.isBlacklist}
            {...customerDetail}
          />
        </Space>
      </div>
    </div>
  )
}
