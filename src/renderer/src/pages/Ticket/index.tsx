import { useState, useEffect } from 'react'
import { Outlet, useLocation } from 'react-router-dom'
import { Select, Space, DatePicker, Input, Button, message, Modal } from 'antd'
import { getCurrentTimeZone } from '@/utils/util'

// 引入侧边栏组件
import NavBar from '@/pages/Ticket/components/NavBar'
// 引入表格组件
import TicketTable from './components/TicketTable'
import styles from './index.module.scss'

const { RangePicker } = DatePicker
const { Search } = Input

import { postTicketList, getTicketFilter, putMergeTicket } from '@/api/ticket'

const timeZone = getCurrentTimeZone()

export default function Ticket() {
  const location = useLocation()
  // 定义一个变量，用来接收子组件传递过来的值，控制筛选项的显示
  const [childSelect, setChildSelect] = useState<number>(1)
  const getChildSelect = (val: number) => {
    console.log('获取子组件的值', val)
    setChildSelect(val)
    setChildPagination((prev) => ({
      ...prev,
      current: 1
    }))
    if (val === 0 || val === 1) {
      setAllAssigneeValue(undefined)
      setTicketListParams({
        ...ticketListParams,
        type: val,
        customerServiceId: ''
      })
    } else {
      setTicketListParams({
        ...ticketListParams,
        type: val
      })
    }
    if (ticketListParams?.ticketStatus && val !== 5) {
      setTicketListParams({
        ...ticketListParams,
        ticketStatus: undefined
      })
    }
  }

  // 定义一个变量，用来接收子组件传递过来的值，用来判断是否可以将ticket进行合并
  const [childMergeData, setChildMergeData] = useState<number[]>([])
  const getChildMergeData = (val: number[]) => {
    console.log('获取子组件的值', val)
    setChildMergeData(val)
  }
  // 是否开始合并消息
  const [isMerge, setIsMerge] = useState<boolean>(false)
  const [messageApi, contextHolder] = message.useMessage()
  // 弹出框显隐变量
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false)
  // 确定合并之后，按钮进入Loading状态的变量
  const [isMergeLoading, setIsMergeLoading] = useState<boolean>(false)
  const handleMerge = () => {
    if (isMerge) {
      // 逻辑待定
      if (childMergeData.length < 2) {
        messageApi.open({
          type: 'error',
          content: 'Please select at least two tickets to merge'
        })
        return
      }
      setIsModalOpen(true)
    } else {
      setIsMerge(true)
    }
  }
  const handleCancel = () => {
    setIsMerge(false)
  }

  const [childPagination, setChildPagination] = useState<
    Record<string, number>
  >({
    current: 1,
    size: 10
  })
  // 获取table组件中的翻页数据
  const getPagination = (current: number, size: number) => {
    console.log('获取table组件翻页的参数', current, size)
    setChildPagination({
      current,
      size
    })
  }

  // 除翻页之外的其他参数
  const [ticketListParams, setTicketListParams] = useState<Record<string, any>>(
    {
      type: 1 //默认为1
    }
  )
  // table的内容数据
  const [tableData, setTableData] = useState<Record<string, any>>({})
  // 侧边导航数据
  const [navData, setNavData] = useState<NavListType[]>([])
  // 筛选项数据
  const [filterData, setFilterData] = useState<any>({})
  // 日期格式
  const dateFormat = 'YYYY/MM/DD'
  // all assignee的value
  const [allAssigneeValue, setAllAssigneeValue] = useState<string | undefined>()

  useEffect(() => {
    if (!location.pathname.includes('ticketDetail')) {
      postTicketList({
        ...childPagination,
        ...ticketListParams,
        timeZone
      }).then((res: any) => {
        console.log(res)
        setTableData({
          records: res.records,
          total: res.total,
          current: res.current,
          size: res.size
        })
        setNavData(res.extraData)
      })

      getTicketFilter()
        .then((res: any) => {
          console.log(res)
          setFilterData(res)
        })
        .catch((err) => {
          console.log(err)
        })
    }
  }, [childPagination, ticketListParams])

  // 当table的翻页数据发生变化时、筛选项发生变化时，重新请求table数据
  // useEffect(() => {
  //   if (!location.pathname.includes('ticketDetail')) {
  //     postTicketList({
  //       ...childPagination,
  //       ...ticketListParams
  //     }).then((res: any) => {
  //       console.log(res)
  //       setTableData({
  //         records: res.records,
  //         total: res.total
  //       })
  //     })
  //   }
  // }, [childPagination, ticketListParams])

  return (
    <>
      {location.pathname.includes('ticketDetail') ? (
        <Outlet />
      ) : (
        <div className={styles.box}>
          {contextHolder}
          {/* 侧边栏组件 */}
          <NavBar
            getSelect={getChildSelect}
            navData={navData}
            tabIndex={childSelect}
          />

          <div className={styles.right}>
            {/* 筛选项 */}
            <div className={styles.filter}>
              <Space wrap size={12}>
                <Select
                  placeholder="All site"
                  size="large"
                  fieldNames={{ label: 'name', value: 'value' }}
                  options={filterData.allSite}
                  allowClear
                  onChange={(value: string) => {
                    setTicketListParams({
                      ...ticketListParams,
                      site: value
                    })
                    setChildPagination((prev) => ({
                      ...prev,
                      current: 1
                    }))
                  }}
                />

                {/* 当侧边栏选择Unassignment tickets和My open tickets时，隐藏这个筛选项 */}
                {childSelect === 0 || childSelect === 1 ? null : (
                  <Select
                    placeholder="All assignee"
                    size="large"
                    fieldNames={{ label: 'name', value: 'idStr' }}
                    options={filterData.allAssignee}
                    allowClear
                    onChange={(value: string) => {
                      setAllAssigneeValue(value)
                      setTicketListParams({
                        ...ticketListParams,
                        customerServiceId: value
                      })
                      setChildPagination((prev) => ({
                        ...prev,
                        current: 1
                      }))
                    }}
                    defaultValue={allAssigneeValue}
                  />
                )}

                <Select
                  placeholder="All rate"
                  size="large"
                  fieldNames={{ label: 'name', value: 'value' }}
                  options={filterData.allRate}
                  allowClear
                  onChange={(value: number) => {
                    setTicketListParams({
                      ...ticketListParams,
                      rate: value
                    })
                    setChildPagination((prev) => ({
                      ...prev,
                      current: 1
                    }))
                  }}
                />

                <RangePicker
                  size="large"
                  format={dateFormat}
                  allowClear
                  onChange={(_, dateStrings: [string, string]) => {
                    console.log(dateStrings)
                    setChildPagination({
                      ...childPagination,
                      current: 1,
                      size: 10
                    })
                    setTicketListParams({
                      ...ticketListParams,
                      searchDate: dateStrings,
                      timeZone
                    })
                  }}
                />
                {childSelect === 5 && (
                  <Select
                    placeholder="All Status"
                    size="large"
                    fieldNames={{ label: 'name', value: 'id' }}
                    options={filterData.ticketStatusList}
                    allowClear
                    onChange={(value: number) => {
                      setTicketListParams({
                        ...ticketListParams,
                        ticketStatus: value
                      })
                      setChildPagination((prev) => ({
                        ...prev,
                        current: 1
                      }))
                    }}
                  />
                )}
                <Search
                  placeholder="Search"
                  size="large"
                  style={{ width: '280px' }}
                  allowClear
                  onSearch={(value: string) => {
                    setTicketListParams({
                      ...ticketListParams,
                      keyWorld: value
                    })
                    setChildPagination((prev) => ({
                      ...prev,
                      current: 1
                    }))
                  }}
                />
              </Space>
              <div className={styles.buttonBox}>
                {isMerge ? (
                  <Button size="large" onClick={handleCancel}>
                    Cancel
                  </Button>
                ) : (
                  ''
                )}
                <Button type="primary" size="large" onClick={handleMerge}>
                  {isMerge ? 'Merging' : 'Message Merging'}
                </Button>
              </div>
            </div>

            {/* 表格组件 */}
            <TicketTable
              isMerge={isMerge}
              tableData={tableData}
              getMergeData={getChildMergeData}
              getPagination={getPagination}
            />
            {/* 合并ticket确认框 */}
            <Modal
              open={isModalOpen}
              className={styles.modal}
              okText="Yes"
              cancelText="No"
              onOk={() => {
                setIsMergeLoading(true)
                setIsModalOpen(false)
                setIsMerge(false)
                setIsMergeLoading(false)
                console.log(childMergeData)
                putMergeTicket({ idList: childMergeData }).then((res: any) => {
                  console.log(res)
                  if (res.result) {
                    messageApi.open({
                      type: 'success',
                      content: 'Message Merged Successfully'
                    })
                    setTicketListParams({
                      ...ticketListParams
                    })
                  } else {
                    messageApi.open({
                      type: 'error',
                      content: res.message
                    })
                  }
                })
              }}
              onCancel={() => setIsModalOpen(false)}
              confirmLoading={isMergeLoading}
            >
              <Space size={12}>
                <i
                  className="iconfont icon-tishi-jingshi"
                  style={{ fontSize: '20px', color: '#ECDA3D' }}
                ></i>
                <span className={styles.modalFont}>
                  Are You Sure You Want To Merge It?
                </span>
              </Space>
            </Modal>
          </div>
        </div>
      )}
    </>
  )
}
