import React from 'react'
import { Popover } from 'antd'
import FsAvatar from '@/component/FsAvatar'
import style from './avatarList.module.scss'

type DataProps = {
  list: Array<{
    avatar: string
    id: number
    name: string
    status: number
  }>
}

function AavatarList(props: DataProps) {
  const { list } = props
  const memberListHtml = (
    <div className={style.member_list_wrap}>
      {list.map((item) => (
        <div className={style.item_wrap} key={item.id}>
          <FsAvatar src={item.avatar} onlineStatus={item.status} />
          <span>{item.name}</span>
        </div>
      ))}
    </div>
  )
  return (
    <Popover title={memberListHtml}>
      <div className={style.avater_container}>
        <>
          {list.map((item) => (
            <FsAvatar
              src={item.avatar}
              customClassName={style.fs_avatar_cs}
              key={item.id}
            ></FsAvatar>
          ))}
          {list.length >= 5 ? (
            <span className={style.cover}>+{list.length}</span>
          ) : null}
        </>
      </div>
    </Popover>
  )
}

export default AavatarList
