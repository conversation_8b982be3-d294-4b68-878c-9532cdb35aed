.avater_container {
  display: flex;
  position: relative;
  width: fit-content;
  .fs_avatar_cs {
    width: 42px;
    height: 42px;
    &:not(:first-child) {
      margin-left: -12px;
    }
    &:last-child {
      cursor: default;
    }
  }
  .cover {
    cursor: pointer;
    position: absolute;
    top: 0;
    right: 0;
    width: 42px;
    height: 42px;
    border-radius: 50%;
    text-align: center;
    line-height: 42px;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1;
  }
}
.member_list_wrap {
  display: flex;
  width: 270px;
  flex-wrap: wrap;
  .item_wrap {
    display: flex;
    background-color: #eeeeee;
    border-radius: 17px;
    margin: 0 10px 10px 0;
    align-items: center;
    padding-right: 10px;
    span {
      margin-left: 10px;
    }
  }
}
