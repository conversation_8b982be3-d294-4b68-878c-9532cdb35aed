import React, { useEffect, useMemo, useState } from 'react'
import style from './index.module.scss'
import { Checkbox, Input, Modal } from 'antd'
import type { CheckboxChangeEvent } from 'antd/es/checkbox'
import FsAvatar from '@/component/FsAvatar'
import { CheckboxValueType } from 'antd/es/checkbox/Group'

type PropsType = {
  show: boolean
  hideModal: (show: boolean) => void
  list: Array<Record<string, any>>
  onMemberSelect?: (val: Array<CheckboxValueType>) => void
  selectedList?: Array<Record<string, any>>
}
const GroupModal = (props: PropsType) => {
  const { show, hideModal, list, onMemberSelect, selectedList } = props
  const [valList, setValList] = useState<CheckboxValueType[]>([])
  const [checkAll, setCheckAll] = useState(false)
  const [searchList, setSearchList] = useState<Record<string, any>[]>([])

  // 全选
  const [indeterminate, setIndeterminate] = useState(false)

  const handleChange = (val: CheckboxValueType[]) => {
    const listVal = list.map((item) => item.id)
    setValList(val)
    console.log(!!val.length && val.length < listVal.length)
    setIndeterminate(!!val.length && val.length < listVal.length)
    setCheckAll(val.length === listVal.length)
  }
  const handleComfirm = () => {
    onMemberSelect(valList)
    hideModal(false)
  }
  const onCheckAllChange = (e: CheckboxChangeEvent) => {
    const listVal = list.map((item) => item.id)
    setValList(e.target.checked ? listVal : [])
    setIndeterminate(false)
    setCheckAll(e.target.checked)
  }
  const filterList = useMemo(() => {
    if (searchList.length > 0) {
      return searchList
    } else {
      return list
    }
  }, [searchList, list])
  console.log('-----render---:', filterList)
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log(e.target.value)
    const val = e.target.value
    // const searchResult = list.filter((item) =>
    //   item.name.toLowerCase().includes(val.toLowerCase())
    // )
    if (val) {
      const searchResult = list.map((item) => {
        if (item.name.toLowerCase().includes(val.toLowerCase())) {
          item.isSearch = true
          return item
        } else {
          item.isSearch = false
          return item
        }
      })
      setSearchList(searchResult)
    } else {
      searchList.length > 0 && setSearchList([])
    }
  }

  useEffect(() => {
    const idlist = selectedList.map((item) => item.id)
    if (idlist.length) {
      setIndeterminate(true)
    }
    setValList(idlist)
  }, [selectedList, list])

  return (
    <Modal
      open={show}
      width={400}
      title="Select Member"
      centered
      onCancel={() => hideModal(false)}
      onOk={handleComfirm}
      okText="Confirm"
    >
      <div className={style.modal_container}>
        <div className={style.input_search}>
          <Input
            placeholder="Please enter customer service name to search"
            onChange={handleSearch}
            allowClear
          ></Input>
        </div>
        <div className={style.checkbox_group_wrap}>
          <div className={style.title}>
            <Checkbox
              indeterminate={indeterminate}
              onChange={onCheckAllChange}
              checked={checkAll}
            >
              Select All
            </Checkbox>
            <div>Primary Group</div>
          </div>
          <div className={style.checkbox_group}>
            <Checkbox.Group onChange={handleChange} value={valList}>
              {filterList.map((item) => (
                <div
                  className={style.checkbox_item}
                  key={item.id}
                  style={{
                    display:
                      searchList.length > 0 && !item.isSearch ? 'none' : ''
                  }}
                >
                  <Checkbox value={item.id}>
                    <div className={style.label}>
                      <FsAvatar
                        src={item.avatar}
                        style={{ width: '24px', height: '24px' }}
                      ></FsAvatar>
                      <span>{item.name}</span>
                    </div>
                  </Checkbox>
                  <div className={style.group_name}>
                    <span>{item.groups.length > 0 && item.groups[0].name}</span>
                  </div>
                </div>
              ))}
            </Checkbox.Group>
          </div>
        </div>
      </div>
    </Modal>
  )
}

export default React.memo(GroupModal)
