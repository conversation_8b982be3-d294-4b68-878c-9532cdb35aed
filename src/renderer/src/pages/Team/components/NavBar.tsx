import React, { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import style from '../index.module.scss'

type PropsType = {
  getNavBarChange: (id: number) => void
  totalCount: {
    manageCount: number
    groupCount: number
  }
}
const NavBar = (props: PropsType) => {
  const navigate = useNavigate()
  const [active, setActive] = useState(1)
  const [navList, setNavList] = useState([
    {
      name: 'Team',
      id: 1,
      number: 0
    },
    {
      name: 'Group',
      id: 2,
      number: 0
    }
  ])
  const { getNavBarChange, totalCount } = props
  const handlerNavChange = (id: number) => {
    navigate('/team')
    getNavBarChange(id)
    setActive(id)
  }
  useEffect(() => {
    setNavList((pre) => {
      pre[0].number = totalCount.manageCount
      pre[1].number = totalCount.groupCount
      return [...pre]
    })
  }, [totalCount])
  return (
    <div className={style.nav_bar_wrap}>
      <div className={style.nav_bar_content}>
        <div className={style.title}>Team</div>
        <div className={style.nav_list}>
          {navList.map((item) => (
            <div
              className={[
                style['list_item'],
                active === item.id ? style['active_list'] : ''
              ].join(' ')}
              key={item.id}
              onClick={() => handlerNavChange(item.id)}
            >
              <div>{item.name}</div>
              <div>{item.number}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default React.memo(NavBar)
