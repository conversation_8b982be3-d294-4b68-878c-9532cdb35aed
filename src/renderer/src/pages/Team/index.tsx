import React, { lazy, Suspense, useEffect, useState } from 'react'
import style from './index.module.scss'
import NavBar from './components/NavBar'
import { Spin } from 'antd'
import { getResourceCount } from '@/api/team'

const modules = import.meta.glob('../Team/*/*.tsx') // import.meta.glob的方式导入组件
// 将 lazy 组件声明在组件外部
const CustomerCom = lazy(modules['./CustomerService/index.tsx'] as any)
const GroupCom = lazy(modules['./Group/index.tsx'] as any)

const Team = () => {
  const [activeTab, setActiveTab] = useState<number>(1)
  const [totalCount, setTotalCount] = useState({
    groupCount: 0,
    manageCount: 0
  })
  const getNavChange = (id: number) => {
    setActiveTab(id)
  }

  const fetchResourceCount = async () => {
    try {
      const data: any = await getResourceCount()

      setTotalCount({
        ...totalCount,
        groupCount: data.groupCount,
        manageCount: data.manageCount
      })
    } catch (error) {
      // setTotalCount({ manageCount: 0, groupCount: 0})
    }
  }

  useEffect(() => {
    fetchResourceCount()
  }, [])

  return (
    <div className={style.team_wrap}>
      <NavBar getNavBarChange={getNavChange} totalCount={totalCount} />
      <div className={style.page_wrap}>
        {/* 动态懒加载组件 */}
        <Suspense
          fallback={
            <div
              style={{
                width: '100%',
                height: '100%',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center'
              }}
            >
              <Spin spinning={true} />
            </div>
          }
        >
          {activeTab === 1 ? (
            <CustomerCom></CustomerCom>
          ) : (
            <GroupCom></GroupCom>
          )}
        </Suspense>
      </div>
    </div>
  )
}

export default React.memo(Team)
