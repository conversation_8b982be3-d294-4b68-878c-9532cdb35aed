import { ChangeEvent, useEffect, useRef, useState } from 'react'
import { Outlet, useLocation, useNavigate } from 'react-router-dom'
import style from './index.module.scss'
import {
  Space,
  Select,
  Input,
  Button,
  Popover,
  Pagination,
  Modal,
  Radio,
  PaginationProps
} from 'antd'
import type { RadioChangeEvent } from 'antd'
import FsTable from '../../Settings/components/FsTable'
import FsAvatar from '@/component/FsAvatar'
import { siteColorMap } from '@/utils/util'
import {
  delManageInfo,
  getManageList,
  getManageListOption,
  getManageStatus,
  getManageStatusList,
  putManageStatus
} from '@/api/team'
import { debounce } from 'lodash'
import useSendMSg from '@renderer/hooks/useSendMsg'
const { Option } = Select
const SearchIcon = (
  <i className="iconfont icon-searchsousuo" style={{ color: '#D8D8D8' }}></i>
)
type DataType = {
  id: number
  name: string
  email: string
  avatar: string
  chatLimit: number // 客服会话上限
  remainingChat: number //客服可接待人数
  status: number //客服状态 1:在线 2:离线 3:忙碌
  groups: Array<{ id?: number; name?: string; primary?: boolean }>
  actions: {
    delete: boolean
    edit: boolean
  }
  roleName: string //客服角色名称
  receptionType: string // 客服类型名称
  createdAt: string
  permissionName: string
  permissions: any[]
  userId: string
}

// type FilterData = {
//   groupId?: undefined | number
//   receptionTypeId?: undefined | number
//   search?: string
//   statusId?: undefined | number
// }

const columns = [
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
    render: (_: string, record: DataType) => {
      return (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <FsAvatar
            src={record.avatar}
            onlineStatus={record.status}
            style={{ width: 28, height: 28 }}
          ></FsAvatar>
          <span style={{ marginLeft: '4px', flex: 1 }}>{record.name}</span>
        </div>
      )
    }
  },
  {
    title: 'Email',
    dataIndex: 'email',
    key: 'email'
  },
  {
    title: 'Role',
    dataIndex: 'roleName',
    key: 'roleName'
  },
  {
    title: 'Permissions',
    dataIndex: 'receptionType',
    key: 'receptionType',
    render: (_: string, record: DataType) => {
      return <p>{record.permissionName}</p>
    }
  },
  {
    title: 'Chat Limit',
    dataIndex: 'chatLimit',
    key: 'chatLimit'
  },
  {
    title: 'Remaining Chats',
    dataIndex: 'remainingChat',
    key: 'remainingChat'
  },
  {
    title: 'Group',
    dataIndex: 'groups',
    key: 'groups',
    render: (_: string, record: DataType) => {
      const commonCom = (sIndex?: number, eIndex?: number) => {
        if (sIndex !== undefined) {
          return record.groups.slice(sIndex, eIndex).map((item) => (
            <div className={style.group_item} key={item.id}>
              <span
                style={{ background: siteColorMap[item.name.toLowerCase()] }}
                className={style.site_icon}
              >
                {item.name.slice(0, 1)}
              </span>
              <span>{item.name}</span>
            </div>
          ))
        } else {
          return (
            <div className={style.group_popover_wrap}>
              <div className={style.content}>
                <div className={style.title}>Primary</div>
                {record.groups
                  .filter((item) => item.primary === true)
                  .map((item) => (
                    <div className={style.group_item} key={item.id}>
                      <span
                        style={{
                          background: siteColorMap[item.name.toLowerCase()]
                        }}
                        className={style.site_icon}
                      >
                        {item.name.slice(0, 1)}
                      </span>
                      <span>{item.name}</span>
                    </div>
                  ))}
              </div>
              <div className={style.content}>
                <div className={style.title}>Secondary</div>
                {record.groups
                  .filter((item) => item.primary === false)
                  .map((item) => (
                    <div className={style.group_item} key={item.id}>
                      <span
                        style={{
                          background: siteColorMap[item.name.toLowerCase()]
                        }}
                        className={style.site_icon}
                      >
                        {item.name.slice(0, 1)}
                      </span>
                      <span>{item.name}</span>
                    </div>
                  ))}
              </div>
            </div>
          )
          // return record.groups.map((item) => (
          //   <div className={style.group_item} key={item.id}>
          //     <span
          //       style={{ background: siteColorMap[item.name.toLowerCase()] }}
          //       className={style.site_icon}
          //     >
          //       {item.name.slice(0, 1)}
          //     </span>
          //     <span>{item.name}</span>
          //   </div>
          // ))
        }
      }

      return (
        <div className={style.group_wrap}>
          {record.groups.length > 2 ? (
            <>
              {commonCom(0, 2)}
              <Popover
                content={commonCom()}
                getPopupContainer={(triggerNode) =>
                  triggerNode.parentElement || document.body
                }
                placement="bottom"
              >
                <span className={style.showmore_icon}>Show More</span>
              </Popover>
            </>
          ) : (
            record.groups.map((item) => (
              <div className={style.group_item} key={item.id}>
                <span
                  style={{ background: siteColorMap[item.name.toLowerCase()] }}
                  className={style.site_icon}
                >
                  {item.name.slice(0, 1)}
                </span>
                <span>{item.name}</span>
              </div>
            ))
          )}
          {record.groups.length === 0 && '/'}
        </div>
      )
    }
  },
  {
    title: 'Creat Date',
    dataIndex: 'createdAt',
    key: 'createdAt'
  }
]

const CustomerService = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [tableLoading, setTableLoading] = useState(false)
  const [tableData, setTableData] = useState<DataType[]>([])
  const [paginationParams, setPaginationParams] = useState<{
    current: number
    size: number
  }>({ current: 1, size: 10 })
  const [filterOptionParams, setFilterOptionParams] = useState<
    Record<string, any>
  >({
    groupId: undefined,
    roleId: undefined,
    search: undefined,
    statusId: undefined,
    permissionId: undefined
  })
  const [filterOption, setFilterOption] = useState({
    addManagePermission: false,
    status: [],
    groups: [],
    roles: [],
    permissions: []
  })
  const [totalNum, setTotalNum] = useState(10)
  const [manageId, setManageId] = useState(0)
  const [manageStatusVal, setManageStatusVal] = useState(1)
  const userId = useRef('')
  const { sendMsg } = useSendMSg()
  //初始化
  useEffect(() => {
    let timer = null
    if (location.pathname.includes('/team')) {
      timer = setInterval(() => {
        updateManageStatus()
      }, 5000)
    }
    return () => {
      clearInterval(timer)
    }
  }, [location.pathname])
  useEffect(() => {
    if (location.pathname.includes('/team')) {
      fetchManageList()
      fetchManageListOption()
    }
  }, [paginationParams, filterOptionParams, location.pathname])

  const fetchManageList = async () => {
    // 请求列表数据
    try {
      setTableLoading(true)
      const params = {
        ...paginationParams,
        ...filterOptionParams
      }
      const data: any = await getManageList(params)
      console.log(data.records)
      setTotalNum(data.total)
      setTableLoading(false)
      setTableData(data.records)
    } catch (e) {
      setTableLoading(false)
    }
  }
  const updateManageStatus = async () => {
    const data: any = await getManageStatusList()
    setTableData((prevData) => {
      const newData = prevData.map((item) => {
        const findItem = data.find((i: any) => i.id === item.id)
        if (findItem) {
          item.status = findItem.status
        }
        return item
      })
      return newData
    })
  }
  const fetchManageListOption = async () => {
    // 请求列表数据
    try {
      const data: any = await getManageListOption()
      setFilterOption(data)
    } catch (e) {
      setTableLoading(false)
    }
  }
  const customerListChange: PaginationProps['onChange'] = (page, pageSize) => {
    setPaginationParams((pre) => ({ ...pre, current: page, size: pageSize }))
  }

  const editStatus = async (id: number, userid: string) => {
    const data: any = await getManageStatus(id)
    setManageStatusVal(data.status)
    userId.current = userid
    setManageId(id)
    setIsModalOpen(true)
  }
  const setManageStatus = async () => {
    const data = await putManageStatus(manageId, manageStatusVal).finally(() =>
      setIsModalOpen(false)
    )
    sendMsg(
      { userId: userId.current, status: manageStatusVal },
      { messageType: 73 }
    )
    fetchManageList()
  }
  const handleChangeStatus = (e: RadioChangeEvent) => {
    setManageStatusVal(e.target.value)
  }

  const statusSelectChange = (statusId: number) => {
    setPaginationParams({ ...paginationParams, current: 1 })
    setFilterOptionParams({ ...filterOptionParams, statusId })
  }

  const groupSelectChange = (groupId: number) => {
    setPaginationParams({ ...paginationParams, current: 1 })
    setFilterOptionParams({ ...filterOptionParams, groupId })
  }

  const roleSelectChange = (roleId: number) => {
    setPaginationParams({ ...paginationParams, current: 1 })
    setFilterOptionParams({ ...filterOptionParams, roleId })
  }

  const permissionsChange = (permissionId: number) => {
    setPaginationParams({ ...paginationParams, current: 1 })
    setFilterOptionParams({ ...filterOptionParams, permissionId })
  }
  const searchChange = debounce((e: ChangeEvent<HTMLInputElement>) => {
    console.log(e.target.value)
    setPaginationParams({ ...paginationParams, current: 1 })
    setFilterOptionParams({ ...filterOptionParams, search: e.target.value })
  }, 500)
  const delManageItem = (id: number) => {
    Modal.confirm({
      title: 'Tip',
      closable: true,
      content: (
        <div style={{ color: '#262626' }}>
          Are you sure want to delete this agent?
          <div style={{ marginTop: '4px', fontSize: '12px', color: '#595959' }}>
            The operation is irreversible, please operate with caution.
          </div>
        </div>
      ),
      onOk: async () => {
        await delManageInfo(id)
        fetchManageList()
      }
    })
  }

  return location.pathname.includes('addCustomer') ? (
    <Outlet />
  ) : (
    <div className={style.customer_wrap}>
      {/* 头部筛选 */}
      <div className={style.head_filter}>
        <Space>
          <Select
            size="large"
            style={{ width: 150 }}
            value={filterOptionParams.statusId}
            placeholder="All Status"
            onChange={statusSelectChange}
            allowClear
          >
            {filterOption.status.map((item: any) => (
              <Option value={item.id} key={item.id}>
                {item.value}
              </Option>
            ))}
          </Select>
          <Select
            size="large"
            style={{ width: 150 }}
            value={filterOptionParams.groupId}
            placeholder="All Group"
            onChange={groupSelectChange}
            allowClear
          >
            {filterOption.groups.map((item: any) => (
              <Option value={item.id} key={item.id}>
                {item.name}
              </Option>
            ))}
          </Select>
          <Select
            size="large"
            style={{ width: 150 }}
            value={filterOptionParams.receptionTypeId}
            placeholder="All Role"
            onChange={roleSelectChange}
            allowClear
          >
            {filterOption.roles.map((item: any) => (
              <Option value={item.id} key={item.id}>
                {item.value}
              </Option>
            ))}
          </Select>
          <Select
            size="large"
            style={{ width: 150 }}
            placeholder="All permission"
            allowClear
            onChange={permissionsChange}
          >
            {filterOption.permissions.map((item: any) => (
              <Option value={item.id} key={item.id}>
                {item.value}
              </Option>
            ))}
          </Select>
          <Input
            suffix={SearchIcon}
            size="large"
            placeholder="Name/Email"
            style={{ width: 230 }}
            onChange={searchChange}
            allowClear
          />
        </Space>
        {filterOption.addManagePermission && (
          <Button
            type="primary"
            onClick={() => {
              navigate('/Team/addCustomer')
            }}
          >
            + Add Account
          </Button>
        )}
      </div>
      {/* table部分 */}
      <div className={style.table_wrap}>
        <FsTable
          data={tableData}
          loading={tableLoading}
          columns={[
            ...columns,
            {
              title: 'Action',
              key: '',
              render: (_: string, record: DataType) => {
                return (
                  record.permissions.length > 0 && (
                    <Space size="middle">
                      {record.permissions.map((item) => {
                        if (item.name === 'status' && item.status) {
                          return (
                            <i
                              className="icon-bianjizhuangtai iconfont"
                              onClick={() =>
                                editStatus(record.id, record.userId)
                              }
                              key={item.name}
                            ></i>
                          )
                        } else if (item.name === 'edit' && item.status) {
                          return (
                            <i
                              className="icon-bianjixinxi iconfont"
                              onClick={() =>
                                navigate(`/Team/addCustomer?id=${record.id}`)
                              }
                              key={item.name}
                            ></i>
                          )
                        } else if (item.name === 'delete' && item.status) {
                          return (
                            <i
                              className="icon-shanchu iconfont"
                              onClick={() => delManageItem(record.id)}
                              key={item.name}
                            ></i>
                          )
                        }
                      })}
                    </Space>
                  )
                )
              }
            }
          ]}
        ></FsTable>
      </div>
      <div className={style.pagination}>
        <div>Total {totalNum} items</div>
        <Pagination
          current={paginationParams.current}
          pageSize={paginationParams.size}
          pageSizeOptions={['5', '10', '20', '50']}
          showQuickJumper
          showSizeChanger
          total={totalNum}
          onChange={customerListChange}
          hideOnSinglePage={true}
        />
      </div>
      <Modal
        title="Edit status"
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        onOk={setManageStatus}
      >
        <div>
          <Radio.Group value={manageStatusVal} onChange={handleChangeStatus}>
            <Space direction="vertical">
              <Radio value={1}>Accepting</Radio>
              <Radio value={2}>Busy</Radio>
            </Space>
          </Radio.Group>
        </div>
      </Modal>
    </div>
  )
}

export default CustomerService
