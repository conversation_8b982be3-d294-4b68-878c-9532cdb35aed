.customer_wrap {
  display: flex;
  flex-direction: column;
  height: 100%;
  .head_filter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 0 0;
  }
  .table_wrap {
    background-color: #fff;
    margin-top: 20px;
    padding: 0 20px;
    flex: 1;
    .group_wrap {
      box-sizing: border-box;
      .group_popover_wrap {
        display: flex;
        align-items: flex-start;
        .content {
          min-width: 85px;
          padding: 0 16px;
          .title {
            @include font14;
            font-weight: 500;
            margin-bottom: 14px;
          }
          &:last-child {
            border-left: 1px solid #fafafa;
          }
        }
      }
      .group_item {
        font-size: 12px;
        .site_icon {
          width: 16px;
          height: 16px;
          line-height: 16px;
          border-radius: 2px;
          display: inline-flex;
          justify-content: center;
          align-items: center;
          color: #fff;
          margin-right: 4px;
        }
      }
      .showmore_icon {
        color: #4b7eff;
        cursor: pointer;
      }
    }
    i {
      cursor: pointer;
    }
    :global {
      .ant-table-thead {
        tr > th {
          background-color: #fff;
        }
      }
      .table_row {
        &:nth-child(2n) {
          td {
            background-color: #fafafa;
          }
        }
      }
    }
  }
  .pagination {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 24px 20px;
    background-color: #fff;
    box-shadow: 0px -3px 8px 0px rgba(0, 0, 0, 0.13);
  }
}
