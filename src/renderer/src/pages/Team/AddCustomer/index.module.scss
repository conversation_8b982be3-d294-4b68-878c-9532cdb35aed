.customer_form_wrap {
  height: 100%;
  background: #fff;
  display: flex;
  flex-direction: column;

  .form_content {
    flex: 1;
    padding: 32px;
    overflow: auto;

    .basic_info {
      padding-bottom: 32px;
      border-bottom: 1px solid #e4e7ed;

      &:not(:first-child) {
        padding-top: 32px;
      }

      &:last-child {
        border: none;
      }

      .title {
        margin-bottom: 24px;
        font-size: 18px;
        font-weight: 500;
        line-height: 26px;
        color: #262626;
      }

      .avatar_wrap {
        display: flex;
        align-items: center;
        cursor: pointer;

        .avatar_img {
          position: relative;

          img {
            width: 42px;
            height: 42px;
            border-radius: 50%;
            display: block;
          }

          .loading {
            position: absolute;
            top: 0;
            left: 0;
            width: 42px;
            height: 42px;
            background-color: #fff;
            border: 2px solid #000;
            border-top-color: rgba(0, 0, 0, 0.2);
            border-right-color: rgba(0, 0, 0, 0.2);
            border-bottom-color: rgba(0, 0, 0, 0.2);
            border-radius: 100%;
            animation: circle infinite 0.75s linear;
          }

          @keyframes circle {
            0% {
              transform: rotate(0);
            }
            100% {
              transform: rotate(360deg);
            }
          }
        }

        .avatar_info {
          margin-left: 8px;

          .title {
            font-weight: 500;
            line-height: 22px;
            color: #262626;
            margin-bottom: 4px;
          }

          .text {
            font-size: 12px;
            line-height: 20px;
            color: #595959;
          }
        }
      }

      .username_wrap {
        margin-top: 24px;
        display: flex;

        i {
          display: block;
          align-self: flex-end;
          width: 24px;
          height: 24px;
          font-size: 24px;
          margin: 0 22px 8px 22px;
          color: #c0c4cc;
        }
      }

      .email_wrap {
        margin-top: 20px;
      }

      .input_item_wrap {
        width: 468px;
        display: flex;
        flex-direction: column;

        label {
          margin-bottom: 8px;
          @include font14;
          color: #262626;
        }

        .label_required {
          &::before {
            display: inline-block;
            margin-inline-end: 4px;
            color: #ff4d4f;
            font-size: 14px;
            font-family: SimSun, sans-serif;
            line-height: 1;
            content: '*';
          }
        }
      }

      .role_wrap {
        display: flex;
        gap: 20px;
        position: relative;
        .role_item {
          display: flex;
          align-items: flex-start;
          padding: 20px;
          border: 1px solid #d9d9d9;
          cursor: pointer;

          &:hover {
            border-color: #345dd9;
          }

          .icon > i {
            width: 20px;
            height: 20px;
            font-size: 20px;
          }

          .role_info {
            max-width: 277px;
            margin-left: 12px;

            .title {
              @include font14;
              color: #262626;
              margin-bottom: 4px;
            }

            .desc {
              @include font12;
              color: #8c8c8c;
            }
          }
        }

        .role_active {
          background: #f5f9ff;
          border-color: #345dd9;
        }

        .role_disabled {
          position: absolute;
          width: 100%;
          height: 100%;
          left: 0;
          top: 0;
          background: rgba(255, 255, 255, 0.6);
          z-index: 1;
          cursor: not-allowed;
        }
      }
      .new_receive {
        display: flex;
        gap: 16px;
      }
      .permission_wrap,
      .association {
        .checkbox_label {
          span {
            display: block;
            @include font12;
            color: #8c8c8c;

            &:first-child {
              @include font14;
              color: #262626;
              margin-bottom: 4px;
            }
          }
        }
      }

      .group_member_wrap {
        .group_wrap {
          display: flex;
          flex-wrap: wrap;
          margin-top: 16px;
          gap: 24px;

          .group_item {
            position: relative;
            display: flex;
            align-items: baseline;
            padding: 8px 10px 12px 12px;
            border-radius: 4px;
            background: #fafafa;

            i {
              position: absolute;
              top: 14px;
              right: 10px;
              width: 12px;
              height: 12px;
              font-size: 12px;
            }

            .group_icon {
              width: 16px;
              height: 16px;
              border-radius: 2px;
              display: flex;
              justify-content: center;
              align-items: center;
              color: #fff;
            }

            .group_info {
              margin-left: 6px;

              .title {
                display: block;
                @include font14;
                color: #262626;
              }

              .set_primary_wrap {
                margin-top: 8px;
                width: 172px;
                display: flex;
                justify-content: space-between;

                & > span {
                  @include font12;
                  color: #8c8c8c;
                }
              }

              .reception {
                margin-top: 8px;
                padding-top: 8px;
                border-top: 1px solid #e4e7ed;
                display: flex;
                flex-direction: column;

                & > .title {
                  @include font12;
                  color: #8c8c8c;
                  margin-bottom: 8px;
                }
              }
            }
          }
        }
      }

      .input_number_wrap {
        display: flex;
        align-items: center;
        gap: 12px;

        .input_number {
          display: flex;
          align-items: center;

          .btn {
            width: 32px;
            height: 32px;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            background: #f4f4f5;
            border: 1px solid #abb9ce;
            border-radius: 2px 0px 0px 2px;
            cursor: pointer;

            &:last-of-type {
              border-radius: 0px 2px 2px 0px;
            }

            i {
              width: 12px;
              height: 12px;
              font-size: 12px;
            }
            &.btn_disabled {
              cursor: not-allowed;
            }
          }

          .num_window {
            width: 72px;
            height: 32px;
            text-align: center;
            line-height: 32px;
            border: 1px solid #dcdfe6;
          }
        }

        .desc {
          @include font12;
          color: #8c8c8c;
        }
      }
    }
  }

  .form_footer {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    padding: 24px 32px;
    box-shadow: 0px -3px 8px 0px rgba(0, 0, 0, 0.13);
  }
}
