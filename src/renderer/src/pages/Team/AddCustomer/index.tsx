import { useEffect, useMemo, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import style from './index.module.scss'
import {
  Button,
  Input,
  Select,
  Space,
  Checkbox,
  Switch,
  Radio,
  Upload,
  message,
  Spin
} from 'antd'
import type { RadioChangeEvent } from 'antd'
import type { CheckboxChangeEvent } from 'antd/es/checkbox'
import {
  getEmployeeList,
  getEmployeeIsAlready,
  getCreateCustomerOption,
  postCreateCustomer,
  getCustomerDetails,
  putEditCustomer,
  getCheckAssistant
} from '@/api/team'
import { BaseOptionType, SelectProps } from 'antd/es/select'
import { siteColorMap } from '@/utils/util'
import { uploadFile } from '@/api/common'
import { RcFile } from 'antd/es/upload'
import FsAvatar from '@/component/FsAvatar'
import defaultAvatar from '@/assets/image/live-chat-logo.svg'

const { Option } = Select

const getBase64 = (img: RcFile, callback: (url: string) => void) => {
  const reader = new FileReader()
  reader.addEventListener('load', () => callback(reader.result as string))
  reader.readAsDataURL(img)
}
const options: SelectProps['options'] = []

for (let i = 10; i < 36; i++) {
  options.push({
    label: i.toString(36) + i,
    value: i.toString(36) + i
  })
}

type EmployeeInfoType = {
  avatar: string
  email: string
  employeeId: number
  name?: string
  updateName?: string
  isReception?: boolean
  isReceptionAll?: boolean
  limit?: number
  loginTypeId?: number
  permissionId?: number
  roleId?: number
  openId?: string
}
// Role列表
const roleIconList = [
  {
    icon: 'icon-a-CustomerService'
  },
  {
    icon: 'icon-Technologist'
  },
  {
    icon: 'icon-a-Salesspecialist'
  },
  {
    icon: 'icon-Visitor'
  }
]

const AddCustomer = () => {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  console.log('searchParams', searchParams.get('id'))

  const [listData, setListData] = useState([])
  const [roleId, setRoleId] = useState(1)
  const [imgLoading, setImgLoading] = useState(false)
  // 选择的主人员信息
  const [selectEmployeeInfo, setSelectEmployeeInfo] =
    useState<EmployeeInfoType>({
      avatar: '',
      email: '',
      employeeId: 0,
      name: '',
      updateName: '',
      roleId: 1,
      openId: '',
      loginTypeId: 1,
      permissionId: 1
    })
  const [loginPermissionId, setLoginPermissionId] = useState(0)
  // 聊天数量限制
  const [chatsLimit, setChatsLimit] = useState(3)
  // 创建客服的选项
  const [createCustomerOption, setCreateCustomerOption] = useState<any>({})
  const handleFormPageCancel = () => {
    navigate(-1)
  }
  const handleSelectChange = (val: string, option: BaseOptionType) => {
    console.log('selected', val, option)
    checkEmployeeIsAlready(Number(val))
      .then(() => {
        setSelectEmployeeInfo({
          ...selectEmployeeInfo,
          avatar: option.avatar,
          email: option.email,
          employeeId: option.id,
          name: option.nickname,
          updateName: '',
          openId: option.openId
        })
      })
      .catch((e) => {
        setSelectEmployeeInfo({
          ...selectEmployeeInfo,
          name: ''
        })
      })
  }

  const ChangeChatLimit = (type: string) => {
    if (loginPermissionId === 3) return
    switch (type) {
      case 'sub':
        if (chatsLimit === 1) return
        setChatsLimit((pre) => pre - 1)
        break
      case 'plus':
        if (chatsLimit === 6) return
        setChatsLimit((pre) => pre + 1)
        break

      default:
        break
    }
  }

  const roleChange = (id: number) => {
    console.log(id)
    setRoleId(id)
    setSelectEmployeeInfo({
      ...selectEmployeeInfo,
      roleId: id
    })
  }

  // 获取雇员列表
  const getEmployeeListInfo = async () => {
    const data: any = await getEmployeeList()
    console.log(data, '123123')
    setListData(data)
  }
  // 获取创建客服选项
  const fetchCreateCustomerOption = async () => {
    const data: any = await getCreateCustomerOption().catch((err) => {
      console.log(err)
    })
    console.log(data, data.permissions[0].id)
    setCreateCustomerOption(data)
    setSelectEmployeeInfo({
      ...selectEmployeeInfo,
      permissionId: data.permissions[0].id
    })
  }

  // 获取客服详情
  const [loading, setLoading] = useState(false)
  const fetchCustomerDetails = async (id: number) => {
    setLoading(true)
    const data: any = await getCustomerDetails(id)
    const employeeInfo = {
      avatar: data.avatar,
      email: data.email,
      employeeId: data.id,
      name: data.name,
      updateName: data.name,
      isReception: data.isReception,
      isReceptionAll: data.isReceptionAll,
      limit: data.limit ?? 3,
      loginTypeId: data.loginTypeId,
      permissionId: data.permissionId ?? 3,
      roleId: data.roleId
    }
    setSelectEmployeeInfo(employeeInfo)
    setLoginPermissionId(data.loginPermissionId)
    setRoleId(data.roleId)
    setChatsLimit(data.limit ?? 3)
    if (data.assistants.length > 0) {
      setSelectSalesAssistant(true)
    }
    setAssistantListId(data.assistants)
    setSelectAssistantNumber(data.assistants.length)
    // 添加组数据
    const groupsState = data.groups.map((item: any) => {
      return {
        name: item.name,
        id: item.id,
        primary: item.primary,
        receptionId: item.receptionId
      }
    })
    setSelectGroups(groupsState)
    // 添加组里面的接待ID数据
    const receptionState = data.groups.map((item: any) => {
      return item.receptions
    })
    console.log(receptionState, '1111111')
    setSelectGroupsReception(receptionState)
    setLoading(false)
  }

  // 验证客服是否存在
  const checkEmployeeIsAlready = async (employeeId: number) => {
    const data = await getEmployeeIsAlready(employeeId)
    return data
  }
  useEffect(() => {
    console.log('----render')
    fetchCreateCustomerOption()
    if (searchParams.get('id')) {
      fetchCustomerDetails(Number(searchParams.get('id')))
    }
    getEmployeeListInfo()
  }, [])
  useEffect(() => {
    if (
      searchParams &&
      searchParams.get('id') &&
      listData.length &&
      assistantListId.length
    ) {
      const filteredAssistantList = listData.filter((item) =>
        assistantListId.includes(item.id)
      )
      const assistantState = filteredAssistantList.map((item) => ({
        avatar: item.avatar,
        email: item.email,
        employeeId: item.id,
        name: item.nickname
      }))
      setAssistantList(assistantState)
    }
  }, [listData])

  // 组的Option
  const renderGroupsOptions = useMemo(() => {
    return createCustomerOption.groups?.map((item: any) => (
      <Option
        value={item.id}
        key={item.id}
        label={item.name}
        receptionList={item.receptionList}
      >
        {item.name}
      </Option>
    ))
  }, [createCustomerOption.groups])

  const beforeUpload = (file: RcFile) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
    if (!isJpgOrPng) {
      message.error('You can only upload JPG/PNG file!')
      return false
      // this.$message.error('You can only upload JPG/PNG file!')
    }
    const isLt300 = file.size / 1024 < 300
    if (!isLt300) {
      message.error('Image must smaller than 300kb!')
      return false
    }
    getBase64(file, (url) => {
      setImgLoading(true)
      const formData = new FormData()
      formData.append('file', file)
      formData.append('type', 'images')
      uploadFile(formData, {})
        .then((data: any) => {
          setSelectEmployeeInfo({ ...selectEmployeeInfo, avatar: data.url })
        })
        .finally(() => {
          setImgLoading(false)
        })
    })
    return false
  }

  // // 验证是否可以添加该名助理
  // const fetchCheckAssistant = async (id: number) => {
  //   const data = await getCheckAssistant(id).catch((err) => {
  //     console.log(err)
  //   })
  //   console.log(data)
  //   return data
  // }

  // 选择助理函数
  const [assistantList, setAssistantList] = useState<any>([])
  const [assistantListId, setAssistantListId] = useState<any>([])
  const [selectAssistantNumber, setSelectAssistantNumber] = useState<number>(0)
  const handleSelectAssistantChange = async (
    val: string,
    option: BaseOptionType
  ) => {
    console.log('selected', val, option)

    if (option.length) {
      console.log(selectAssistantNumber, val.length)
      if (val.length > 5)
        return message.warning('You can only add 5 assistants')
      if (selectAssistantNumber < val.length) {
        console.log('123', selectAssistantNumber, val.length)
        await getCheckAssistant(
          Number(val[val.length - 1]),
          selectEmployeeInfo.employeeId
            ? selectEmployeeInfo.employeeId
            : undefined
        )
          .then((res: any) => {
            const list = option.map((item: any) => ({
              avatar: item.avatar,
              email: item.email,
              employeeId: item.id,
              name: item.nickname
            }))
            const listId = option.map((item: any) => item.id)
            setAssistantListId(listId)
            setAssistantList(list)
            setSelectAssistantNumber(val.length)
          })
          .catch((err: any) => {
            console.log(err)
            setAssistantListId(assistantListId)
            console.log('selected', val, option)
            setSelectAssistantNumber(val.length - 1)
            return
          })
      } else {
        const list = option.map((item: any) => ({
          avatar: item.avatar,
          email: item.email,
          employeeId: item.id,
          name: item.nickname
        }))
        const listId = option.map((item: any) => item.id)
        setAssistantListId(listId)
        setAssistantList(list)
        setSelectAssistantNumber(val.length)
      }
    } else {
      setAssistantListId([])
      setAssistantList([])
      setSelectAssistantNumber(0)
    }
  }

  // Receive选择函数
  const handleReceive = (e: CheckboxChangeEvent) => {
    console.log('checked = ', e.target.checked)
    setSelectEmployeeInfo({
      ...selectEmployeeInfo,
      isReception: e.target.checked
    })
  }

  // Receive All选择函数
  const handleReceiveAll = (e: CheckboxChangeEvent) => {
    console.log('checked = ', e.target.checked)
    setSelectEmployeeInfo({
      ...selectEmployeeInfo,
      isReceptionAll: e.target.checked
    })
  }

  // Permission多选框函数
  const handlePermissions = (e: RadioChangeEvent) => {
    console.log('checked = ', e.target.value)
    setSelectEmployeeInfo({
      ...selectEmployeeInfo,
      permissionId: e.target.value
    })
  }

  // 选择组之后，记录组信息的state
  const [selectGroups, setSelectGroups] = useState<any>([])
  // 选择组之后，记录每个组的所有接待id
  const [selectGroupsReception, setSelectGroupsReception] = useState<any>([])

  // 选择组函数，并将选择的数据记录到selectGroups中，将接待id记录到selectGroupsReception中
  const handleSelectGroups = (val: string, option: BaseOptionType) => {
    console.log('selected', val, option)
    const receptionList = option.map((item: any) => item.receptionList)
    const selectState = option.map((item: any, index: number) => {
      return {
        id: item.value,
        name: item.label,
        primary: false,
        receptionId: receptionList[index][0].id
      }
    })
    console.log(selectState, receptionList)
    setSelectGroups(selectState)
    setSelectGroupsReception(receptionList)
  }

  // 组里面开关的函数
  const handlePrimarySwitch = (value: boolean, index: number) => {
    console.log(value, index)
    const selectState = selectGroups.map((item: any, i: number) => {
      if (i === index) {
        return { ...item, primary: value }
      }
      return item
    })
    console.log(selectState)
    setSelectGroups(selectState)
  }

  // 组中的接待ID的函数
  const handleReceptionChange = (value: string, index: number) => {
    console.log(value, index)
    const selectState = selectGroups.map((item: any, i: number) => {
      if (i === index) {
        return { ...item, receptionId: value }
      }
      return item
    })
    console.log(selectState)
    setSelectGroups(selectState)
  }

  // 选择添加销售助理的函数及变量
  const [selectSalesAssistant, setSelectSalesAssistant] =
    useState<boolean>(false)
  const handleAssociationChange = (e: CheckboxChangeEvent) => {
    console.log(e.target.checked)
    setSelectSalesAssistant(e.target.checked)
  }

  // 删除某一个组
  const handleDeleteGroup = (index: number) => {
    console.log(index)
    const selectState = selectGroups.filter((item: any, i: number) => {
      return i !== index
    })
    console.log(selectState)
    setSelectGroups(selectState)
  }

  // 登陆后状态单选函数
  const handleLoginStatus = (e: RadioChangeEvent) => {
    console.log('radio checked', e.target.value)
    setSelectEmployeeInfo({
      ...selectEmployeeInfo,
      loginTypeId: e.target.value
    })
  }
  const [submitBtnStatus, setSubmitBtnStatus] = useState<boolean>(false)
  // 提交
  const handleSubmit = async () => {
    console.log(selectEmployeeInfo)
    // 去掉selectGroups数组中，每一个name
    if (!selectEmployeeInfo.name) {
      message.error('Please select Customer Service')
      return
    } else if (!selectEmployeeInfo.permissionId) {
      message.error('Please select Permission')
      return
    }
    const selectGroupsState = selectGroups.map((item: any) => {
      return {
        id: item.id,
        receptionId: item.receptionId,
        primary: item.primary
      }
    })
    let params = {}
    if (roleId === 1) {
      params = {
        ...selectEmployeeInfo,
        groups: selectGroupsState,
        limit: chatsLimit
      }
    } else if (roleId === 2 || roleId === 3) {
      if (selectSalesAssistant && assistantList.length) {
        params = {
          ...selectEmployeeInfo,
          assistants: assistantList,
          groups: selectGroupsState,
          limit: chatsLimit
        }
      } else {
        params = {
          ...selectEmployeeInfo,
          groups: selectGroupsState,
          limit: chatsLimit
        }
      }
    } else {
      params = {
        ...selectEmployeeInfo
      }
    }
    console.log(params, roleId)
    setSubmitBtnStatus(true)
    if (searchParams.get('id')) {
      await putEditCustomer(searchParams.get('id'), params)
        .then((res) => {
          console.log(res)
          message
            .success('Edit Customer Success!!!', 1)
            .then(() => navigate(-1))
        })
        .catch((e) => setSubmitBtnStatus(false))
    } else {
      await postCreateCustomer(params)
        .then((res) => {
          console.log(res)
          message.success('Add Customer Success!!!', 1).then(() => navigate(-1))
        })
        .catch((e) => setSubmitBtnStatus(false))
    }
  }

  return (
    <div className={style.customer_form_wrap}>
      {loading ? (
        <Spin size="large"></Spin>
      ) : (
        <>
          <div className={style.form_content}>
            <div className={style.basic_info}>
              <div className={style.title}>Basic Information</div>
              <Upload beforeUpload={beforeUpload} showUploadList={false}>
                <div className={style.avatar_wrap}>
                  <div className={style.avatar_img}>
                    <FsAvatar
                      src={
                        selectEmployeeInfo.avatar
                          ? selectEmployeeInfo.avatar
                          : defaultAvatar
                      }
                    />
                    {imgLoading && <div className={style.loading}></div>}
                  </div>
                  <div className={style.avatar_info}>
                    <div className={style.title}>Select Local Avatar</div>
                    <div className={style.text}>
                      Only support JPG,JPEG. The maximum file size is 300KB.
                    </div>
                  </div>
                </div>
              </Upload>
              <div className={style.username_wrap}>
                {searchParams.get('id') ? null : (
                  <>
                    <div className={style.input_item_wrap}>
                      <label className={style.label_required}>Name:</label>
                      <Select
                        showSearch
                        placeholder="Select Customer Service"
                        size="large"
                        onChange={handleSelectChange}
                        value={selectEmployeeInfo.name}
                        options={listData}
                        fieldNames={{ label: 'nickname', value: 'id' }}
                        filterOption={(input, option) =>
                          (option?.nickname.toUpperCase() ?? '').includes(
                            input.toUpperCase()
                          )
                        }
                      />
                    </div>
                    <i className="iconfont icon-line"></i>
                  </>
                )}
                <div className={style.input_item_wrap}>
                  <label className="">Update Name:</label>
                  <Input
                    placeholder="Update Name"
                    size="large"
                    value={selectEmployeeInfo.updateName}
                    maxLength={50}
                    onChange={(e) =>
                      setSelectEmployeeInfo({
                        ...selectEmployeeInfo,
                        updateName: e.target.value
                      })
                    }
                  />
                </div>
              </div>
              <div className={style.email_wrap}>
                <div className={style['input_item_wrap']}>
                  <label className={style.label_required}>Email:</label>
                  <Input
                    size="large"
                    disabled
                    value={selectEmployeeInfo.email}
                  />
                </div>
              </div>
            </div>

            <div className={style.basic_info}>
              <div className={style.title}>Role</div>
              <div className={style.role_wrap}>
                {createCustomerOption.roles
                  ? createCustomerOption.roles.map(
                      (item: any, index: number) => (
                        <div
                          className={`${style.role_item} ${
                            roleId === item.id ? style.role_active : ''
                          }`}
                          key={item.id}
                          onClick={() => roleChange(item.id)}
                        >
                          <div className={style.icon}>
                            <i
                              className={`iconfont ${roleIconList[index].icon}`}
                            ></i>
                          </div>
                          <div className={style.role_info}>
                            <div className={style.title}>{item.name}</div>
                            <div className={style.desc}>{item.describe}</div>
                          </div>
                        </div>
                      )
                    )
                  : ''}
                {searchParams.get('id') &&
                  ![1, 2].includes(loginPermissionId) && (
                    <div className={style.role_disabled}></div>
                  )}
              </div>
            </div>
            {roleId === 2 || roleId === 3 ? (
              <div className={style.basic_info}>
                <div className={style.title}>Role association</div>
                <div className={style.association}>
                  <Checkbox
                    defaultChecked={selectSalesAssistant}
                    onChange={handleAssociationChange}
                  >
                    <div className={style.checkbox_label}>
                      <span>Add assistant</span>
                      <span>
                        The assistant will receive and synchronize your chat.
                      </span>
                    </div>
                  </Checkbox>
                  {selectSalesAssistant ? (
                    <div style={{ paddingLeft: '24px', marginTop: '10px' }}>
                      <Select
                        style={{ width: '380px' }}
                        showSearch
                        maxLength={5}
                        placeholder="Select an associated role"
                        size="middle"
                        mode="multiple"
                        value={assistantListId}
                        onChange={handleSelectAssistantChange}
                        options={listData}
                        fieldNames={{ label: 'nickname', value: 'id' }}
                        filterOption={(input, option) =>
                          (option?.nickname.toUpperCase() ?? '').includes(
                            input.toUpperCase()
                          )
                        }
                      />
                    </div>
                  ) : (
                    ''
                  )}
                </div>
              </div>
            ) : (
              ''
            )}
            {roleId !== 4 ? (
              <>
                <div className={style.basic_info}>
                  <div className={style.title}>Receive</div>
                  <div className={style.new_receive}>
                    <div className={style.permission_wrap}>
                      <Checkbox
                        checked={selectEmployeeInfo.isReception}
                        onChange={handleReceive}
                      >
                        <div className={style.checkbox_label}>
                          <span>Receive</span>
                          <span>Can receive customers</span>
                        </div>
                      </Checkbox>
                    </div>
                    {selectEmployeeInfo.isReception && (
                      <div className={style.permission_wrap}>
                        <Checkbox
                          checked={selectEmployeeInfo.isReceptionAll}
                          onChange={handleReceiveAll}
                        >
                          <div className={style.checkbox_label}>
                            <span>Receive All</span>
                            <span>
                              Whether to receive customers from other regions
                            </span>
                          </div>
                        </Checkbox>
                      </div>
                    )}
                  </div>
                </div>
                {/* <div className={style.basic_info}>
                  <div className={style.title}>Receive All</div>
                  <div className={style.permission_wrap}>
                    <Checkbox
                      key={customerDetails.isReceptionAll}
                      defaultChecked={customerDetails.isReceptionAll}
                      onChange={handleReceiveAll}
                    >
                      <div className={style.checkbox_label}>
                        <span>Receive All</span>
                        <span>Whether to receive customers from other regions</span>
                      </div>
                    </Checkbox>
                  </div>
                </div> */}
                <div className={style.basic_info}>
                  <div className={style.title}>Permissions</div>
                  <div className={style.permission_wrap}>
                    <Radio.Group
                      onChange={handlePermissions}
                      value={selectEmployeeInfo.permissionId || 3}
                      disabled={loginPermissionId === 3}
                    >
                      {createCustomerOption.permissions
                        ? createCustomerOption.permissions.map((item: any) => (
                            <Radio value={item.id} key={item.id}>
                              <div className={style.checkbox_label}>
                                <span>{item.name}</span>
                                <span>{item.describe}</span>
                              </div>
                            </Radio>
                          ))
                        : ''}
                    </Radio.Group>
                  </div>
                </div>
                <div className={style.basic_info}>
                  <div className={style.title}>Member of groups</div>
                  <div className={style.group_member_wrap}>
                    <div style={{ width: '468px' }}>
                      <Select
                        mode="multiple"
                        allowClear
                        style={{ width: '100%' }}
                        placeholder="Please select"
                        size="large"
                        onChange={handleSelectGroups}
                        value={selectGroups.map((item: any) => item.id)}
                        filterOption={(input, option) =>
                          (option?.label ?? '')
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        }
                      >
                        {renderGroupsOptions}
                      </Select>
                    </div>
                    <div className={style.group_wrap}>
                      {selectGroups.map((item: any, index: number) => (
                        <div className={style.group_item} key={item.id}>
                          <i
                            className="iconfont icon-guanbi"
                            style={{ cursor: 'pointer' }}
                            onClick={() => handleDeleteGroup(index)}
                          ></i>
                          <div
                            className={style.group_icon}
                            style={{
                              background: siteColorMap[item.name.toLowerCase()]
                            }}
                          >
                            {item.name.slice(0, 1).toUpperCase()}
                          </div>
                          <div className={style.group_info}>
                            <span className={style.title}>{item.name}</span>
                            <div className={style.set_primary_wrap}>
                              <span>Set primary agent</span>
                              <Switch
                                onChange={(value) =>
                                  handlePrimarySwitch(value, index)
                                }
                                size="small"
                                defaultChecked={item.primary}
                                disabled={loginPermissionId > 2}
                              ></Switch>
                            </div>
                            <div className={style.reception}>
                              <span className={style.title}>
                                Reception type:
                              </span>
                              <Select
                                placeholder="Select Reception"
                                size="middle"
                                options={selectGroupsReception[index]}
                                defaultValue={item.receptionId}
                                fieldNames={{ label: 'name', value: 'id' }}
                                onChange={(value) =>
                                  handleReceptionChange(value, index)
                                }
                              />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <div className={style.basic_info}>
                  <div className={style.title}>Chats limit</div>
                  <div className={style.input_number_wrap}>
                    <div className={style.input_number}>
                      <span
                        className={`${style.btn} ${
                          loginPermissionId === 3 ? style.btn_disabled : ''
                        }`}
                        onClick={() => ChangeChatLimit('sub')}
                      >
                        <i className="iconfont icon-jianshao"></i>
                      </span>
                      <div className={style.num_window}>{chatsLimit}</div>
                      <span
                        className={`${style.btn} ${
                          loginPermissionId === 3 ? style.btn_disabled : ''
                        }`}
                        onClick={() => ChangeChatLimit('plus')}
                      >
                        <i className="iconfont icon-tianjia"></i>
                      </span>
                    </div>
                    <div className={style.desc}>
                      Concurrent chats (3 is optimal for rookies)
                    </div>
                  </div>
                </div>
                <div className={style.basic_info}>
                  <div className={style.title}>Status after logging in</div>
                  <div>
                    <Radio.Group
                      onChange={handleLoginStatus}
                      key={selectEmployeeInfo.loginTypeId}
                      defaultValue={selectEmployeeInfo.loginTypeId || 1}
                    >
                      <Radio value={1}>Accept chats</Radio>
                      <Radio value={2}>Don't accept chats</Radio>
                    </Radio.Group>
                  </div>
                </div>
              </>
            ) : (
              ''
            )}
          </div>
          <div className={style.form_footer}>
            <Space>
              <Button onClick={handleFormPageCancel}>Cancel</Button>
              <Button
                type="primary"
                onClick={handleSubmit}
                disabled={submitBtnStatus}
              >
                Submit
              </Button>
            </Space>
          </div>
        </>
      )}
    </div>
  )
}

export default AddCustomer
