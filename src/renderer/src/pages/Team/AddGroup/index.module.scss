.group_member_wrap {
  height: 100%;
  background: #fff;
  display: flex;
  flex-direction: column;
  .form_content {
    flex: 1;
    padding: 32px;
    overflow: auto;
    .basic_info {
      padding-bottom: 32px;
      border-bottom: 1px solid #e4e7ed;
      &:not(:first-child) {
        padding-top: 32px;
      }
      &:last-child {
        border: none;
      }
      .title {
        margin-bottom: 24px;
        font-size: 18px;
        font-weight: 500;
        line-height: 26px;
        color: #262626;
      }
      .groupname_wrap {
        margin-top: 24px;
        display: flex;
        gap: 32px;
        .input_item_wrap {
          width: 468px;
          display: flex;
          flex-direction: column;
          label {
            margin-bottom: 8px;
            @include font14;
            color: #262626;
          }
          .label_required {
            &::before {
              display: inline-block;
              margin-inline-end: 4px;
              color: #ff4d4f;
              font-size: 14px;
              font-family: SimSun, sans-serif;
              line-height: 1;
              content: '*';
            }
          }
        }
      }
      .group_list_wrap {
        display: flex;
        flex-wrap: wrap;
        margin-top: 16px;
        gap: 24px;
        .group_item {
          position: relative;
          // display: flex;
          // align-items: baseline;
          padding: 8px 12px 16px;
          border-radius: 4px;
          background: #fafafa;
          .userinfo {
            display: flex;
            padding-bottom: 12px;
            .user_text {
              margin-left: 8px;
              .text {
                display: flex;
                align-items: baseline;
                justify-content: space-between;
                i {
                  width: 12px;
                  height: 12px;
                  font-size: 12px;
                  cursor: pointer;
                }
                & > span {
                  display: inline-block;
                  width: 142px;
                  @include font14;
                  color: #262626;
                  margin-bottom: 4px;
                }
              }
              .group_icon {
                width: 16px;
                height: 16px;
                margin-right: 4px;
                border-radius: 2px;
                display: inline-flex;
                justify-content: center;
                align-items: center;
                color: #fff;
              }
              .group_name {
                @include font12;
                color: #bfbfbf;
              }
            }
          }
          .btn {
            padding-top: 12px;
            border-top: 1px solid #e4e7ed;
            .set_primary_wrap {
              width: 100%;
              display: flex;
              justify-content: space-between;
              align-items: center;
              & > span {
                @include font12;
                color: #8c8c8c;
              }
            }
            .reception {
              margin-top: 8px;
              padding-top: 8px;
              border-top: 1px solid #e4e7ed;
              display: flex;
              flex-direction: column;
              & > span {
                @include font12;
                color: #8c8c8c;
                margin-bottom: 8px;
              }
            }
          }
        }
      }
    }
  }
  .form_footer {
    display: flex;
    justify-content: flex-end;
    padding: 24px 32px;
    box-shadow: 0px -3px 8px 0px rgba(0, 0, 0, 0.13);
  }
}
