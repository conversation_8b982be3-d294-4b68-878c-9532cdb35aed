import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import style from './index.module.scss'
import { Button, Input, Select, Space, Switch, message } from 'antd'
import GroupModal from '../components/GroupModal/index'
import {
  creatGroup,
  getAllSite,
  getGroupAllMember,
  getGroupDetails,
  putGroupUpdate,
  getSiteReception
} from '@/api/team'
import { CheckboxValueType } from 'antd/es/checkbox/Group'
import { siteColorMap } from '@/utils/util'
import FsAvatar from '@/component/FsAvatar'
const { Option } = Select

type ReceptionType = {
  id: number
  name: string
}[]

// type PropsType = {}
function AddCustomer() {
  const [showModal, setShowModal] = useState(false)
  const [memberList, setMemberList] = useState<Array<Record<string, any>>>([])
  const [siteList, setSiteList] = useState<Record<string, any>[]>([])
  const [formData, setFormData] = useState<UpdateGroup>({
    name: ''
  })
  const [selectMemberList, setSelectMemberList] = useState<
    Record<string, any>[]
  >([])
  // 接待ID
  const [receptionId, setReceptionId] = useState<ReceptionType>([])
  const [selectReceptionId, setSelectReceptionId] = useState<number[]>([])
  const [messageApi, contextHolder] = message.useMessage()
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()

  const handleFormPageCancel = () => {
    navigate(-1)
  }
  const showGroupModal = () => {
    if (formData.siteId) {
      setShowModal(true)
    } else {
      messageApi.open({
        type: 'error',
        content: 'Please select a site first'
      })
    }
  }
  const hideModal = (show: boolean) => {
    setShowModal(show)
  }

  const fetchGroupAllMember = async () => {
    const data: any = await getGroupAllMember()
    setMemberList(data)
  }

  const fetchAllSite = async () => {
    const data: any = await getAllSite()
    setSiteList(data)
  }

  const [submitBtnStatus, setSubmitBtnStatus] = useState(false)
  // 获取客服组详情
  const fetchGroupDetails = async (id: number) => {
    setSubmitBtnStatus(true)
    const data: any = await getGroupDetails(id)
    console.log(data, '111')
    setSelectMemberList(data.members)

    setReceptionId(data.receptions)
    setSelectReceptionId(data.members.map((item: any) => item.receptionId))
    const members = data.members.map((item: any) => {
      return {
        id: item.id,
        primary: item.primary,
        receptionId: item.receptionId
      }
    })
    setFormData({ name: data.name, siteId: data.siteId, members })
    setSubmitBtnStatus(false)
  }

  // 更新客服组
  const updateGroup = async () => {
    const data: any = await putGroupUpdate(
      Number(searchParams.get('id')),
      formData
    )
    console.log(data)
  }

  // 根据站点获取接待列表
  const fetchSiteReception = async (id: number) => {
    const data: any = await getSiteReception(id)
    setReceptionId(data)
  }

  const hangleInput = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setFormData({ ...formData, name: e.target.value })
  }
  const handleSelect = (id: number) => {
    setFormData({ ...formData, siteId: id })
    fetchSiteReception(id)
    // setSelectMemberList([])
  }

  // primary开关操作
  const handlePrimarySwitch = (index: number, value: boolean) => {
    console.log(selectMemberList)
    // 修改selectMemberList中对应下标的primary值
    const newSelectMemberList = selectMemberList.map((item, i) => {
      if (i === index) {
        item.primary = value
      }
      return item
    })
    setSelectMemberList(newSelectMemberList)
    // 修改formData中对应下标的primary值
    const newFormData = formData.members.map((item, i) => {
      if (i === index) {
        item.primary = value
      }
      return item
    })
    setFormData({ ...formData, members: newFormData })
  }

  const submitFormData = async () => {
    console.log(formData)
    if (!formData.name) {
      messageApi.open({
        type: 'error',
        content: 'Please enter group name'
      })
      return
    }
    if (!formData.siteId) {
      messageApi.open({
        type: 'error',
        content: 'Please select site'
      })
      return
    }
    setSubmitBtnStatus(true)
    if (searchParams.get('id')) {
      await updateGroup()
        .then(() => {
          messageApi
            .open({
              type: 'success',
              content: 'Edit successfully!!!',
              duration: 1
            })
            .then(() => {
              navigate(-1)
            })
        })
        .catch((e) => setSubmitBtnStatus(false))
    } else {
      console.log(formData)
      await creatGroup(formData)
        .then(() => {
          messageApi
            .open({
              type: 'success',
              content: 'Add successfully!!!',
              duration: 1
            })
            .then(() => {
              navigate(-1)
            })
        })
        .catch((e) => setSubmitBtnStatus(false))
    }
  }
  const onMemberSelect = (idList: CheckboxValueType[]) => {
    const filterList = memberList.filter((item) => idList.includes(item.id))
    const defaultReception: number[] = []
    filterList.forEach((item) => {
      item.primary = false
      item.receptionId = receptionId[0].id
      defaultReception.push(item.receptionId)
    })
    console.log(filterList, '123')
    setSelectMemberList(filterList)
    setSelectReceptionId(defaultReception)
    const members = filterList.map((item) => {
      return {
        id: item.id,
        primary: item.primary,
        receptionId: item.receptionId
      }
    })
    setFormData({ ...formData, members })
  }
  const removerMember = (id: number) => {
    const index = selectMemberList.findIndex((item) => item.id === id)
    selectMemberList.splice(index, 1)
    setSelectMemberList([...selectMemberList])
    const members = selectMemberList.map((item) => {
      return {
        id: item.id,
        primary: item.primary,
        receptionId: item.receptionId
      }
    })
    setFormData({ ...formData, members })
  }
  const processedData = useMemo(() => selectMemberList, [selectMemberList])
  useEffect(() => {
    fetchGroupAllMember()
    fetchAllSite()
    if (searchParams.get('id')) {
      fetchGroupDetails(Number(searchParams.get('id')))
    }
  }, [])
  return (
    <div className={style.group_member_wrap}>
      {contextHolder}
      <div className={style.form_content}>
        <div className={style.basic_info}>
          <div className={style.title}>Basic Information</div>
          <div className={style.groupname_wrap}>
            <div className={style.input_item_wrap}>
              <label className={style.label_required}>Group Name:</label>
              <Input
                size="large"
                placeholder="Please enter group name"
                value={formData.name}
                onChange={hangleInput}
              ></Input>
            </div>
            <div className={style.input_item_wrap}>
              <label className="">Site:</label>
              <Select
                placeholder="Select Site"
                size="large"
                value={formData.siteId}
                onChange={handleSelect}
                showSearch
                options={siteList}
                fieldNames={{ label: 'name', value: 'id' }}
                filterOption={(input, option) => {
                  console.log(input, option)
                  return (option?.name.toUpperCase() ?? '').includes(
                    input.toUpperCase()
                  )
                }}
              >
                {/* {siteList.map((item) => (
                  <Option value={item.id} key={item.id}>
                    {item.name}
                  </Option>
                ))} */}
              </Select>
            </div>
          </div>
        </div>
        <div className={style.basic_info}>
          <div className={style.title}>Members</div>
          <Button size="large" type="primary" ghost onClick={showGroupModal}>
            + Add Group Members
          </Button>
          <div className={style.group_list_wrap}>
            {selectMemberList.map((item, index: number) => (
              <div className={style.group_item} key={item.id}>
                <div className={style.userinfo}>
                  <FsAvatar src={item.avatar}></FsAvatar>
                  <div className={style.user_text}>
                    <div className={style.text}>
                      <span>{item.name}</span>
                      <i
                        className="iconfont icon-guanbi"
                        onClick={() => removerMember(item.id)}
                      ></i>
                    </div>
                    {item.length > 0 && (
                      <div>
                        <div
                          className={style.group_icon}
                          style={{ background: siteColorMap['cn'] }}
                        >
                          {item[0].name.substr(0, 1)}
                        </div>
                        <span className={style.group_name}>{item[0].name}</span>
                      </div>
                    )}
                  </div>
                </div>
                <div className={style.btn}>
                  <div className={style.set_primary_wrap}>
                    <span>Set primary agent</span>
                    <Switch
                      size="small"
                      checked={item.primary}
                      onChange={(value) => handlePrimarySwitch(index, value)}
                    ></Switch>
                  </div>
                  <div className={style.reception}>
                    <span>Reception type:</span>
                    <Select
                      placeholder="Select Reception"
                      size="middle"
                      options={receptionId}
                      value={selectReceptionId[index]}
                      fieldNames={{ label: 'name', value: 'id' }}
                      onChange={(value) => {
                        const newReceptionId = [...selectReceptionId]
                        newReceptionId[index] = value
                        setSelectReceptionId(newReceptionId)
                        // 更新formData中对应的receptionId
                        const newMembers = [...formData.members]
                        newMembers[index].receptionId = value
                        setFormData({ ...formData, members: newMembers })
                      }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      <div className={style.form_footer}>
        <Space>
          <Button onClick={handleFormPageCancel}>Cancel</Button>
          <Button
            type="primary"
            onClick={submitFormData}
            disabled={submitBtnStatus}
          >
            Submit
          </Button>
        </Space>
      </div>
      <GroupModal
        show={showModal}
        hideModal={hideModal}
        list={memberList}
        onMemberSelect={onMemberSelect}
        selectedList={processedData}
      ></GroupModal>
    </div>
  )
}

export default AddCustomer
