import React, { useEffect, useState } from 'react'
import style from './index.module.scss'
import { Button, Input, Pagination, Select, Space } from 'antd'
import type { PaginationProps } from 'antd'
import FsTable from '@/pages/Settings/components/FsTable'
import FsAvatar from '@/component/FsAvatar'
import AavatarList from '../components/AavatarList/AavatarList'
import { getGroupList, getGroupListOption } from '@/api/team'
import { Outlet, useLocation, useNavigate } from 'react-router-dom'

type DataType = {
  acceptNumber: number
  chatNumber: number
  chatSatisfaction: string
  createDate: string
  id: number
  name: string //客服组名称
  siteName: string //客服站点名称
  status: number
  members: Array<{
    avatar: string
    id: number
    name: string
    status: number
  }>
  permissions: Array<{
    name: string
    status: boolean
  }>
}
// 筛选项结果数据类型
type FilterResultType = {
  groupId?: number
  search?: string
  statusId?: number
}

const { Search } = Input

const columns = [
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
    render: (_: string, record: DataType) => {
      console.log(record.status === 2)
      return (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <FsAvatar
            name={record.name}
            onlineStatus={record.status}
            style={{ width: 42, height: 42, flexShrink: 0 }}
          ></FsAvatar>
          <div className={style.group_name}>
            <span>
              {record.name} ({record.members.length})
            </span>
            <span>{record.acceptNumber} user accepting chats</span>
          </div>
        </div>
      )
    }
  },
  {
    title: 'Members',
    dataIndex: 'members',
    key: 'members',
    render: (_: string, record: DataType) => {
      return <AavatarList list={record.members} key={record.id}></AavatarList>
    }
  },
  {
    title: 'Site',
    dataIndex: 'siteName',
    key: 'siteName'
  },
  {
    title: 'Total Chats',
    dataIndex: 'chatNumber',
    key: 'chatNumber'
  },
  {
    title: 'Chat Satisfaction',
    dataIndex: 'chatSatisfaction',
    key: 'chatSatisfaction'
  },
  {
    title: 'Creat Date',
    dataIndex: 'createDate',
    key: 'createDate'
  }
]
const SearchIcon = (
  <i className="iconfont icon-searchsousuo" style={{ color: '#D8D8D8' }}></i>
)

const Group = () => {
  const [showAddCustomerCom, setShowAddCustomerCom] = useState(false)
  const [tableData, setTableData] = useState([])
  const [tableLoading, setTableLoading] = useState(false)
  const [totalNum, setTotalNum] = useState(10)
  const [paginationParams, setPaginationParams] = useState<{
    current: number
    size: number
  }>({ current: 1, size: 10 })
  const location = useLocation()
  const navigate = useNavigate()
  // 客服组列表筛选项数据
  const [groupListOption, setGroupListOption] = useState<any>({})
  // 筛选项结果
  const [filterResult, setFilterResult] = useState<FilterResultType>({})
  // 是否允许新增组
  const [isAddGroup, setIsAddGroup] = useState(false)
  //初始化
  useEffect(() => {
    if (location.pathname === '/team') {
      fetchManageList()
      fetchGroupListOption()
    }
  }, [paginationParams, filterResult, location.pathname])

  const fetchManageList = async () => {
    // 请求列表数据
    setTableLoading(true)
    const params = {
      ...paginationParams,
      ...filterResult
    }
    const data: any = await getGroupList(params).finally(() =>
      setTableLoading(false)
    )
    console.log(data.records)
    // setPaginationParams((pre) => ({ ...pre}))
    setTableLoading(false)
    setTableData(data.records)
    setTotalNum(data.total)
  }

  const fetchGroupListOption = async () => {
    setTableLoading(true)
    const data: any = await getGroupListOption().finally(() => {
      setTableLoading(false)
    })
    console.log(data, '123')
    setGroupListOption({
      groups: data.groups,
      status: [...data.status]
    })
    setIsAddGroup(data.addGroupPermission)
  }

  const goAddGroup = (url: number) => {
    navigate(`/team/addGroup?id=${url}`)
  }

  const handleChangePage: PaginationProps['onChange'] = (
    pageNumber,
    pageSize
  ) => {
    console.log(pageNumber, pageSize)
    setPaginationParams({
      ...paginationParams,
      current: pageNumber,
      size: pageSize
    })
  }

  return location.pathname.includes('addGroup') ? (
    <Outlet />
  ) : (
    <div className={style.group_page_wrap}>
      <div className={style.head_filter}>
        <Space>
          <Select
            size="large"
            style={{ width: 120 }}
            placeholder="All Status"
            fieldNames={{ label: 'value', value: 'id' }}
            options={groupListOption.status}
            allowClear
            onChange={(value) => {
              setFilterResult({ ...filterResult, statusId: value })
            }}
          />
          <Select
            size="large"
            style={{ width: 120 }}
            placeholder="All Groups"
            fieldNames={{ label: 'name', value: 'id' }}
            options={groupListOption.groups}
            allowClear
            onChange={(value) => {
              setFilterResult({ ...filterResult, groupId: value })
            }}
          />
          <Search
            style={{ width: 380 }}
            size="large"
            placeholder="Search"
            allowClear
            onSearch={(value) => {
              setFilterResult({ ...filterResult, search: value })
            }}
          />
        </Space>
        {/* 新增组按钮，根据权限判断是否展示 */}
        {isAddGroup ? (
          <Button
            type="primary"
            size="large"
            onClick={() => navigate('/team/addGroup')}
          >
            + Add Group
          </Button>
        ) : (
          ''
        )}
      </div>
      {/* table部分 */}
      <div className={style.table_wrap}>
        <FsTable
          data={tableData}
          loading={tableLoading}
          columns={[
            ...columns,
            {
              title: 'Chat',
              key: 'Chat',
              render: (_: string, record: DataType) => {
                return record.permissions[0].status ? (
                  <Space size="middle">
                    <i
                      className="icon-bianjixinxi iconfont"
                      onClick={() => goAddGroup(record.id)}
                    ></i>
                  </Space>
                ) : (
                  ''
                )
              }
            }
          ]}
        ></FsTable>
      </div>
      <div className={style.pagination}>
        <Pagination
          current={paginationParams.current}
          pageSize={paginationParams.size}
          pageSizeOptions={['5', '10', '20', '50']}
          showQuickJumper
          showSizeChanger={true}
          total={totalNum}
          hideOnSinglePage={true}
          showTotal={(totalNum) => `Total ${totalNum} items`}
          onChange={handleChangePage}
        />
      </div>
    </div>
  )
}

export default Group
