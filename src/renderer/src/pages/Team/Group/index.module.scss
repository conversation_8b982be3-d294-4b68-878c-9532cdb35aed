.group_page_wrap {
  display: flex;
  flex-direction: column;
  height: 100%;
  .head_filter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 0 0;
  }
  .table_wrap {
    background-color: #fff;
    margin-top: 20px;
    padding: 0 20px;
    flex: 1;
    .group_name {
      margin-left: 8px;
      span {
        display: block;
        @include font14;
        color: #262626;
        &:last-child {
          @include font12;
          color: #8c8c8c;
        }
      }
    }
    :global {
      .ant-table-thead {
        tr > th {
          background-color: #fff;
        }
      }
      .table_row {
        &:nth-child(2n) {
          td {
            background-color: #fafafa;
          }
        }
      }
    }
    :global {
      i.iconfont {
        cursor: pointer;
      }
    }
  }
  .pagination {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 24px 20px;
    background-color: #fff;
    box-shadow: 0px -3px 8px 0px rgba(0, 0, 0, 0.13);
  }
}
