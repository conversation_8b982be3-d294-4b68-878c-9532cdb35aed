import useLogin from '@/hooks/useLogin'
import './index.scss'
export default function Login() {
  const [isRedirect] = useLogin()
  const redirect_uri = import.meta.env.RENDERER_VITE_REDIRECT_URL
  const iframeUrl = `https://open.feishu.cn/open-apis/authen/v1/index?app_id=${
    import.meta.env.RENDERER_VITE_APP_KEY
  }&redirect_uri=${redirect_uri}`

  return (
    <div className="iframe-container">
      {!isRedirect && <iframe className="iframe_box" src={iframeUrl}></iframe>}
    </div>
  )
}
