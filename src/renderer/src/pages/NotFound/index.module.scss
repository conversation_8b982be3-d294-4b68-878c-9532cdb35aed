.not_found_wrap {
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  .content_wrap {
    text-align: center;
    .info_wrap {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      img {
        width: 96px;
        height: 96px;
      }
      p {
        text-align: center;
        &:first-child {
          @include font18;
          color: #3d3d3d;
          font-weight: 700;
        }
        &:last-child {
          color: #808080;
          @include font12;
          font-size: 400;
        }
      }
    }
    .btn_wrap {
      width: 180px;
      margin-top: 46px;
      display: inline-flex;
      flex-direction: column;
      gap: 12px;
    }
  }
}
