import { useNavigate } from 'react-router-dom'
import styles from './index.module.scss'
import { Button } from 'antd'

export default function NotFound() {
  const navigate = useNavigate()
  // const quitApp = () => {
  //   window.ipcRenderer.send('quitApp')
  // }
  return (
    <div className={styles.not_found_wrap}>
      <div className={styles.content_wrap}>
        <h1>404</h1>
        <Button type="primary" onClick={() => navigate(-1)}>
          Back
        </Button>
      </div>
    </div>
  )
}
