.supervised__container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #fff;
  box-sizing: border-box;
  overflow: hidden;
  .search_wrapper {
    padding: 20px;
    background-color: #fbfbfc;
    box-sizing: border-box;
    .input_search {
      width: 380px;
      height: 40px;
      border-radius: 2px;
      border: 1px solid #e4e7ed;
    }
    i {
      color: #d8d8d8;
      font-size: 10px;
    }
  }
  .list_wrapper {
    padding: 0 20px 20px;
    box-sizing: border-box;
    flex: 1;
    overflow: auto;
    .activity_wrapper {
      i {
        font-size: 16px;
      }
      span {
        margin-left: 4px;
      }
    }
    :global {
      table {
        .ant-table-thead {
          th {
            background-color: #fff;
            &:before {
              display: none;
            }
          }
        }
      }
    }
  }
  .pagination_wrapper {
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    justify-content: flex-end;
    box-shadow: 0px -3px 8px 0px rgba(0, 0, 0, 0.13);
    .pagination {
      margin: 0;
    }
  }
}
