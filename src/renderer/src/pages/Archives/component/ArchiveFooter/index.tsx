import { getUuid } from '@/utils/util'
import styles from './index.module.scss'
import { useRef } from 'react'

function ArchiveFooter() {
  const uuid = getUuid()
  //缓存uuid
  const cacheUuid = useRef(uuid)
  const buttonList: Record<string, any>[] = [
    {
      name: 'Synchronize it to the ERP'
    },
    {
      name: 'Later processing'
    },
    {
      name: 'Mark it as spam'
    }
  ]
  const buttonTp = (name: string, key: string) => {
    return (
      <div className={styles.button_tp} key={key}>
        <span>{name}</span>
      </div>
    )
  }
  return (
    <div className={styles.archive_footer}>
      {buttonList.map((item, index) => {
        return buttonTp(item.name, index + cacheUuid.current)
      })}
    </div>
  )
}

export default ArchiveFooter
