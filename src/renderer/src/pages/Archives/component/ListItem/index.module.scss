.list_item_wrapper {
  border-bottom: 1px solid #e4e7ed;
  padding: 12px 0;
  box-sizing: border-box;
  .activeItem {
    background: #f0f7ff;
  }
  .list_item_content {
    &:hover {
      background-color: #eaecf3;
      cursor: pointer;
    }
    display: flex;
    padding: 4px 8px;
    box-sizing: border-box;
    border-radius: 4px;
    font-size: 12px;
    .list_item_info {
      flex: 1;
      min-width: 0;
      .list_item_name_time {
        font-size: 14px;
        display: flex;
        justify-content: space-between;
        .list_item_time {
          color: #bfbfbf;
          font-size: 12px;
        }
        .list_item_name {
          font-size: 12px;
          height: 12px;
        }
      }
      .agent_box {
        font-size: 12px;
        color: #bfbfbf;
        margin: 4px 0;
      }
      .list_item_message {
        font-size: 12px;
        color: #bfbfbf;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
