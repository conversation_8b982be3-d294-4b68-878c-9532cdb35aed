import { memo } from 'react'
import { ListType } from '../..'
import styles from './index.module.scss'
import FsAvatarProps from '@/component/FsAvatar'
import { formatUTC } from '@/utils/util'
interface ListProps {
  itemData: ListType
  isActive?: boolean
  index?: number
}
//联系人列表项
function ListItem(props: ListProps) {
  const { itemData, isActive = false, index } = props
  //评价模版
  const evaluateTemplate: Record<string, any> = {
    '1': {
      tp: 'icon-a-rongqi19301',
      color: '#48C43D'
    },
    '2': {
      tp: 'icon-a-rongqi1',
      color: '#f74a44'
    }
  }
  return (
    <div className={styles.list_item_wrapper} data-type={index}>
      <div
        className={[
          styles.list_item_content,
          isActive ? styles.activeItem : ''
        ].join(' ')}
      >
        <FsAvatarProps
          style={{ marginRight: '8px' }}
          src={itemData.avatar}
          name={itemData.name || itemData.idStr}
          backgroundColor={itemData.colour}
        ></FsAvatarProps>
        <div className={styles.list_item_info}>
          <div className={styles.list_item_name_time}>
            <div className={styles.list_item_name}>
              {evaluateTemplate[itemData.evaluate] ? (
                <i
                  style={{
                    color: evaluateTemplate[itemData.evaluate].color,
                    marginRight: '4px'
                  }}
                  className={[
                    'iconfont',
                    evaluateTemplate[itemData.evaluate].tp
                  ].join(' ')}
                ></i>
              ) : null}
              {itemData.name}
            </div>
            <div className={styles.list_item_time}>
              {formatUTC(itemData.messageTime)}
            </div>
          </div>
          <div className={styles.agent_box}>{itemData.customerServiceName}</div>
          <div className={styles.list_item_message}>{itemData.msg}</div>
        </div>
      </div>
    </div>
  )
}
export default memo(ListItem)
