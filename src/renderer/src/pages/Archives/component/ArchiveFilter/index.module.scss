.archive_file_wrapper {
  // width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  & > div {
    margin: 8px 0;
    box-sizing: border-box;
  }
  :global {
    .ant-select {
      //     .ant-select-selector {
      //         // height: 40px;
      //         border-radius: 2px;
      //         border: 1px solid #E4E7ED;

      //     }
      // }
      .ant-picker-range {
        width: 300px;
        // height: 40px;
        border-radius: 2px;
        border: 1px solid #e4e7ed;
      }
    }
  }
  .input_search {
    width: 380px;
    // height: 40px;
    border-radius: 2px;
    // border: 1px solid #e4e7ed;
    i {
      color: #d8d8d8;
      font-size: 10px;
    }
  }
  div:not(:last-child) {
    margin-right: 20px;
  }
  .unit_info {
    margin-left: 16px;
  }
}
