import { DatePicker, Input, Select } from 'antd'
import styles from './index.module.scss'
import { memo, useEffect, useState } from 'react'
import { getFilterItem } from '@/api/archives'
const { RangePicker } = DatePicker
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
dayjs.extend(utc)
type SearchType = Omit<ChatUserListType, 'size' | 'current'>
interface ArchiveFilterProps {
  formChange?: (value: SearchType) => void
  formDataObj?: SearchType
  disabled?: boolean
  unit?: number
}
function ArchiveFilter(props: ArchiveFilterProps) {
  const { formChange, formDataObj, disabled, unit } = props
  const [filerData, setFilerData] = useState<SearchType>(formDataObj)
  const [assignee, setAssignee] = useState([])
  const [site, setSite] = useState([])
  const [rate, setRate] = useState([])
  const [transfer, setTransfer] = useState([])
  const onChange = (value: any, filed: string) => {
    console.log(`selected ${value}`)
    console.log(value)
    if (value != 0 && !value) {
      value = ''
    }
    setFilerData((pre) => {
      const data = { ...pre, [filed]: value }
      formChange && formChange(data)
      return data
    })
  }
  const onInputChange = (value: string) => {
    console.log('search:', value)
    setFilerData((pre) => {
      const data = { ...pre, keyWords: value }
      return data
    })
  }
  const searchIcon = () => {
    return <i className="iconfont icon-searchsousuo"></i>
  }
  //获取选择数据
  const getFilterData = async () => {
    try {
      const res: any = await getFilterItem()
      const {
        allAssignee = [],
        allRate = [],
        allSite = [],
        allTransfer = []
      } = res
      setAssignee(allAssignee)
      setRate(allRate)
      setSite(allSite)
      setTransfer(allTransfer)
    } catch (error) {
      console.log(error)
    }
  }
  useEffect(() => {
    console.log('formDataObj', formDataObj)
    setFilerData(formDataObj)
  }, [formDataObj])
  useEffect(() => {
    getFilterData()
  }, [])
  return (
    <div className={styles.archive_file_wrapper}>
      <Select
        showSearch
        placeholder="All assignee"
        optionFilterProp="children"
        onChange={(value) => onChange(value, 'customerServiceIdStr')}
        fieldNames={{ label: 'name', value: 'id' }}
        // size="large"
        value={filerData.customerServiceIdStr}
        options={assignee}
        disabled={disabled}
      />
      <Select
        showSearch
        placeholder="All site"
        optionFilterProp="children"
        onChange={(value) => onChange(value, 'siteId')}
        fieldNames={{ label: 'name', value: 'id' }}
        // size="large"
        options={site}
        disabled={disabled}
        allowClear
      />
      <Select
        showSearch
        placeholder="All rate"
        optionFilterProp="children"
        onChange={(value) => onChange(value, 'rateId')}
        // size="large"
        fieldNames={{ label: 'name', value: 'value' }}
        options={rate}
        disabled={disabled}
        value={filerData.rateId}
      />
      <Select
        showSearch
        placeholder="All transfer"
        optionFilterProp="children"
        onChange={(value) => onChange(value, 'transferType')}
        // size="large"
        fieldNames={{ label: 'value', value: 'key' }}
        options={transfer}
        disabled={disabled}
      />
      <RangePicker
        onChange={(value) => {
          console.log('value', value)
          if (!value) {
            onChange([], 'searchDate')
            return
          }
          const startTime = value?.[0]?.format('YYYY-MM-DD HH:mm:ss') ?? ''
          const endTime = value?.[1]?.format('YYYY-MM-DD HH:mm:ss') ?? ''
          console.log('current', dayjs(endTime).utc().local().format())
          onChange([startTime, endTime], 'searchDate')
        }}
        disabled={disabled}
        value={
          filerData.searchDate
            ?.filter(Boolean)
            ?.map((item) => dayjs(item).local()) as any
        }
        showTime
      />
      <Input.Search
        className={styles.input_search}
        placeholder="Search"
        // suffix={searchIcon()}
        onPressEnter={(e: any) => onChange(e.target.value, 'keyWords')}
        value={filerData.keyWords}
        onChange={(e) => onInputChange(e.target.value)}
        onSearch={(value) => onChange(value, 'keyWords')}
        disabled={disabled}
      />
      <div className={styles.unit_info}>{unit} Units</div>
    </div>
  )
}

export default memo(ArchiveFilter)
