import FsAvatar from '@/component/FsAvatar'
import styles from './index.module.scss'
import { memo, useLayoutEffect, useRef, useState } from 'react'
import AddBlacklist from '@/component/AddBlacklist'
import { formatUTC } from '@/utils/util'
import OtherInfo from '@renderer/component/OtherInfo'
interface ChatDetailProps {
  viewAll?: (id: string) => void
  detailFeedback?: any
  groupUserChatDetailVO?: any
}
function ChatDetail(props: ChatDetailProps) {
  const { viewAll, detailFeedback, groupUserChatDetailVO } = props
  const [isExpand, setIsExpand] = useState(false)
  const [isShowMore, setIsShowMore] = useState(false)
  const [openAddBlackListModal, setOpenAddBlackListModal] = useState(false)
  const contentRef = useRef<HTMLDivElement>(null)

  const evaluationMap: Record<string, any> = {
    '1': {
      name: 'Good evaluation',
      icon: 'icon-a-rongqi19301',
      color: '#10A300'
    },
    '2': {
      name: 'Bad evaluation',
      icon: 'icon-a-rongqi1',
      color: '#f74a44'
    },
    default: {
      name: 'No evaluation feedback',
      icon: '',
      color: ''
    }
  }
  const changeExpand = () => {
    setIsExpand(!isExpand)
    const currentExpand = !isExpand
    console.log(currentExpand)
    const content = contentRef.current
    if (currentExpand) {
      const scrollHeight = content.scrollHeight
      console.log(scrollHeight)
      content.style.height = scrollHeight + 'px'
    } else {
      content.style.height = '80px'
      setIsExpand(false)
    }
  }
  useLayoutEffect(() => {
    const content = contentRef.current
    if (content) {
      const scrollHeight = content.scrollHeight
      setIsExpand(false)
      if (scrollHeight > 65) {
        setIsShowMore(true)
        content.style.height = '65px'
      } else {
        setIsShowMore(false)
      }
    }
    return () => {
      if (content) {
        content.style.height = 'auto'
      }
    }
  }, [detailFeedback.comment])
  // useEffect(() => {
  //   console.log('detailFeedback.comment')
  //   if (detailFeedback.comment.length > 0 && contentRef.current) {
  //     contentRef.current.style.height = 'auto'
  //   }
  // }, [detailFeedback.comment])
  const handleViewAll = () => {
    const id = groupUserChatDetailVO.userId + '' || ''
    viewAll && viewAll(id)
  }
  const handleOpenAddBlackListModal = () => {
    setOpenAddBlackListModal(true)
  }
  return (
    <div className={styles.chat_detail_wrapper}>
      <div className={styles.chat_detail_title}>Details</div>
      <div className={styles.chat_detail_content}>
        {/* General Info */}
        <div className={styles.chat_general_info}>
          <div className={styles.chat_detail_content_title}>General Info</div>
          <div className={styles.chat_user_info}>
            <div className={styles.chat_user_info_item}>
              <FsAvatar
                name={
                  groupUserChatDetailVO.name ||
                  groupUserChatDetailVO.userId + ''
                }
                backgroundColor={groupUserChatDetailVO.colour}
              />
              <span className={styles.chat_user_name}>
                {groupUserChatDetailVO.name || groupUserChatDetailVO.userId}
              </span>
              <span className={styles.chat_user_email}>
                {groupUserChatDetailVO.email}
              </span>
            </div>
            <div className={styles.chat_user_td}>
              <div className={styles.chat_time}>
                <i className="iconfont icon-time"></i>
                <span>
                  {groupUserChatDetailVO.dateTime &&
                    formatUTC(groupUserChatDetailVO.dateTime)}
                </span>
                <span className={styles.chat_local_time}>local time</span>
              </div>
              <div className={styles.chat_line}></div>
              <div className={styles.chat_address}>
                <i className="iconfont icon-dizhi"></i>
                <span>{groupUserChatDetailVO.address}</span>
              </div>
            </div>
          </div>
          <div className={styles.chat_view_all} onClick={handleViewAll}>
            View all Chats
          </div>
        </div>
        {/* Evaluation Info */}
        <div className={styles.chat_evaluation}>
          <div className={styles.evaluation_title}>Evaluation Info</div>
          <div className={styles.evaluation_content}>
            {/* <span> */}
            <i
              className={[
                'iconfont',
                evaluationMap[detailFeedback.star ?? 'default'].icon
              ].join(' ')}
              style={{
                color: evaluationMap[detailFeedback.star || 'default'].color
              }}
            ></i>
            {/* </span> */}
            <span>{evaluationMap[detailFeedback.star ?? 'default'].name}</span>
          </div>
          <div className={styles.feedback_content}>
            <div className={styles.feedback_title}>Feedback content:</div>
            <div className={styles.feedback_detail} ref={contentRef}>
              {detailFeedback.comment}
            </div>
            {detailFeedback.comment?.length > 0 && isShowMore ? (
              <div className={styles.expand_box} onClick={changeExpand}>
                <span>{isExpand ? 'Collapse' : 'Show More'}</span>
                <i
                  className={[
                    'iconfont',
                    isExpand ? 'icon-shang' : 'icon-xia'
                  ].join(' ')}
                ></i>
              </div>
            ) : null}
          </div>
        </div>
        {/* Other Info */}
        <div style={{ paddingTop: '28px' }}>
          <OtherInfo {...groupUserChatDetailVO}></OtherInfo>
        </div>
      </div>
      {/* 加入黑名单 */}
      {openAddBlackListModal && (
        <AddBlacklist
          openAddBlackListModal={openAddBlackListModal}
          setOpenAddBlackListModal={setOpenAddBlackListModal}
          customerId={groupUserChatDetailVO.userId}
          ipAddress={groupUserChatDetailVO.ipAddress}
        />
      )}
    </div>
  )
}

export default memo(ChatDetail)
