.chat_detail_wrapper {
  height: 100%;
  $titleColor: #262626;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  overflow-y: auto;
  &:hover {
    &::-webkit-scrollbar-thumb {
      background: #b3b1b1;
      border-radius: 4px;
    }
  }
  //修改滚动条样式
  &::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
  &::-webkit-scrollbar-thumb {
    background: #fff;
    border-radius: 4px;
  }
  &::-webkit-scrollbar-track {
    background: #fff;
    border-radius: 4px;
  }
  .chat_detail_title {
    color: $titleColor;
    font-size: 24px;
    font-weight: 500;
    margin: 20px 0 28px 32px;
  }
  .chat_detail_content {
    .chat_general_info {
      padding: 20px 24px 28px 24px;
      border-bottom: 1px solid #dcdfe6;
      box-sizing: border-box;
      .chat_detail_content_title {
        color: $titleColor;
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 20px;
      }
      .chat_user_info {
        display: flex;
        flex-direction: column;
        .chat_user_info_item {
          display: flex;
          align-items: center;
          color: $titleColor;
          font-size: 16px;
          .chat_user_name {
            margin: 0 8px 0 12px;
            font-weight: 500;
          }
          .chat_user_email {
            color: #595959;
          }
        }
        .chat_user_td {
          margin-top: 5px;
          color: #8c8c8c;
          font-size: 12px;
          display: flex;
          align-items: center;
          .chat_line {
            width: 1px;
            height: 12px;
            margin: 0 8px;
            background-color: #e4e7ed;
          }
          .chat_address,
          .chat_time {
            display: flex;
            align-items: center;
            i {
              font-size: 12px;
              margin-right: 4px;
            }
          }
          .chat_time {
            .chat_local_time {
              margin-left: 4px;
            }
            i {
              color: #303133;
            }
          }
          .chat_address {
            i {
              color: #bfbfbf;
            }
          }
        }
      }
      .chat_view_all {
        display: flex;
        height: 32px;
        padding: 5px 16px;
        border: 1px solid #b8ccff;
        color: #4e80ff;
        font-size: 14px;
        box-sizing: border-box;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        margin-top: 16px;
      }
    }
    .chat_evaluation {
      padding: 28px 24px;
      border-bottom: 1px solid #dcdfe6;
      box-sizing: border-box;
      .evaluation_title {
        color: $titleColor;
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 20px;
      }
      .evaluation_content {
        font-size: 14px;
        color: #595959;
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        // span {
        // width: 20px;
        i {
          width: 12px;
          height: auto;
          // font-size: 24px;
          margin-right: 10px;
        }
        // }
      }
      .feedback_content {
        background: rgba(171, 185, 206, 0.1);
        padding: 16px;
        .feedback_title {
          color: #595959;
          font-size: 14px;
          font-weight: 500;
          line-height: 22px;
          margin-bottom: 8px;
        }
        .feedback_detail {
          // height: 80px;
          overflow: hidden;
          color: #8c8c8c;
          font-size: 14px;
          font-weight: normal;
          transition: height 0.5s ease;
          overflow-wrap: break-word;
        }
        .expand_box {
          color: #4b7eff;
          margin-top: 12px;
          display: inline-flex;
          align-items: center;
          cursor: pointer;
          font-size: 14px;
          i {
            font-size: 10px;
            margin-left: 4px;
          }
        }
      }
    }
    .chat_other_info {
      padding: 28px 24px;
      box-sizing: border-box;
      .other_info_title {
        color: $titleColor;
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 20px;
      }
      .chat_other_info_content {
        display: flex;
        flex-direction: column;
        .chat_other_info_device {
          display: flex;
          align-items: center;
          color: $titleColor;
          font-size: 14px;
          margin-bottom: 10px;
          line-height: 22px;
          .chat_other_info_item:first-child {
            margin-right: 50px;
          }
          .chat_other_info_item {
            display: flex;
            .chat_other_info_device_title {
              color: #8c8c8c;
              margin-right: 8px;
              font-weight: 500;
              min-width: 78px;
            }
            .chat_other_info_device_content {
              color: #595959;
            }
          }
        }
        .chat_other_info_item {
          display: flex;
          align-items: center;
          color: $titleColor;
          font-size: 14px;
          margin-bottom: 10px;
          line-height: 22px;
          .chat_other_info_item_title {
            color: #8c8c8c;
            margin-right: 8px;
            font-weight: 500;
            min-width: 78px;
          }
          .chat_other_info_item_content {
            color: #595959;
            flex: 1;
            //单行文本超出显示省略号
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        .chat_other_info_item:first-child {
          .chat_other_info_item_content {
            //下划线
            text-decoration: underline;
            cursor: pointer;
          }
        }
        .chat_other_info_item:nth-child(2) {
          .chat_other_info_item_content {
            span {
              font-size: 10px;
              width: 20px;
              height: 20px;
              background: #eda73d;
              padding: 4px 3px;
              border-radius: 2px;
              box-sizing: border-box;
              line-height: 20px;
            }
          }
        }
        .chat_other_info_address {
          .chat_other_info_item_content {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 8px;
            // button {
            //   margin-left: 8px;
            //   padding: 1px 8px;
            //   height: 24px;
            //   border: 1px solid #b8ccff;
            //   border-radius: 2px;
            //   color: #4e80ff;
            //   font-size: 14px;
            //   cursor: pointer;
            // }
          }
        }
      }
    }
  }
}
