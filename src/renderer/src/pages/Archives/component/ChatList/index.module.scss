.chat_wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  .chat_spin_box {
    padding: 20px 12px 20px 16px;
    box-sizing: border-box;
    height: 100%;
    overflow-y: auto;
    .noMore_box {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 20px;
      margin-top: 10px;
    }
    &:hover {
      &::-webkit-scrollbar-thumb {
        background: #eaecf3;
        border-radius: 4px;
      }
    }
    //修改滚动条样式
    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }
    &::-webkit-scrollbar-thumb {
      background: #fff;
      border-radius: 4px;
    }
    &::-webkit-scrollbar-track {
      background: #fff;
      border-radius: 4px;
    }
  }
  // }
  .spin_wrapper {
    height: 100%;
    display: inline-block;
  }
  .empty_box {
    height: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .load_wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    // height: 100%;
    z-index: 999;
    .load {
      width: 20px;
      height: 20px;
      margin-right: 10px;
    }
  }
  // overflow: hidden;
}
