import { getUuid } from '@/utils/util'
import ListItem from '../ListItem'
import styles from './index.module.scss'
import {
  forwardRef,
  memo,
  useEffect,
  useImperativeHandle,
  useRef,
  useState
} from 'react'
import { Spin } from 'antd'
interface ChatListProps {
  listData: any[]
  onScroll?: () => any
  loading?: boolean
  noMore?: boolean
  clickItem?: (index: number) => void
}

const ChatList = forwardRef(function ChatList(props: ChatListProps, ref) {
  const { listData, loading, noMore, onScroll, clickItem } = props
  const [activeIndex, setActiveIndex] = useState<number | null>(null)
  const targetRef = useRef<HTMLDivElement>(null)
  const uuid = getUuid()
  //缓存uuid
  const cacheUuid = useRef(uuid)
  //暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    //获取当前激活的index
    getActiveIndex: () => {
      return activeIndex
    },
    // 设置当前激活的index
    setActiveIndex: (index: number) => {
      setActiveIndex(index)
    }
  }))
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        for (const entry of entries) {
          console.log(entry)
          if (entry.isIntersecting === true) {
            console.log('进入可视区域')
            // 检测节点是否可见
            // 处于交叉可见状态
            console.log('noMore', noMore)
            console.log('loading', loading)
            if (noMore || loading) return
            console.log('触发滚动事件')
            onScroll && onScroll()
          }
        }
      },
      { threshold: 0.6 }
    )

    // 往观察器列表注册观察元素
    targetRef.current && observer.observe(targetRef.current)
    return () => {
      observer.disconnect()
    }
  }, [])
  //通过事件委托，获取点击的元素，获取item的index
  const itemHandler = (e: any) => {
    while (e.target) {
      if (e.target.dataset?.type) {
        setActiveIndex(Number(e.target.dataset.type))
        clickItem && clickItem(Number(e.target.dataset.type))
        break
      }
      e.target = e.target.parentNode
    }
  }
  return (
    <div className={styles.chat_wrapper}>
      <Spin
        spinning={loading}
        tip={'loading data'}
        wrapperClassName={styles.spin_wrapper}
        style={{ height: '100%', width: '100%' }}
      >
        <div className={styles.chat_spin_box} onClick={itemHandler}>
          {listData?.map((item, index) => {
            const newData = {
              ...item,
              customerServiceName: item.customerServerName,
              msg: item.message,
              evaluate: item.star
            }
            return (
              <ListItem
                key={index + cacheUuid.current}
                itemData={newData}
                isActive={activeIndex === index}
                index={index}
              />
            )
          })}
          <div className={styles.noMore_box} ref={targetRef}>
            no more data
          </div>
        </div>
      </Spin>
    </div>
  )
})

export default memo(ChatList)
