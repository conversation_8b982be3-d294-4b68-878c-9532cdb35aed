import FsAvatarProps from '@/component/FsAvatar'
import styles from './index.module.scss'
import type { MenuProps } from 'antd'
import { Dropdown, message } from 'antd'
import { createTicket, setChatMessage } from '@renderer/api/common'
import { useSelector } from 'react-redux'
import { getCurrentTimeZone } from '@renderer/utils/util'
interface ArchiveHeaderProps {
  userInfo: any
}

export default function ArchiveHeader(props: ArchiveHeaderProps) {
  const globalUserInfo = useSelector((state: any) => state.login.userInfo)
  const { userInfo } = props
  const items: MenuProps['items'] = [
    {
      key: '1',
      label: <>Add Case</>
    },
    {
      key: '2',
      label: <>Download Transcript</>
    }
  ]
  //发送聊天记录到邮箱
  const sendChatRecord = async () => {
    if (!userInfo.email) {
      message.error('The email address is empty')
      return
    }
    try {
      await setChatMessage({
        cc: globalUserInfo.email,
        email: userInfo.email,
        groupIdStr: userInfo.groupIdStr,
        languagesId: 1,
        timeZone: getCurrentTimeZone()
      })
      message.success('Send successfully')
    } catch (error) {
      console.log(error)
      message.error('Send failed')
    }
  }
  //创建ticket
  const createTicketRequest = async () => {
    console.log('createTicket')
    try {
      const param: CreateTicketType = {
        customersId: userInfo.userId,
        customersServiceId: userInfo.customerServiceIdStr,
        groupId: userInfo.groupIdStr
      }
      await createTicket({
        ...param
      })
      message.success('Create successfully')
    } catch (error) {
      console.log(error)
    }
  }
  const onClick: MenuProps['onClick'] = ({ key }) => {
    console.log(`selected ${key}`)
    if (key === '2') {
      sendChatRecord()
    } else if (key === '1') {
      createTicketRequest()
    }
  }
  return (
    <div className={styles.chat_header_wrapper}>
      <div className={styles.chat_header_left}>
        <FsAvatarProps
          name={userInfo.name || userInfo.userId + ''}
          backgroundColor={userInfo.colour}
        ></FsAvatarProps>
        <span>{userInfo.name || userInfo.userId + ''}</span>
      </div>
      <div className={styles.chat_header_right}>
        <Dropdown menu={{ items, onClick }} placement="bottomLeft">
          <i className="icon-hanbaocaidan iconfont"></i>
        </Dropdown>
      </div>
    </div>
  )
}
