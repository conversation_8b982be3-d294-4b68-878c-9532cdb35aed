import { useEffect, useRef, useState } from 'react'
interface pageType {
  current: number
  size: number
}
interface SearchListProps {
  pageInfo?: pageType
  getListData?: (params: any) => Promise<any>
  searchParam?: Record<string, any>
}

function useSearchList(props: SearchListProps) {
  const { pageInfo, getListData, searchParam } = props
  const [total, setTotal] = useState(0)
  const [searchParams, setSearchParams] =
    useState<Record<string, any>>(searchParam)
  const [pagination, setPagination] = useState<Record<string, any>>(pageInfo)
  const [listData, setListData] = useState([])
  const [noMore, setNoMore] = useState(false)
  const [loading, setLoading] = useState(false)
  useEffect(() => {
    console.log('pagination', pagination)
    setLoading(true)
    getListData({
      ...searchParams,
      ...pagination
    })
      .then((res) => {
        const { records = [], current, size, total } = res
        console.log('res', records)
        if (current === 1) {
          setListData(records)
        } else {
          setListData((pre) => [...pre, ...records])
        }
        setTotal(total)
        setLoading(false)
        const newNoMore = total <= current * size
        console.log('noMore', newNoMore)
        if (newNoMore) {
          setNoMore(newNoMore)
        }
      })
      .finally(() => {
        setLoading(false)
      })
  }, [searchParams, pagination])
  return {
    listData,
    setSearchParams,
    total,
    noMore,
    loading,
    setPagination,
    setListData,
    setNoMore
  }
}

export default useSearchList
