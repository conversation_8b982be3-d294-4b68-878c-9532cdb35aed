import ChatView from '@/component/ChatView'
import ArchiveHeader from './component/ArchiveHeader'
import styles from './style/index.module.scss'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
dayjs.extend(utc)
import {
  createElement,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react'
import ChatList from './component/ChatList'
import ArchiveFilter from './component/ArchiveFilter'
import ChatDetail from './component/ChatDetail'
import useSearchList from './hook/useSearchList'
import { getChatDetail, getChatUserList } from '@/api/archives'
import { useSelector } from 'react-redux'
import Empty from '@/component/Empty'
import { formatUTC, formatUTCToFull, getCurrentTimeZone } from '@/utils/util'
import { Button, Spin } from 'antd'
import { useSearchParams } from 'react-router-dom'
import useChatInfo from '@renderer/hooks/useChatInfo'
import eventBus from '@renderer/utils/eventBus'
export type ListType = MessageItemProps & {
  //评价
  evaluate?: string
  idStr?: string
}
interface DetailInfoType {
  detailFeedback: any
  groupUserChatDetailVO: any
}
type SearchType = Omit<ChatUserListType, 'size' | 'current'>
export default function Archives() {
  const [searchParams] = useSearchParams()
  const urlParam = searchParams.get('type')
  const customerInfo = useSelector((state: any) => state.login.customerInfo)
  const [viewAllVisible, setViewAllVisible] = useState<boolean>(false)
  const [listLoading, setListLoading] = useState<boolean>(false)
  const noMoreListRef = useRef<boolean>(false)
  const chatViewRef = useRef(null)
  const prevScrollHeight = useRef(0)
  const indexRef = useRef(null)
  const [detailInfo, setDetailInfo] = useState<DetailInfoType>({
    detailFeedback: {},
    groupUserChatDetailVO: {}
  })
  const { setChatDetail } = useChatInfo()
  const moreRef = useRef(false)
  const chatListRef = useRef<any>(null)
  console.log(requestByParam())
  const [formDataObj, setFormDataObj] = useState<SearchType>({
    customerServiceIdStr: customerInfo.userId,
    siteId: NaN,
    rateId: undefined,
    searchDate: [],
    keyWords: '',
    transferType: NaN,
    customersIdStr: '',
    ...requestByParam()
  })
  const detailParamsRef = useRef<ChatDetailType>({
    current: 1,
    size: 10,
    groupIdStr: '',
    timeZone: getCurrentTimeZone()
  })

  const [messageData, setMessageData] = useState<MessageItemProps[]>([])
  const getDataCallback = async (params: ChatUserListType) => {
    try {
      const res: any = await getChatUserList(params)
      return res
    } catch (error: any) {
      console.log(error)
      return {}
    }
  }
  const {
    listData,
    setSearchParams,
    total,
    noMore,
    loading,
    setPagination,
    setListData,
    setNoMore
  } = useSearchList({
    pageInfo: { current: 1, size: 10 },
    getListData: getDataCallback,
    searchParam: { ...formDataObj }
  })
  function requestByParam() {
    switch (urlParam) {
      case 'accepted':
        return {
          searchDate: [
            dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
            dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
          ],
          rateId: undefined
        }
      case 'good':
        return {
          searchDate: [
            dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
            dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
          ],
          rateId: 1
        }
      case 'bad':
        return {
          searchDate: [
            dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
            dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
          ],
          rateId: 2
        }

      default:
        return {}
    }
  }
  //表单筛选区数据变化回调
  const formChange = (values: SearchType) => {
    console.log(values)
    setFormDataObj(values)
    setSearchParams(values)
    resetInit()
  }
  //重置聊天详情参数
  const resetInit = () => {
    detailParamsRef.current = {
      groupIdStr: '',
      current: 1,
      size: 10,
      timeZone: getCurrentTimeZone()
    }
    setMessageData([])
    setDetailInfo({
      detailFeedback: {},
      groupUserChatDetailVO: {}
    })
    setPagination((prev) => {
      return {
        ...prev,
        current: 1
      }
    })
    setNoMore(false)
  }
  //获取聊天详情
  const getChatDetailRequest = async (
    params: ChatDetailType,
    isReset = false
  ) => {
    if (isReset) {
      noMoreListRef.current = false
    }
    if (noMoreListRef.current) return
    setListLoading(true)
    try {
      const res: any = await getChatDetail(params)
      console.log(res)
      const { messageList, detailFeedback, groupUserChatDetailVO } = res
      setDetailInfo({
        detailFeedback,
        groupUserChatDetailVO
      })
      setChatDetail(groupUserChatDetailVO)
      if (detailParamsRef.current.current === messageList.pages) {
        noMoreListRef.current = true
      }
      setMessageData((prev) => {
        const addList = messageList.records.map((item: any) => {
          return {
            ...item,
            messageTime: formatUTC(item.messageTime),
            fullTime: formatUTCToFull(item.messageTime)
          }
        })
        if (isReset) return addList
        return [...addList, ...prev]
      })
    } catch (error: any) {
      console.log(error)
      throw new Error(error)
    } finally {
      setListLoading(false)
    }
  }
  //左侧列表滚动回调
  const onScroll = useCallback(() => {
    console.log('moreRef.current', moreRef.current)
    if (moreRef.current) return
    setPagination((prev) => {
      const current = moreRef.current ? prev.current : prev.current + 1
      return {
        ...prev,
        current
      }
    })
  }, [moreRef.current])
  //点击view all,填充用户id到搜索框
  const viewAll = (id: string) => {
    setViewAllVisible(true)
    setFormDataObj((prev) => {
      return {
        ...prev,
        customersIdStr: id
      }
    })
    setListData([])
    setSearchParams((prev) => {
      return {
        customerServiceIdStr: prev.customerServiceIdStr,
        siteId: NaN,
        rateId: NaN,
        searchDate: [],
        transferType: NaN,
        keyWords: id,
        customersIdStr: id
      }
    })
    resetInit()
  }
  //点击左侧列表回调
  const clickItem = (index: number) => {
    if (chatListRef.current.getActiveIndex()) {
      noMoreListRef.current = false
    }
    if (index === chatListRef.current && chatListRef.current.getActiveIndex())
      return
    //设置当前激活的index
    indexRef.current = index
    chatListRef.current && chatListRef.current.setActiveIndex(index)
    const item: any = listData[index]
    //设置聊天详情请求参数
    detailParamsRef.current = {
      groupIdStr: item.groupIdStr,
      current: 1,
      size: 10,
      timeZone: getCurrentTimeZone()
    }
    // 重置聊天详情数据
    setMessageData([])
    //获取聊天详情
    getChatDetailRequest(detailParamsRef.current, true)
  }
  //设置滚动条回到底部
  const scrollBottom = (domRef: any) => {
    domRef && domRef.scrollToBottom()
  }
  //滚动顶部加载更多
  const loadMore = (domRef: React.MutableRefObject<HTMLDivElement>) => {
    prevScrollHeight.current = domRef.current.scrollHeight
    if (isScrollTop(domRef)) {
      console.log('到底了')
      //执行加载更多
      detailParamsRef.current = {
        ...detailParamsRef.current,
        current: detailParamsRef.current.current + 1
      }
      getChatDetailRequest(detailParamsRef.current)
    }
  }
  //判断是否滚动到顶部
  const isScrollTop = (domRef: React.MutableRefObject<HTMLDivElement>) => {
    const { scrollTop } = domRef.current
    return scrollTop <= 20
  }
  //切换到上或者下一个聊天
  const switchChat = (type: 'prev' | 'next') => {
    const preIndex = chatListRef.current?.getActiveIndex()
    let index = preIndex || 0
    if (type === 'prev') {
      index = index - 1
      chatListRef.current && chatListRef.current.setActiveIndex(index)
    } else {
      index = index + 1
      chatListRef.current && chatListRef.current.setActiveIndex(index)
    }
    clickItem(index)
  }
  //上一个聊天指向的显示条件
  const prevVisible = useMemo(() => {
    const index = indexRef.current
    const listLength = listData.length
    const hasPre = index > 0 && index <= listLength
    const condition = listLength > 0 && hasPre
    return condition
  }, [listData, indexRef.current])
  //下一个聊天指向的显示条件
  const nextVisible = useMemo(() => {
    const index = indexRef.current
    const listLength = listData.length
    console.log(index, listLength)
    const hasNext = index < listLength - 1
    const condition = listLength > 0 && hasNext
    console.log(condition)
    return condition
  }, [listData, indexRef.current])
  //退出view all
  const exitViewAll = () => {
    setViewAllVisible(false)
    setFormDataObj((prev) => {
      return {
        ...prev,
        customersIdStr: ''
      }
    })
    setListData([])
    setSearchParams((prev) => {
      return {
        customerServiceIdStr: prev.customerServiceIdStr,
        siteId: NaN,
        rateId: NaN,
        searchDate: [],
        transferType: NaN,
        keyWords: '',
        customersIdStr: ''
      }
    })
    resetInit()
  }
  useEffect(() => {
    if (detailParamsRef.current.current === 1) {
      scrollBottom(chatViewRef.current)
    } else {
      //保持滚动条位置
      const domRef = chatViewRef.current.chatViewRef.current
      const { scrollHeight } = domRef
      domRef.scrollTop = scrollHeight - prevScrollHeight.current
    }
    /**
     * @description 更新ticketBtn状态
     */
    const ticketHandler = (data: any) => {
      const btnIndex = messageData.findIndex(
        (item) => item.ticketId == data.ticketId
      )
      setMessageData((prev) => {
        const updateData = [...prev]
        updateData[btnIndex] = {
          ...updateData[btnIndex],
          msg: JSON.stringify(data)
        }
        return [...updateData]
      })
    }

    console.log('Archives 注册 updateTicketBtn 监听器');
    const unsubscribe = eventBus.on('updateTicketBtn', ticketHandler)

    return () => {
      console.log('Archives 组件卸载，移除 updateTicketBtn 监听器');
      unsubscribe()
    }
  }, [messageData.length])
  useEffect(() => {
    moreRef.current = noMore
  }, [noMore])
  useEffect(() => {
    if (viewAllVisible && listData.length > 0) {
      clickItem(0)
    }
  }, [listData, viewAllVisible])
  return (
    <div className={styles.archives_wrapper}>
      {/* 头部筛选区域 */}
      <div className={styles.filter_wrapper}>
        {!viewAllVisible ? (
          <ArchiveFilter
            disabled={viewAllVisible}
            formDataObj={formDataObj}
            formChange={formChange}
            unit={listData.length}
          />
        ) : (
          <Button type="primary" onClick={exitViewAll}>
            Back
          </Button>
        )}
      </div>
      {/* 内容区域 */}
      <div className={styles.content_wrapper}>
        {/* 左侧聊天列表 */}
        <div className={styles.chat_list_wrapper}>
          {listData.length > 0 ? (
            <ChatList
              listData={listData}
              onScroll={onScroll}
              loading={loading}
              noMore={noMore}
              clickItem={clickItem}
              ref={chatListRef}
            />
          ) : (
            <Empty wrapperClassName={styles.empty_list_box} />
          )}
        </div>
        {/* 中间聊天内容 */}
        <div className={styles.chat_content_wrapper}>
          {messageData.length && messageData.length > 0 ? (
            <>
              {/* 前一个 */}
              {viewAllVisible && prevVisible && (
                <div
                  className={styles.chat_prev}
                  onClick={() => switchChat('prev')}
                >
                  <i className="iconfont icon-xiangshang"></i>
                  <span>Previous chat with this customer</span>
                </div>
              )}
              <Spin
                spinning={listLoading}
                style={{ height: '100%' }}
                wrapperClassName={styles.spin_self_view}
              >
                <ChatView
                  headerBox={createElement(ArchiveHeader, {
                    userInfo: detailInfo.groupUserChatDetailVO
                  })}
                  // footerBox={createElement(ArchiveFooter)}
                  messageData={messageData}
                  loadMore={loadMore}
                  chatWrapRef={chatViewRef}
                />
              </Spin>
              {viewAllVisible && nextVisible && (
                <div
                  className={styles.chat_next}
                  onClick={() => switchChat('next')}
                >
                  <i className="iconfont icon-xiangxia"></i>
                  <span>Next chat with this customer</span>
                </div>
              )}
              {/* 后一个 */}
            </>
          ) : (
            <Empty
              style={{ width: '170px', height: '170px' }}
              wrapperClassName={styles.empty_wrapper_box}
              isShowText
              type="chatEmpty"
            />
          )}
        </div>
        {/* 右侧聊天详情 */}
        <div className={styles.chat_detail_wrapper}>
          {messageData.length && messageData.length > 0 ? (
            <ChatDetail viewAll={viewAll} {...detailInfo} />
          ) : (
            <Empty wrapperClassName={styles.empty_detail} />
          )}
        </div>
      </div>
    </div>
  )
}
