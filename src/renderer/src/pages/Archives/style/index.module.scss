.archives_wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #fff;
  box-sizing: border-box;
  overflow: hidden;
  @mixin pre-btn() {
    transform: translateX(-50%);
    // width: 243px;
    height: 32px;
    border-radius: 2px;
    background-color: #fff;
    padding: 5px 16px;
    box-sizing: border-box;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
    color: #4e80ff;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    span {
      margin-left: 8px;
    }
  }
  .filter_wrapper {
    padding: 20px;
    // height: 64px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    background-color: #fbfbfc;
  }
  .content_wrapper {
    flex: 1;
    display: flex;
    overflow: hidden;
    .chat_list_wrapper {
      @media screen and (max-width: 1680px) {
        width: 300px;
      }
      @media screen and (min-width: 1680px) and (max-width: 1920px) {
        width: 360px;
      }
      // width: 430px;
      height: 100%;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      .empty_list_box {
        // background-color: #f2f4fa;
        height: 100%;
      }
    }
    .chat_content_wrapper {
      flex: 1;
      height: 100%;
      position: relative;
      .spin_self_view {
        height: 100%;
      }
      .empty_wrapper_box {
        background-color: #f2f4fa;
        height: 100%;
      }
      .chat_prev {
        position: absolute;
        top: 92px;
        left: 50%;
        @include pre-btn();
      }
      .chat_next {
        position: absolute;
        bottom: 104px;
        left: 50%;
        @include pre-btn();
      }
    }
    .chat_detail_wrapper {
      @media screen and (max-width: 1680px) {
        width: 400px;
      }
      @media screen and (min-width: 1680px) and (max-width: 1920px) {
        width: 430px;
      }
      // width:430px;
      height: 100%;
      display: flex;
      flex-direction: column;
      .empty_detail {
        height: 100%;
      }
    }
  }
}
