import React, { useEffect, useState } from 'react'
import styles from '../../style/chatInfo.module.scss'
import UserInfo from './UserInfo'
import Shortcut from './Shortcut/index'
import useChatInfo from '@/hooks/useChatInfo'
import Empty from '@/component/Empty'
import userIconActive from '@/assets/image/user_active.svg'
import userIconDefault from '@/assets/image/user_default.svg'
import shortcutIconActive from '@/assets/image/shortcut_active.svg'
import shortcutIconDefault from '@/assets/image/shortcut_default.svg'
import gptIconActive from '@/assets/image/gpt_active.svg'
import gptIconDefault from '@/assets/image/gpt_default.svg'
import GPTPage from './GPTPage'

export default React.memo(function ChatInfo() {
  const [chatInfoTab, setChatInfoTab] = useState(0)
  const [isShowChatDetail, setIsShowChatDetail] = useState(false)
  const { chatRoomInfo } = useChatInfo()
  const chatInfoTabList = [
    {
      active: userIconActive,
      default: userIconDefault
    },
    {
      active: shortcutIconActive,
      default: shortcutIconDefault
    },
    {
      active: gptIconActive,
      default: gptIconDefault
    }
  ]
  const handleTabChange = (index: number) => {
    setChatInfoTab(index)
  }
  useEffect(() => {
    if (chatRoomInfo.groupId !== '0') {
      setIsShowChatDetail(true)
    } else {
      setIsShowChatDetail(false)
    }
  }, [chatRoomInfo.groupId])
  return (
    <div className={styles.chat_info_wrapper}>
      {/* tab切换 */}
      <div className={styles.chat_info_tab}>
        {chatInfoTabList.map((item, index) => {
          return (
            <div
              key={index}
              onClick={() => handleTabChange(index)}
              className={[
                styles.chat_info_tab_item,
                index === chatInfoTab ? styles.chat_info_tab_active : ''
              ].join(' ')}
            >
              <div className={styles.chat_info_tab_item_icon}>
                <img src={index === chatInfoTab ? item.active : item.default} />
              </div>
            </div>
          )
        })}
        {/* <div className={styles.chat_info_tab_item}>
          <div className={styles.chat_info_tab_item_icon}>
            <i className="iconfont icon-a-shuxing1a1yonghushuxing2a0xuanzhong"></i>
          </div>
        </div>
        <div
          className={[
            styles.chat_info_tab_item,
            styles.chat_info_tab_active
          ].join(' ')}
        >
          <div className={styles.chat_info_tab_item_icon}>
            <i className="iconfont icon-a-shuxing1a0kuaijiexiaoxishuxing2a2dianji"></i>
          </div>
        </div> */}
      </div>
      {chatInfoTab === 0 &&
        (isShowChatDetail ? (
          <UserInfo />
        ) : (
          <div className={styles.empty_wrap}>
            <Empty style={{ width: '120px', height: '120px' }} isShowText />
          </div>
        ))}
      {chatInfoTab === 1 && <Shortcut />}
      {chatInfoTab === 2 && <GPTPage />}
    </div>
  )
})
