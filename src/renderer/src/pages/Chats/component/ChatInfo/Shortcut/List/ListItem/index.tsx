import { useContext, useState } from 'react'
import useChatInfo from '@/hooks/useChatInfo'
import {
  deleteShortcutGroup,
  deleteShortcut,
  putEditShortcutGroup
} from '@/api/chat'
import { Modal, Input, message, Popconfirm, Tooltip } from 'antd'
import styles from './index.module.scss'
import Collapse from '@/component/Collapse'
import ShortcutContext from '@renderer/context/shortcutContext'
import eventBus from '@renderer/utils/eventBus'
type itemDataProps = {
  itemData: ShortcutData
}
function ListItemData(props: itemDataProps) {
  const { itemData } = props
  const shortcutContext = useContext(ShortcutContext)
  const { permission, handleUpdateDate, onAddOrEditShortcut } = shortcutContext
  const {
    setEditShortcutGroup,
    editShortcutGroup,
    setSelectedShortcut,
    chatRoomInfo
  } = useChatInfo()

  const [show, setShow] = useState<boolean>(true)
  // 编辑组弹框开启关闭变量
  const [editGroupModal, setEditGroupModal] = useState<boolean>(false)
  // 编辑组名input的值
  const [editGroupName, setEditGroupName] = useState<string>(itemData.name)
  // 编辑组名提交时的loading显示与否
  const [editGroupLoading, setEditGroupLoading] = useState<boolean>(false)

  // 打开编辑快捷语弹框函数
  const editShortcutModal = (data: any, groupId) => {
    onAddOrEditShortcut(data, groupId)
  }
  const handleEditGroupNameValue = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setEditGroupName(e.target.value)
  }

  // 确定删除快捷语组
  const deleteGroup = async (id: number) => {
    await deleteShortcutGroup(id).then(() => {
      message.success('Delete successfully')
      updateDate()
    })
  }

  // 确定删除快捷语
  const deleteShortcutItem = async (id: number) => {
    await deleteShortcut(id).then(() => {
      message.success('Delete successfully')
      updateDate()
    })
  }

  // 通知祖组件更新数据
  const updateDate = () => {
    handleUpdateDate()
  }

  const shortcutClick = (data: Shortcuts) => {
    if (chatRoomInfo.groupId === '0') return
    setSelectedShortcut(data.content)
    eventBus.emit('shortcut', data.content)
  }
  return (
    <div className={styles.group_wrap}>
      <div className={styles.head}>
        <div style={{ lineHeight: '34px' }}>{itemData.name}</div>
        <div className={styles.option_icon}>
          <span className={styles.hover_option}>
            {itemData.permissions.some((item) => item.status) && (
              <i
                className="iconfont"
                onClick={() => {
                  setEditShortcutGroup({ name: itemData.name, id: itemData.id })
                  setEditGroupModal(true)
                  // onEditShortcutGroup(itemData)
                }}
              >
                &#xe64a;
              </i>
            )}
            {itemData.permissions.some((item) => item.status) && (
              <Popconfirm
                title="Delete this group?"
                description="Are you sure to delete this group?"
                onConfirm={() => deleteGroup(itemData.id)}
                okText="Yes"
                placement="left"
              >
                <i className="iconfont">&#xe655;</i>
              </Popconfirm>
            )}
          </span>
          <i
            onClick={() => {
              if (itemData.shortcuts.length) {
                setShow(!show)
              }
            }}
            className={`iconfont down icon-xia ${
              itemData.show ? styles.active : ''
            }`}
          ></i>
        </div>
      </div>
      {itemData.shortcuts.length > 0 && (
        <Collapse open={show}>
          <div className={styles.dropdown}>
            {itemData.shortcuts.map((cut, index: number) => {
              return (
                <div
                  className={styles.line}
                  onClick={() => shortcutClick(cut)}
                  key={index}
                >
                  {/* <a-tooltip
                      v-if="cut.content.length > 50"
                      placement="left"
                    >
                      <template slot="title">
                        <div v-html="cut.content"></div>
                      </template>
                      <div className={styles.text">
                        <span className={styles.cut">#{{ cut.prompt }}</span>
                        <span className={styles.text_wrap" v-html="cut.content"></span>
                      </div>
                    </a-tooltip> */}
                  <div className={styles.text}>
                    <span className={styles.cut}>#{cut.prompt}</span>
                    {cut.content.length > 50 ? (
                      <Tooltip title={cut.content}>
                        <span
                          className={styles.text_wrap}
                          dangerouslySetInnerHTML={{ __html: cut.content }}
                        ></span>
                      </Tooltip>
                    ) : (
                      <span
                        className={styles.text_wrap}
                        dangerouslySetInnerHTML={{ __html: cut.content }}
                      ></span>
                    )}
                  </div>
                  {cut.permissions.some((item) => item.status) && (
                    <div className={styles.option}>
                      <i
                        className="iconfont"
                        onClick={() => editShortcutModal(cut, itemData.id)}
                      >
                        &#xe64a;
                      </i>
                      <Popconfirm
                        title="Delete this group?"
                        description="Are you sure to delete this shortcut?"
                        onConfirm={() => deleteShortcutItem(cut.id)}
                        okText="Yes"
                        placement="left"
                      >
                        <i className="iconfont">&#xe655;</i>
                      </Popconfirm>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </Collapse>
      )}

      {/* 编辑组名弹框 */}
      <Modal
        title="Edit Group"
        open={editGroupModal}
        okText="Confirm"
        width={400}
        centered
        zIndex={99}
        onCancel={() => {
          setEditGroupModal(false)
          setEditGroupName('')
        }}
        confirmLoading={editGroupLoading}
        onOk={async () => {
          setEditGroupLoading(true)
          await putEditShortcutGroup(editShortcutGroup.id, {
            name: editGroupName
          })
            .then(() => {
              message.success('Edit successfully')
              setEditGroupModal(false)
              setEditGroupLoading(false)
              updateDate()
            })
            .catch(() => {
              setEditGroupLoading(false)
            })
        }}
      >
        <p style={{ margin: '20px 0 8px' }}>Edit group</p>
        <Input
          showCount
          maxLength={16}
          onChange={handleEditGroupNameValue}
          value={editGroupName}
        />
      </Modal>
    </div>
  )
}

export default ListItemData
