import { useState, useEffect, useMemo, useCallback, useRef } from 'react'
import {
  getShortcut,
  getShortcutGroupList,
  getShortcutPermission,
  postShortcut,
  postShortcutCreateGroup,
  putEditShortcut,
  putEditShortcutGroupName
} from '@/api/chat'

import useChatInfo from '@/hooks/useChatInfo'

import List from './List'
import styles from './index.module.scss'
import { Input, Button, Modal, message, Spin, Select } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import ShortcutContext from '@renderer/context/shortcutContext'

const Search = Input.Search
const TextArea = Input.TextArea
const Option = Select.Option
type ModalValueTypes = {
  content: string
  prompt: string
  groupId: number | null
}
function Shortcut() {
  const {
    isShowAddOrEditShortcutModal,
    setIsShowAddOrEditShortcutModal,
    editShortcutGroup,
    setEditShortcutGroup
  } = useChatInfo()
  const [listData, setListData] = useState<any[]>([])
  // 是否有权限进行新增快捷语
  const [addShortcut, setAddShortcut] = useState<boolean>(false)
  //搜索的值
  const [search, setSearch] = useState<string>('')
  // 显示个人或团队的快捷语变量
  const [showType, setShowType] = useState<number>(1)
  // 新增快捷语组组名
  const [addShortcutGroupName, setAddShortcutGroupName] = useState<string>(
    editShortcutGroup.name
  )
  // 编辑组弹框的标题
  const [editGroupModalTitle, setEditGroupModalTitle] =
    useState<string>('Add New Group')
  const shortcutId = useRef(0)
  // 是新增还是编辑快捷语
  const [isAddShortcut, setIsAddShortcut] = useState<boolean>(true)
  // 新增快捷语/组弹框开启关闭变量
  const [addOrEditModalOpen, setAddOrEditModalOpen] = useState<boolean>(false)
  // 新增快捷语/组弹框标题名称
  const [addOrEditModalTitle, setAddOrEditModalTitle] = useState<string>(
    'Add Canned Response'
  )
  // 新增/编辑快捷语确定loading
  const [addOrEditModalLoading, setAddOrEditModalLoading] =
    useState<boolean>(false)

  // modal中的值
  const [modalValue, setModalValue] = useState<ModalValueTypes>({
    content: '',
    prompt: '',
    groupId: showType === 1 ? 1 : 2
  })
  // 快捷语组列表
  const [shortcutGroupList, setShortcutGroupList] = useState<any[]>([])

  // 是否显示加载图标
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const fetchShortcut = async (typeId: number, search: string) => {
    setIsLoading(true)
    const res: any = await getShortcut({ typeId, search })
    setListData(res)
    setIsLoading(false)
  }
  // 获取快捷语组列表
  const fetchShortcutGroupList = async () => {
    const res: any = await getShortcutGroupList({ typeId: showType })
    setShortcutGroupList(res)
  }
  // 获取快捷语编辑权限
  const fetchShortcutPermission = async () => {
    const res: any = await getShortcutPermission({ typeId: showType })
    setAddShortcut(res.permission)
  }
  useEffect(() => {
    fetchShortcut(showType, search)
  }, [showType, search])

  useMemo(() => {
    fetchShortcutPermission()
  }, [showType])
  // useEffect(() => {
  //   addOrEditModalOpen && fetchShortcutGroupList()
  // }, [addOrEditModalOpen])
  useEffect(() => {
    if (editShortcutGroup.id) {
      setAddShortcutGroupName(editShortcutGroup.name)
      setEditGroupModalTitle('Edit Group Name')
    }
  }, [editShortcutGroup])

  // 获取个人或组快捷语
  const handleChangeShortcutType = (type: number) => {
    setShowType(type)
  }
  // 打开新增快捷语/组弹框
  const handleOpenAddModal = () => {
    resetFormData()
    setAddOrEditModalTitle('Add Canned Response')
    setAddOrEditModalOpen(true)
    fetchShortcutGroupList()
    setIsAddShortcut(true)
  }
  const resetFormData = () => {
    setModalValue({
      content: '',
      prompt: '',
      groupId: showType === 1 ? 1 : 2
    })
    setAddOrEditModalLoading(false)
  }
  // 更新数据
  const handleUpdateDate = () => {
    fetchShortcut(showType, search)
  }
  // const onEditShortcutGroup = (data: ShortcutData) => {
  //   console.log(data)
  //   // setEditGroupModalTitle('Edit Group Name')
  //   // setIsShowAddOrEditShortcutModal(true)
  // }

  const onAddOrEditShortcut = (cut, groupId) => {
    console.log(cut, groupId)
    setAddOrEditModalOpen(true)
    setIsAddShortcut(false)
    setAddOrEditModalTitle('Edit Canned Response')
    fetchShortcutGroupList()
    shortcutId.current = cut.id
    setModalValue({
      ...modalValue,
      content: cut.content,
      prompt: cut.prompt,
      groupId: groupId
    })
  }
  // 新增快捷语组
  const addOrEditShortcutGroup = async () => {
    if (editShortcutGroup.id) {
      await putEditShortcutGroupName(editShortcutGroup.id, {
        name: addShortcutGroupName
      }).then(() => {
        message.success('Edit Group Name Success!!!')
        setAddShortcutGroupName('')
        setIsShowAddOrEditShortcutModal(false)
        setEditShortcutGroup({ id: 0, name: '' })
        fetchShortcut(showType, search)
      })
    } else {
      if (!addShortcutGroupName) return message.error('Please enter group name')
      await postShortcutCreateGroup({
        name: addShortcutGroupName,
        typeId: showType
      }).then(() => {
        message.success('Add New Group Success!!!')
        setAddShortcutGroupName('')
        setIsShowAddOrEditShortcutModal(false)
        fetchShortcut(showType, search)
        fetchShortcutGroupList()
      })
    }
  }
  // 编辑快捷语提交
  const editShortcutSubmit = async () => {
    if (!modalValue.content) {
      return message.error('Please enter Label')
    }
    if (!modalValue.prompt) {
      return message.error('Please enter Shortcuts')
    }
    if (isAddShortcut) {
      setAddOrEditModalLoading(true)
      await postShortcut({
        content: modalValue.content,
        prompt: modalValue.prompt,
        groupId: modalValue.groupId,
        typeId: showType
      })
        .then(() => {
          message.success('Add successfully')
          setAddOrEditModalOpen(false)
          handleUpdateDate()
        })
        .finally(() => {
          setAddOrEditModalLoading(false)
        })
    } else {
      await putEditShortcut(shortcutId.current, {
        content: modalValue.content,
        prompt: modalValue.prompt,
        groupId: modalValue.groupId
      })
        .then(() => {
          message.success('Edit successfully')
          setAddOrEditModalOpen(false)
          handleUpdateDate()
        })
        .finally(() => {
          setAddOrEditModalLoading(false)
        })
    }
  }

  return (
    <div className={styles.shortcut_wrap}>
      <div className={styles.search_wrap}>
        <Search
          allowClear
          placeholder="Search canned respones"
          onSearch={(value: string) => setSearch(value)}
        />
      </div>
      <div className={styles.main_wrap}>
        <div className={styles.tab_wrap}>
          <div className={styles.tab_item}>
            <span
              className={`${showType === 1 ? styles.active : ''}`}
              onClick={() => handleChangeShortcutType(1)}
            >
              Individual
            </span>
            <span
              className={`${showType === 2 ? styles.active : ''}`}
              onClick={() => handleChangeShortcutType(2)}
            >
              Team
            </span>
          </div>
          {addShortcut && (
            <Button size="small" type="primary" onClick={handleOpenAddModal}>
              Add
            </Button>
          )}
        </div>
        <div className={styles.list_wrap}>
          {isLoading ? (
            <div className={styles.listLoading}>
              <Spin spinning={isLoading} />
            </div>
          ) : (
            <ShortcutContext.Provider
              value={{
                permission: addShortcut,
                shortcutType: showType,
                onAddOrEditShortcut,
                handleUpdateDate
              }}
            >
              <List listData={listData} />
            </ShortcutContext.Provider>
          )}
        </div>
      </div>
      {/* 新增组弹框 */}
      <Modal
        destroyOnClose
        title={editGroupModalTitle}
        okText="Confirm"
        width={400}
        open={isShowAddOrEditShortcutModal}
        centered
        onCancel={() => {
          setIsShowAddOrEditShortcutModal(false)
          setAddShortcutGroupName('')
        }}
        onOk={addOrEditShortcutGroup}
      >
        <p style={{ margin: '20px 0 8px' }} className={styles.modalTitle}>
          {editGroupModalTitle}
        </p>
        <Input
          showCount
          maxLength={16}
          placeholder="please enter group name"
          value={addShortcutGroupName}
          onChange={(
            e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
          ) => setAddShortcutGroupName(e.target.value)}
        />
      </Modal>
      {/* 新增/编辑快捷语，快捷语组弹框 */}
      <Modal
        destroyOnClose
        title={addOrEditModalTitle}
        open={addOrEditModalOpen}
        okText="Confirm"
        width={680}
        centered
        onCancel={() => {
          setAddOrEditModalOpen(false)
        }}
        onOk={editShortcutSubmit}
        confirmLoading={addOrEditModalLoading}
      >
        <p style={{ marginTop: '20px' }} className={styles.modalTitle}>
          Label
        </p>
        <TextArea
          showCount
          maxLength={1000}
          placeholder="please enter canned response text:"
          value={modalValue.content}
          onChange={(
            e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
          ) => setModalValue({ ...modalValue, content: e.target.value })}
        />
        <p style={{ marginTop: '20px' }} className={styles.modalTitle}>
          Shortcuts(hit Enter to add another one)
        </p>
        <Input
          showCount
          maxLength={16}
          placeholder="#"
          value={modalValue.prompt}
          onChange={(
            e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
          ) => setModalValue({ ...modalValue, prompt: e.target.value })}
        />
        <p style={{ marginTop: '20px' }} className={styles.modalTitle}>
          Add to Group
        </p>
        <Select
          style={{ width: '100%', marginBottom: '20px' }}
          showSearch
          placeholder="Select a person"
          // options={shortcutGroupList}
          // fieldNames={{ label: 'name', value: 'id' }}
          onChange={(value: string) => {
            setModalValue({ ...modalValue, groupId: Number(value) })
          }}
          key={modalValue.groupId && modalValue.groupId}
          defaultValue={modalValue.groupId && modalValue.groupId.toString()}
          dropdownRender={(menu: any) => (
            <>
              <div className={styles.addGroup}>
                <Button
                  icon={<PlusOutlined />}
                  type="link"
                  onClick={() => {
                    setIsShowAddOrEditShortcutModal(true)
                  }}
                >
                  Add New Group
                </Button>
              </div>
              {menu}
            </>
          )}
        >
          {shortcutGroupList.map((item: any) => (
            <Option key={item.id} value={item.id.toString()}>
              {item.name}
            </Option>
          ))}
        </Select>
      </Modal>
    </div>
  )
}

export default Shortcut
