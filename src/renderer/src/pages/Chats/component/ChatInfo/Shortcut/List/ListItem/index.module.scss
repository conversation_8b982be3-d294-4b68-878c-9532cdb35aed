.group_wrap {
  line-height: normal;
  font-size: 12px;
  margin-bottom: 8px;
  .head {
    display: flex;
    justify-content: space-between;
    align-content: center;
    padding: 0 16px;
    background: #fafafa;
    height: 34px;
    .option_icon {
      display: flex;
      align-items: center;
      .hover_option {
        display: none;
        margin-right: 8px;
        :global(.iconfont) {
          color: #bfbfbf;
          font-size: 16px;
          margin-right: 8px;
          &:hover {
            color: #232323;
          }
        }
      }
    }
    span {
      display: inline-block;
    }
    i {
      cursor: pointer;
      color: #262626;
      transition: all 0.3s;
      font-size: 14px;
      :global(&.active) {
        transform: rotateX(180deg);
      }
    }
    &:hover {
      .option_icon {
        .hover_option {
          display: inline-block;
        }
      }
    }
  }
  .show_dropdown {
    opacity: 1;
    max-height: 1000px;
    transition: all 0.3s ease;
  }
  .hide_dropdown {
    opacity: 0;
    max-height: 0;
  }
  .dropdown {
    padding: 6px 4px 16px;
    .line {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 18px;
      position: relative;
      cursor: pointer;
      .text {
        display: flex;
        width: 100%;
        .text_wrap {
          flex: 1;
          white-space: nowrap;
          overflow: hidden; //文本超出隐藏
          text-overflow: ellipsis; //文本超出省略号替代
        }
        .cut {
          width: 55px;
          white-space: nowrap;
          overflow: hidden; //文本超出隐藏
          text-overflow: ellipsis; //文本超出省略号替代
          color: #909399;
          margin-right: 12px;
        }
      }
      .option {
        display: none;
        position: absolute;
        top: 0;
        right: 0;
        i {
          cursor: pointer;
          color: #4778c7;
        }
      }
      &:hover {
        .option {
          display: inline-block;
        }
        .text {
          .text_wrap {
            flex: 0.8;
          }
        }
      }
    }
  }
  &:hover {
    .head {
      background: #f0f7ff;
    }
  }
}
.modalTitle {
  color: #232323;
  font-size: 14px;
  margin-bottom: 8px;
}
