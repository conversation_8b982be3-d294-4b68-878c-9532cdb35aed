import React from 'react'
import styles from './index.module.scss'
import classNames from 'classnames/bind'
import gptLogo from '@/assets/image/gpt_logo.svg'
import { But<PERSON>, Divider } from 'antd'
import { ArrowRightOutlined, InfoCircleOutlined } from '@ant-design/icons'
import rightArrow from '@/assets/image/right_arrow.svg'

const cx = classNames.bind(styles)

function GPTPage() {
  const jumpToGPT = () => {
    window.open(
      'https://www.fs.com/technical_documents.html?country=US&languages=English&currency=USD',
      '_blank'
    )
  }
  const jumpToFeedback = () => {
    window.open(
      'https://mah2eds8ab.feishu.cn/share/base/form/shrcnbPV8VdoUdbA9t8OApTMSmh',
      '_blank'
    )
  }
  return (
    <div className={cx('gpt_page_wrapper')}>
      <div className={cx('gpt_page_header')}>
        <div className={cx('gpt_page_header_logo')}>
          <img src={gptLogo} alt="gpt_logo" />
        </div>
        <div className={cx('gpt_page_header_desc')}>
          <div className={cx('title')}>FS GPT AI Assistant</div>
          <div className={cx('text')}>
            Hello! You can visit the FS Mall Document Center page and use the FS
            GPT AI-powered assistant to efficiently search for the materials you
            need. If you have any suggestions or encounter any issues during
            use, please feel free to submit the feedback form to help us improve
            our services. Thank you for your support!
          </div>
        </div>
      </div>
      <div className={cx('gpt_page_button')}>
        <Button type="primary" style={{ width: '100%' }} onClick={jumpToGPT}>
          Search with FS GPT
        </Button>
      </div>
      <Divider />
      <div className={cx('gpt_page_feedback_wrap')}>
        <div className={cx('icon')}>
          <InfoCircleOutlined
            style={{ fontSize: 14, color: '#4B7EFF', marginTop: 2 }}
          />
        </div>
        <div className={cx('content')}>
          <div className={cx('title')}>
            Help us improve! Your thoughts on FS GPT matter.
          </div>
          <div className={cx('link')}>
            <div className={cx('icon')}>
              <img src={rightArrow} alt="right_arrow" />
            </div>
            <span className={cx('link_text')} onClick={jumpToFeedback}>
              Submit feedback
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default GPTPage
