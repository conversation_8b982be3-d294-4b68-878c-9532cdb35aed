.gpt_page_wrapper {
  padding: 20px 24px;
  background-color: #fff;
  .gpt_page_header {
    display: flex;
    align-items: flex-start;
    .gpt_page_header_logo {
      flex-shrink: 0;
      width: 28px;
      height: 28px;
      margin-right: 8px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .gpt_page_header_desc {
      .title {
        font-size: 18px;
        line-height: 26px;
        font-weight: 500;
        color: #262626;
      }
      .text {
        font-size: 12px;
        color: #595959;
        margin-top: 8px;
      }
    }
  }
  .gpt_page_button {
    padding: 28px 0;
    width: 100%;
  }
  .gpt_page_feedback_wrap {
    display: flex;
    align-items: flex-start;
    padding: 10px 12px;
    border-radius: 2px;
    background: rgba(171, 185, 206, 0.1);
    .icon {
      flex-shrink: 0;
      width: 16px;
      height: 16px;
      margin-right: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .content {
      .title {
        font-size: 14px;
        color: #595959;
        margin-bottom: 4px;
      }
      .link {
        display: flex;
        align-items: center;
        cursor: pointer;
        .icon {
          width: 16px;
          height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .link_text {
          font-size: 14px;
          color: #4b7eff;
        }
      }
    }
  }
}
