import style from './index.module.scss'
import FsAvatar from '@/component/FsAvatar'
import useChatInfo from '@/hooks/useChatInfo'
import { RootState } from '@renderer/store'
import { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
function Supervised() {
  const { supervisionList, setSupervisionList } = useChatInfo()
  const currentReceiveMsg = useSelector(
    (state: RootState) => state.message.currentReceiveMsg
  )

  useEffect(() => {
    if (currentReceiveMsg.messageType === 69) {
      setSupervisionList(currentReceiveMsg.memberList)
    }
  }, [currentReceiveMsg, supervisionList])
  return (
    supervisionList.length > 0 && (
      <div className={style.supervised_wrap}>
        <div className={style.title}>Supervised</div>
        <div className={style.supervised_list}>
          {supervisionList.map((item) => (
            <div className={style.supervised_item} key={item.name}>
              <span>Supervised:</span>
              <FsAvatar
                style={{ width: '28px', height: '28px' }}
                src={item.avatar}
              ></FsAvatar>
              <span>{item.name}</span>
            </div>
          ))}
        </div>
      </div>
    )
  )
}

export default Supervised
