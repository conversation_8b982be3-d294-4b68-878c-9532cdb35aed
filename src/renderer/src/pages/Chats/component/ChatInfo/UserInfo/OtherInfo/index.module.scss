.chat_other_info {
  padding: 28px 24px;
  box-sizing: border-box;
  $titleColor: #262626;
  border-bottom: 1px solid #e4e7ed;
  .other_info_title {
    color: $titleColor;
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 20px;
  }
  .chat_other_info_content {
    display: flex;
    flex-direction: column;
    .chat_other_info_device {
      display: flex;
      align-items: center;
      color: $titleColor;
      font-size: 14px;
      margin-bottom: 10px;
      line-height: 22px;
      .chat_other_info_item:first-child {
        margin-right: 50px;
      }
      .chat_other_info_item {
        display: flex;
        .chat_other_info_device_title {
          color: #8c8c8c;
          margin-right: 8px;
          font-weight: 500;
          min-width: 78px;
        }
        .chat_other_info_device_content {
          color: #595959;
        }
      }
    }
    .chat_other_info_item {
      display: flex;
      align-items: center;
      color: $titleColor;
      font-size: 14px;
      margin-bottom: 10px;
      line-height: 22px;
      .chat_other_info_item_title {
        color: #8c8c8c;
        margin-right: 8px;
        font-weight: 500;
        min-width: 78px;
      }
      .chat_other_info_item_content {
        color: #595959;
        flex: 1;
        //单行文本超出显示省略号
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .chat_other_info_item:first-child {
      .chat_other_info_item_content {
        //下划线
        text-decoration: underline;
        cursor: pointer;
      }
    }
    .chat_other_info_item:nth-child(2) {
      .chat_other_info_item_content {
        span {
          font-size: 10px;
          width: 20px;
          height: 20px;
          color: #fff;
          background: #eda73d;
          padding: 4px 3px;
          border-radius: 2px;
          box-sizing: border-box;
          line-height: 20px;
        }
      }
    }
    .chat_other_info_address {
      .chat_other_info_item_content {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 8px;
      }
    }
    .looking_wrap {
      padding: 16px;
      background: rgba(171, 185, 206, 0.1);
      .title {
        @include font14;
        font-weight: 500;
        color: #595959;
        margin-bottom: 8px;
      }
      .content {
        word-break: break-all;
      }
    }
  }
}
