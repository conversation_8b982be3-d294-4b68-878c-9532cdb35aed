import { Badge, Radio, message } from 'antd'
import type { RadioChangeEvent } from 'antd'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigate, useSearchParams } from 'react-router-dom'

import styles from '../../style/contact.module.scss'
import { formatUTC } from '@/utils/util'
import useChatInfo from '@/hooks/useChatInfo'
import { RootState } from '@/store'
import Chatting from './Chatting/index'
import Supervised from './Supervised/index'
import { getFinishedChatList } from '@/api/chat'
import useSendMSg from '@/hooks/useSendMsg'
import eventBus from '@renderer/utils/eventBus'

export type TabPosition = 'chatting' | 'supervised'
const modifyMsg = (item: MessageItemProps) => {
  if (item?.fileInfo) {
    if (['jpeg', 'jpg', 'png', 'heic', 'heif'].includes(item.fileType)) {
      return '【Picture】'
    } else if (
      ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt'].includes(item.fileType)
    ) {
      return `【Folder】${item.fileInfo.fileName}`
    }
  }
  return item.msg
}
export default function ContactList() {
  const [searchParams] = useSearchParams()
  const { sendMsg } = useSendMSg()
  const navigate = useNavigate()
  const tabType = searchParams.get('tab')
  const superviseId = searchParams.get('superviseId')
  const dispatch = useDispatch()
  const {
    chatRoomInfo,
    messageCountMap,
    setChatInfo,
    setMessageCountMap,
    setGlobalChatMode,
    resetMessageCountMap
  } = useChatInfo()
  const userFormData = useSelector(
    (state: RootState) => state.message.userFormData
  )

  const customerInfo = useSelector(
    (state: RootState) => state.login.customerInfo
  )
  const socketStatus = useSelector(
    (state: RootState) => state.common.socketStatus
  )
  const [mode, setMode] = useState<TabPosition>('chatting')
  const [listData, setListData] = useState<Array<ItemType>>([])
  const [supervisedList, setSupervisedList] = useState<Array<ItemType>>([])
  const [finishedChatList, setFinishedChatList] = useState<Array<ItemType>>([])
  const [todayChatData, setTodayChatData] = useState<TodayDataType>({})
  const handleModeChange = (e: RadioChangeEvent) => {
    setMode(e.target.value)
    setChatInfo({ groupId: '0' }) // 重置聊天室信息
  }
  //处理接收到的消息
  const handleReceiveMsg = useCallback(
    (currentMsg: MessageItemProps) => {
      // 32客户发送消息 21客服发送消息
      const { messageVisibilityType, messageType } = currentMsg
      if (messageVisibilityType === 1) {
        // 如果接收的消息不是正在聊天的，增加msg数量
        if (chatRoomInfo.groupId !== currentMsg.groupId) {
          if (messageType === 32) {
            setMessageCountMap(currentMsg)
          }
        }
        updateList(currentMsg)
      } else if (messageType === 56) {
        //获取到在线客户列表
        initListData(currentMsg)
      } else if (messageType === 68) {
        // 即时消息加入列表
        initInstantMsg(currentMsg)
      } else if (messageType === 2) {
        // 客服发送消息
        updateList(currentMsg)
      }
      // 接收关闭聊天的提示
      closeChat(currentMsg)
    },
    [mode, listData, supervisedList, finishedChatList, chatRoomInfo.groupId]
  )

  //订阅接收消息事件
  useEffect(() => {
    console.log('ContactList 注册 receiveMsg 监听器');
    // 使用改进的事件监听，返回取消订阅函数
    const unsubscribe = eventBus.on('receiveMsg', handleReceiveMsg)
    console.log('ContactList 监听器注册完成，当前监听器总数:', eventBus.getListenerCount('receiveMsg'));

    return () => {
      console.log('ContactList 组件卸载，移除监听器');
      // 使用返回的取消订阅函数，只移除当前组件的监听器
      unsubscribe()
      console.log('ContactList 监听器移除完成，剩余监听器总数:', eventBus.getListenerCount('receiveMsg'));
    }
  }, [handleReceiveMsg])

  //更新联系人的用户信息
  useEffect(() => {
    if (Object.keys(userFormData).length !== 0) {
      updateUserInfo(userFormData)
    }
  }, [userFormData])
  // 更新用户信息
  function updateUserInfo(params: MessageItemProps) {
    setListData((prevListData) => {
      const index = prevListData.findIndex(
        (item) => item.groupId === params.groupId
      )
      const updatedListData = [...prevListData]
      updatedListData[index] = {
        ...updatedListData[index],
        name: params.customerName
      }
      return updatedListData
    })
  }

  // 监督页面跳转进入
  useEffect(() => {
    if (tabType === 'supervised') {
      navigate('/chats')
      setMode('supervised')
      setGlobalChatMode(mode) // 设置全局mode状态
      setTimeout(() => {
        setChatInfo({ groupId: superviseId })
      }, 80)
    }
  }, [tabType])
  // tab切换
  useEffect(() => {
    setGlobalChatMode(mode) // 设置全局mode状态
    if (mode === 'chatting') {
      fetchFinishedChatList()
      // fetchTodayChatData()
    }
  }, [mode])

  useEffect(() => {
    if (socketStatus === 'open') {
      fetchSupervisedOrChatting()
    }
  }, [socketStatus])
  // chatting数据
  const formatListData = useMemo(() => {
    let total = 0
    const formattedData = listData.map((item, index) => {
      total += messageCountMap[item.groupId] || 0
      return {
        ...item,
        messageTime: formatUTC(item.messageTime),
        name: item.name || item.fromUserId,
        onlineStatus: 1,
        messageCount: messageCountMap[item.groupId]
      }
    })
    return formattedData
  }, [listData, messageCountMap])

  const chatMessageBadge = useMemo(() => {
    let total = 0
    listData.forEach((item) => {
      total += messageCountMap[item.groupId] || 0
    })
    return total
  }, [listData, messageCountMap])

  const supervisedMessageBadge = useMemo(() => {
    let total = 0
    supervisedList.forEach((item) => {
      total += messageCountMap[item.groupId] || 0
    })
    return total
  }, [supervisedList, messageCountMap])
  // 监督数据
  const formatSupervisedList = useMemo(() => {
    let total = 0
    const formattedData = supervisedList.map((item, index) => {
      total += messageCountMap[item.groupId] || 0
      return {
        ...item,
        name: item.name || item.fromUserId,
        messageTime: formatUTC(item.messageTime),
        onlineStatus: 1,
        messageCount: messageCountMap[item.groupId]
      }
    })
    return formattedData
  }, [supervisedList, messageCountMap])
  const formatFinishedList = useMemo(() => {
    const formatData = finishedChatList.map((item) => {
      return {
        ...item,
        name: item.name || item.fromUserId || item.groupId,
        onlineStatus: 3,
        messageTime: formatUTC(item.messageTime),
        isFinished: true
      }
    })
    return formatData
  }, [finishedChatList])
  //请求已完成聊天数据
  const fetchFinishedChatList = async () => {
    try {
      if (!customerInfo.userId) return
      const data: any = await getFinishedChatList({
        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        customerServiceIdStr: `${customerInfo.userId}`
      })
      setFinishedChatList(data.records)
      setTodayChatData(data.extraData)
    } catch (error) {
      console.log(error)
    }
  }
  // 24小时聊天数据
  // const fetchTodayChatData = async () => {
  //   try {
  //     if (!customerInfo.userId) return
  //     const data: any = await getTodayChatData({
  //       userId: `${customerInfo.userId}`,
  //       timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
  //     })
  //     setTodayChatData(data)
  //   } catch (error) {
  //     console.log(error)
  //   }
  // }
  // 请求监督数据
  const fetchSupervisedOrChatting = () => {
    sendMsg({ isChatting: true }, { messageType: 55 })
  }
  const diffListData = (prevListData: ItemType[], nextListData: ItemType[]) => {
    console.log(prevListData, nextListData)
    if (prevListData.length !== 0) {
      //比较prev和msg.extraData的中相同groupId的数据，覆盖prev中的数据
      for (let i = 0; i < prevListData.length; i++) {
        const item = prevListData[i]
        if (
          nextListData.some((nextItem) => nextItem.groupId === item.groupId)
        ) {
          return nextListData
        } else {
          return prevListData
        }
      }
    }

    // initMessageCount(nextListData)
    return nextListData
  }
  // 初始拉取的客户列表
  const initListData = (msg: MessageItemProps) => {
    if (msg.extraData.length > 0) {
      const chatList = msg.extraData.filter((item: any) =>
        [1, 3].includes(item.customerServiceType)
      )
      const supervisedList = msg.extraData.filter(
        (item: any) => item.customerServiceType === 2
      )
      setListData((prev) => diffListData(prev, chatList))
      setSupervisedList((prev) => diffListData(prev, supervisedList))
    }
    initMessageCount(msg.extraData)
  }
  // 初始化消息数量
  const initMessageCount = (list: ItemType[]) => {
    Object.keys(messageCountMap).forEach((key) => {
      if (!list.some((item) => item.groupId === key)) {
        console.log('------reset', key)
        //解决react渲染报错，延迟setState
        setTimeout(() => {
          resetMessageCountMap(key)
        }, 0)
      }
    })
  }
  //初始化即时消息
  const initInstantMsg = (msg: MessageItemProps) => {
    updateList(msg)
  }
  /**
   * 26 五分钟不回复转移，关闭窗口
   * 30 会话超时关闭窗口
   * 37 关闭聊天
   * 999 客服主动关闭聊天
   * 60 监督者加入退出群组状态响应
   * 61 聊天转移响应（包括主动转移，被接管）
   */
  const closeChat = async (msg: MessageItemProps) => {
    //chat聊天关闭
    if ([30, 37, 999].includes(msg.messageType)) {
      removeListItem(msg)
      setChatInfo({ groupId: '0' })
    } else if (msg.messageType === 60 || msg.messageType === 61) {
      if ([0, 30021, 30022].includes(msg.code)) {
        removeListItem(msg)
        setChatInfo({ groupId: '0' })
        if (msg.code === 30021) {
          jumpToChatting(msg)
        }
        //判断是否是监督者接管，直接跳转到聊天页面
        if (msg.isTakeOver) {
          message.success('success!', 1, () => {
            jumpToChatting(msg)
          })
        }
      } else {
        message.error(msg.msg)
      }
    }
  }
  const jumpToChatting = (msg: MessageItemProps) => {
    setMode('chatting')
    setGlobalChatMode('chatting')
    setTimeout(() => {
      setChatInfo({ groupId: msg.groupId })
    }, 80)
  }
  const transferChat = (item: ItemType) => {
    console.log('-----transfer---', item)
    setFinishedChatList((prevListData) => {
      const updatedListData = [...prevListData]
      updatedListData.unshift(item)
      return updatedListData
    })
  }
  // 列表上清除
  const removeListItem = (msg: MessageItemProps) => {
    const { groupId, messageType } = msg
    let id = '0'
    const isListDataItem = listData.some((item) => item.groupId === groupId)
    const isSupervisedItem = supervisedList.some(
      (item) => item.groupId === groupId
    )
    if (isListDataItem) {
      setListData((prevListData) => {
        // 从列表中移除
        const index = prevListData.findIndex((item) => item.groupId === groupId)
        if (index === -1) return prevListData
        const updatedListData = [...prevListData]
        // 转移到已完成列表
        if ([30, 37, 999].includes(messageType)) {
          transferChat(updatedListData[index])
          // fetchFinishedChatList()
        }
        id = updatedListData[index]?.groupId
        resetMessageCountMap(id)
        console.log('----removeChatList----:', id)
        updatedListData.splice(index, 1)
        return updatedListData
      })
      return
    }
    if (isSupervisedItem) {
      setSupervisedList((prevListData) => {
        // 从列表中移除
        const index = prevListData.findIndex((item) => item.groupId === groupId)
        const updatedListData = [...prevListData]
        id = updatedListData[index]?.groupId
        resetMessageCountMap(id)
        console.log('----removeSupervise----:', id)
        updatedListData.splice(index, 1)
        return updatedListData
      })
      return
    }
  }
  const asyncSetSupervisedList = async (msg: MessageItemProps) => {
    return new Promise((resolve, reject) => {
      setSupervisedList((prevListData) => {
        console.log('updateList----supervisedList', prevListData)
        const { groupId } = msg
        if (prevListData.some((item) => item.groupId === groupId)) {
          const index = prevListData.findIndex(
            (item) => item.groupId === groupId
          )
          const updatedListData = [...prevListData]
          updatedListData[index] = {
            ...updatedListData[index],
            messageTime: msg.messageTime,
            msg: modifyMsg(msg)
          }
          reject('updateSupervisedList')
          return updatedListData
        } else {
          resolve('next')
          return prevListData
        }
      })
    })
  }
  const asyncSetFinishedChatList = async (msg: MessageItemProps) => {
    return new Promise((resolve, reject) => {
      setFinishedChatList((prevListData) => {
        console.log('updateList----finishedChatList', prevListData)
        const { groupId } = msg
        if (prevListData.some((item) => item.groupId === groupId)) {
          const index = prevListData.findIndex(
            (item) => item.groupId === groupId
          )
          const updatedListData = [...prevListData]
          updatedListData[index] = {
            ...updatedListData[index],
            messageTime: msg.messageTime,
            msg: modifyMsg(msg)
          }
          reject('updateFinishedChatList')
          return updatedListData
        } else {
          resolve('next')
          return prevListData
        }
      })
    })
  }
  // 根据接受的msg更新列表，通过对比groupId属性判断列表是新增还是更新
  const updateList = async (msg: ItemType) => {
    try {
      const flag = await asyncSetSupervisedList(msg)
      if (flag === 'updateSupervisedList') return
      const flag2 = await asyncSetFinishedChatList(msg)
      if (flag2 === 'updateFinishedChatList') return
      setListData((prevListData) => {
        console.log('updateList----ChatList', prevListData)
        const { groupId } = msg
        const index = prevListData.findIndex((item) => item.groupId === groupId)
        if (index === -1) {
          return [...prevListData, msg]
        } else {
          const updatedListData = [...prevListData]
          updatedListData[index] = {
            ...updatedListData[index],
            messageTime: msg.messageTime,
            msg: modifyMsg(msg)
          }
          return updatedListData
        }
      })
    } catch (e) {
      console.log(e)
    }
  }

  return (
    <div className={styles.contact_list_wrapper}>
      {/* 头部导航切换 */}
      <div className={styles.contact_list_header}>
        <Radio.Group
          onChange={handleModeChange}
          buttonStyle="solid"
          value={mode}
        >
          <Radio.Button value="chatting">
            <Badge count={chatMessageBadge} size="small">
              <span style={{ color: mode === 'chatting' ? '#fff' : '' }}>
                My Chats
              </span>
            </Badge>
          </Radio.Button>
          <Radio.Button value="supervised">
            <Badge count={supervisedMessageBadge} size="small">
              <span style={{ color: mode === 'supervised' ? '#fff' : '' }}>
                Supervised
              </span>
            </Badge>
          </Radio.Button>
        </Radio.Group>
      </div>
      {/* <Carousel>
        <Chatting formatListData={formatListData}></Chatting>
        <Supervised supervisedList={supervisedList}></Supervised>
      </Carousel> */}
      {mode === 'chatting' ? (
        <Chatting
          formatListData={formatListData}
          finishedListData={formatFinishedList}
          todayChatData={todayChatData}
        ></Chatting>
      ) : (
        <Supervised supervisedList={formatSupervisedList}></Supervised>
      )}
    </div>
  )
}
