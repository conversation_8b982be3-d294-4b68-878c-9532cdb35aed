import { Input, Tooltip } from 'antd'
import { memo, useMemo, useRef, useState } from 'react'
import { SearchOutlined } from '@ant-design/icons'

import ListItem from '../ListItem'
import styles from '../../../style/contact.module.scss'
import Collapse from '@/component/Collapse'
import { useNavigate } from 'react-router-dom'
import { debounce } from 'lodash'
import Empty from '@/component/Empty'

type ChattingListProps = {
  formatListData: MessageItemProps[]
  finishedListData: MessageItemProps[]
  todayChatData: TodayDataType
}
const suffix = (
  <SearchOutlined
    style={{
      fontSize: 16,
      color: '#D8D8D8'
    }}
  />
)
const tipContent =
  'Only the sessions that ended within 24 hours are displayed. For more sessions, go to Archives.'
function Chatting(props: ChattingListProps) {
  const { formatListData, finishedListData, todayChatData } = props
  const navigate = useNavigate()
  const [isCollapse, setIsCollapse] = useState<boolean>(true)
  const [isStatistics, setIsStatistics] = useState<boolean>(true)
  const [isSearchView, setIsSearchView] = useState<boolean>(false) //  是否进入搜索视图
  const [keyword, setKeyword] = useState<string>('')
  const toggleStatistics = () => {
    setIsStatistics(!isStatistics)
  }
  const toggleCollapse = () => {
    setIsCollapse(!isCollapse)
  }
  const goArchives = (type) => {
    navigate(`/archives?type=${type}`)
  }
  const searchList = useMemo(() => {
    const list = {
      chatList: [],
      finishList: []
    }
    list.chatList = formatListData.filter((item) => item.name.includes(keyword))
    list.finishList = finishedListData.filter((item) =>
      item.name.includes(keyword)
    )
    return list
  }, [keyword])
  const inputSearch = debounce((e: React.ChangeEvent<HTMLInputElement>) => {
    setIsSearchView(e.target.value ? true : false)
    setKeyword(e.target.value)
  }, 500)
  return (
    <>
      {/* 根据导航切换不同组件显示 */}
      <div className={styles.contact_list_content}>
        {/* 搜索 */}
        <div className={styles.contact_list_search}>
          <Input placeholder="Search" suffix={suffix} onChange={inputSearch} />
        </div>
        {/* 联系人列表 */}
        {isSearchView &&
        searchList.chatList.length === 0 &&
        searchList.finishList.length === 0 ? (
          <div className={styles.empty_list}>
            <Empty></Empty>
            <p className={styles.desc}>There is nothing~</p>
          </div>
        ) : (
          <>
            <div className={styles.chat_list_box}>
              <div className={styles.chat_group_title}>Chatting</div>
              <div className={styles.list_box_content}>
                {isSearchView
                  ? searchList.chatList.map((item, index) => {
                      return <ListItem itemData={item} key={index} />
                    })
                  : formatListData.map((item, index) => {
                      return <ListItem itemData={item} key={index} />
                    })}
              </div>
            </div>
            <div className={styles.supervised_list_box}>
              <div className={styles.chat_group_module}>
                <div className={styles.chat_group_title}>
                  <span>Finished</span>
                  <Tooltip title={tipContent}>
                    <i className="iconfont icon-xiaoxitishi"></i>
                  </Tooltip>
                </div>
                <div className={styles.Collapse_up} onClick={toggleCollapse}>
                  <i
                    className={[
                      'iconfont',
                      isCollapse ? 'icon-shang' : 'icon-xia'
                    ].join(' ')}
                  ></i>
                </div>
              </div>
              <div className={styles.finish_list_wrap}>
                <Collapse open={isCollapse}>
                  <div className={styles.list_box_content}>
                    {isSearchView
                      ? searchList.finishList.map((item, index) => {
                          return <ListItem itemData={item} key={index} />
                        })
                      : finishedListData.map((item, index) => {
                          return <ListItem itemData={item} key={index} />
                        })}
                  </div>
                </Collapse>
              </div>
            </div>
          </>
        )}
      </div>
      {/* 消息统计面板 */}
      {!isSearchView ? (
        <div className={styles.contact_list_statistics}>
          <div className={styles.statistics_title_box}>
            <span>Today's data</span>
            <div className={styles.Collapse_up} onClick={toggleStatistics}>
              <i
                className={[
                  'iconfont',
                  isStatistics ? 'icon-shang' : 'icon-xia'
                ].join(' ')}
              ></i>
            </div>
          </div>
          <Collapse open={isStatistics}>
            <div className={styles.statistics_content}>
              {Object.keys(todayChatData).map((key, index) => {
                return (
                  <div
                    className={styles.statistics_item}
                    key={index}
                    onClick={() => goArchives(key)}
                  >
                    <div className={styles.statistics_item_title}>
                      {todayChatData[key as keyof TodayDataType].value}
                    </div>
                    <div className={styles.statistics_item_content}>
                      {todayChatData[key as keyof TodayDataType].key}
                    </div>
                  </div>
                )
              })}
            </div>
          </Collapse>
        </div>
      ) : null}
    </>
  )
}

export default memo(Chatting)
