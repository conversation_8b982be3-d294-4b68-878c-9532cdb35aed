import { memo, useMemo, useRef, useState } from 'react'
import { Input } from 'antd'
import { SearchOutlined } from '@ant-design/icons'

import styles from '../../../style/contact.module.scss'
import ListItem from '../ListItem'
import { debounce } from 'lodash'
import Empty from '@/component/Empty'

const suffix = (
  <SearchOutlined
    style={{
      fontSize: 16,
      color: '#D8D8D8'
    }}
  />
)

type SupervisedListProps = {
  supervisedList: MessageItemProps[]
}
function Supervised(props: SupervisedListProps) {
  const { supervisedList } = props
  const [isSearchView, setIsSearchView] = useState<boolean>(false) //  是否进入搜索视图
  const [keyword, setKeyword] = useState<string>('')

  const searchList = useMemo(() => {
    return supervisedList.filter((item) => {
      return item.name.includes(keyword)
    })
  }, [keyword])
  const inputSearch = debounce((e: React.ChangeEvent<HTMLInputElement>) => {
    setIsSearchView(e.target.value ? true : false)
    setKeyword(e.target.value)
  }, 500)
  return (
    <>
      <div className={styles.contact_list_content}>
        {/* 搜索 */}
        <div className={styles.contact_list_search}>
          <Input
            placeholder="Search"
            suffix={suffix}
            onChange={inputSearch}
            allowClear
          />
        </div>
        {/* 联系人列表 */}
        {!isSearchView ? (
          <>
            <div className={styles.chat_list_box}>
              {/* <div className={styles.chat_group_title}>Chatting</div> */}
              <div className={styles.list_box_content}>
                {supervisedList.map((item, index) => {
                  return <ListItem itemData={item} key={index} />
                })}
              </div>
            </div>
          </>
        ) : searchList.length > 0 ? (
          <div className={styles.contact_list_search_view}>
            <div className={styles.list_box_content}>
              {searchList.map((item, index) => {
                return <ListItem itemData={item} key={index} />
              })}
            </div>
          </div>
        ) : (
          <div className={styles.search_empty}>
            <Empty />
            <p className={styles.desc}>There is nothing~</p>
          </div>
        )}
      </div>
    </>
  )
}

export default memo(Supervised)
