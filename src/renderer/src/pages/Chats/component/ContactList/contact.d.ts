declare interface ItemType {
  avatar?: string
  extraData?: []
  fromUserId?: string
  groupId?: string
  messageId?: string
  messageReadStatus?: boolean
  messageTime?: string
  messageType?: number
  messageVisibilityType?: number
  msg?: string
  name?: string
  sequenceId?: string
  traceId?: string
  onlineStatus?: OnlineStatusType
  messageCount?: number
  customerServiceName?: string
  token?: string
  isFinished?: boolean
  status?: number
  colour?: string
}
declare interface TodayDataType {
  accepted?: Accepted
  bad?: Bad
  good?: Good
}

declare interface Accepted {
  key: string
  value: number
}

declare interface Bad {
  key: string
  value: number
}

declare interface Good {
  key: string
  value: number
}
enum OnlineStatusType {
  online = 1,
  busy = 2,
  offline = 3
}
