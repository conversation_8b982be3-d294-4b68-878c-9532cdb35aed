import React from 'react'
import styles from '../index.module.scss'
import DragIcon from '@/assets/image/drag_icon.svg'
type DragBoxProps = {
  style?: React.CSSProperties
}
function DragBox(props: DragBoxProps) {
  const { style } = props
  return (
    <div className={styles.drag_box} style={style}>
      <div className={styles.drag_box_inner}>
        <div className={styles.drag_box_icon}>
          <img src={DragIcon} alt="" className={styles.icon} />
        </div>
        <div className={styles.drag_box_text}>
          Drag and drop here to send to customer.
        </div>
      </div>
    </div>
  )
}

export default DragBox
