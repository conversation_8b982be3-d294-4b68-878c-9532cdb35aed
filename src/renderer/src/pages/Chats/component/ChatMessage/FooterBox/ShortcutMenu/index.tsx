import { useRef } from 'react'
import styles from './index.module.scss'
import { Spin } from 'antd'
import { useClickAway } from 'ahooks'

type ShortcutMenuProps = {
  onShortcutClick: (data: ShortcutItem) => void
  promptList: ShortcutItem[]
  loading: boolean
}
function ShortcutMenu(props: ShortcutMenuProps) {
  const { onShortcutClick, promptList, loading } = props
  const shortcutRef = useRef(null)

  const emitShortcutClick = (data: ShortcutItem) => {
    onShortcutClick(data)
  }
  /**
   * @description: 点击外部关闭快捷回复
   */
  useClickAway(() => {
    onShortcutClick({} as ShortcutItem)
  }, [shortcutRef, () => document.querySelector('.shortcut_btn')])
  return (
    <div className={styles.shortcut_wrap} ref={shortcutRef}>
      {loading ? (
        <div className={styles.empty_wrap}>
          <Spin></Spin>
        </div>
      ) : (
        <div className={styles.shortcut}>
          {promptList.map((item) => (
            <div
              className={styles.shortcut_item}
              key={item.id}
              onClick={() => emitShortcutClick(item)}
            >
              <div className={styles.item_group}>{item.groupName}</div>
              <div className={styles.item_title}>{item.prompt}</div>
              <div className={styles.item_content}>{item.content}</div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default ShortcutMenu
