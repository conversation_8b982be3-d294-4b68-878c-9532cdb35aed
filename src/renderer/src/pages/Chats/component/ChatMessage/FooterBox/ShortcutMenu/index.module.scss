.shortcut_wrap {
  width: 100%;
  height: 347px;
  overflow: auto;
  position: absolute;
  left: 0;
  top: -350px;
  border-radius: 4px;
  background-color: #fff;
  .empty_wrap {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .shortcut {
    cursor: pointer;
    @include font12;
    color: #909199;
    padding-top: 8px;
    .shortcut_item {
      padding: 5px 12px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #fafafa;
      .item_group {
        width: 15%;
        word-wrap: break-word;
      }
      .item_title {
        width: 25%;
        word-wrap: break-word;
      }
      .item_content {
        width: 60%;
        word-wrap: break-word;
      }
      &:hover {
        background: #fafafa;
      }
    }
  }
}
