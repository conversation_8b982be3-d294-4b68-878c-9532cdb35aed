import FsAvatarProps from '@/component/FsAvatar'
import styles from './index.module.scss'
import type { MenuProps } from 'antd'
import { Dropdown, Select, Tooltip, message, Modal } from 'antd'
import React, { useMemo } from 'react'
import useChatInfo from '@/hooks/useChatInfo'
import useSendMSg from '@/hooks/useSendMsg'
import { useDispatch, useSelector } from 'react-redux'
import { RootState } from '@renderer/store'
import { setChatMessage } from '@renderer/api/common'
import { getCurrentTimeZone } from '@renderer/utils/util'
import eventBus from '@renderer/utils/eventBus'
// import { useSelector } from 'react-redux'
// import { RootState } from '@/store'

export type CustomerListOption = {
  userId: number
  name: string
}

type HeaderProps = {
  quitChatRoom?: () => void
  customerList?: CustomerListOption[]
  onDropMenuClick?: (open: boolean) => void
}
export default React.memo(function HeaderBox(props: HeaderProps) {
  const dispatch = useDispatch()
  const { sendMsg } = useSendMSg()
  const { customerList, onDropMenuClick, quitChatRoom } = props
  const { chatRoomInfo, chatDetail, chatMode } = useChatInfo()
  const customerInfo = useSelector(
    (state: RootState) => state.login.customerInfo
  )
  const chatStatus = useSelector((state: RootState) => state.chat.isFinished)
  const options = useMemo(() => {
    return customerList?.map((item) => {
      return { label: item.name, value: item.userId }
    })
  }, [customerList])

  const userName = useMemo(() => {
    return chatDetail.name || chatDetail.userId || chatDetail.groupId
  }, [chatDetail])
  const chattingOption: MenuProps['items'] = [
    // {
    //   key: '1',
    //   label: (
    //     <a
    //       target="_blank"
    //       rel="noopener noreferrer"
    //       href="https://www.antgroup.com"
    //     >
    //       Add Case
    //     </a>
    //   )
    // },
    {
      key: '2',
      label: (
        <a target="_blank" rel="noopener noreferrer" onClick={sendChatRecord}>
          Email Transcrip To Customer
        </a>
      )
    }
  ]

  const supervisedOption: MenuProps['items'] = [
    {
      key: '1',
      label: (
        <a target="_blank" rel="noopener noreferrer" onClick={takeOverChat}>
          To take over the chat
        </a>
      )
    },
    {
      key: '2',
      label: (
        <a target="_blank" rel="noopener noreferrer" onClick={helpCloseChat}>
          Help Close The Chat
        </a>
      )
    }
  ]

  //发送聊天记录到邮箱
  async function sendChatRecord() {
    if (!chatDetail.email) {
      message.error('The email address is empty')
      return
    }
    try {
      await setChatMessage({
        cc: customerInfo.email,
        email: chatDetail.email,
        groupIdStr: chatDetail.groupIdStr,
        languagesId: 1,
        timeZone: getCurrentTimeZone()
      })
      message.success('Send successfully')
    } catch (error) {
      console.log(error)
      message.error('Send failed')
    }
  }
  const handleChange = (value: number) => {
    sendMsg(
      {
        customerServiceId: `${value}`,
        groupId: chatRoomInfo.groupId,
        isTakeOver: false
      },
      { messageType: 25 }
    )
  }
  // chatting页面退出聊天
  const quitChat = () => {
    Modal.confirm({
      content: 'Are you sure you want to stop the chat?',
      onOk: () => {
        eventBus.emit('receiveMsg', {
          messageType: 999,
          groupId: chatRoomInfo.groupId
        })
        sendMsg(
          {
            groupId: chatRoomInfo.groupId,
            isForceClose: false,
            isSupervisionClose: chatMode === 'supervised'
          },
          { messageType: 36 }
        )
      }
    })
  }
  // 监控页面退出监控
  const exitSupervision = () => {
    Modal.confirm({
      content: 'Do you want to end supervision?',
      onOk: () => {
        sendMsg(
          { groupId: chatRoomInfo.groupId, customerServiceType: 2, action: 2 },
          { messageType: 59 }
        )
      }
    })
  }
  //接管聊天
  function takeOverChat() {
    sendMsg(
      {
        customerServiceId: `${customerInfo.userId}`,
        groupId: chatRoomInfo.groupId,
        isTakeOver: true
      },
      { messageType: 25 }
    )
  }
  function helpCloseChat() {
    quitChat()
  }
  const onDropdownVisibleChange = (open: boolean) => {
    onDropMenuClick(open)
  }
  return (
    <div className={styles.chat_header_wrapper}>
      <div className={styles.chat_header_left}>
        <FsAvatarProps
          name={`${userName}`}
          onlineStatus={chatStatus ? 3 : 1}
          backgroundColor={chatDetail.colour}
        ></FsAvatarProps>
        <span>{userName}</span>
      </div>
      <div className={styles.chat_header_right}>
        {chatMode === 'chatting' && !chatStatus && (
          <div className={styles.chat_right_left}>
            <span className={styles.left_title}>Transter to</span>
            <Select
              size={'small'}
              value={0}
              onChange={handleChange}
              style={{ width: 124, height: 24, fontSize: 12 }}
              options={options}
              onDropdownVisibleChange={onDropdownVisibleChange}
            />
          </div>
        )}
        <Dropdown
          menu={{
            items: chatMode === 'chatting' ? chattingOption : supervisedOption
          }}
          placement="bottomLeft"
        >
          <i className={`${styles.icon_btn} iconfont icon-hanbaocaidan`}></i>
        </Dropdown>
        {chatMode === 'chatting' ? (
          !chatStatus && (
            <i
              className={`${styles.icon_btn} iconfont icon-guanbi`}
              onClick={quitChat}
            ></i>
          )
        ) : (
          <Tooltip title="Exit Supervision">
            <i
              className={`${styles.icon_btn} iconfont icon-guanbi`}
              onClick={exitSupervision}
            ></i>
          </Tooltip>
        )}
      </div>
    </div>
  )
})
