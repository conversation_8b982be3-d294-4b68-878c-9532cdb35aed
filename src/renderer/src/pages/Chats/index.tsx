import { useEffect } from 'react'
import ChatInfo from './component/ChatInfo'
import ChatMessage from './component/ChatMessage'
import ContactList from './component/ContactList'
import styles from './style/index.module.scss'
import { useDispatch } from 'react-redux'
import {
  setChatRoomInfo,
  setChatStatus,
  setGlobalChatMode
} from '@/store/modules/chat'

export default function Chats() {
  const dispatch = useDispatch()
  useEffect(() => {
    return () => {
      dispatch(setGlobalChatMode(''))
      dispatch(setChatRoomInfo({ groupId: '0' }))
      dispatch(setChatStatus(false))
    }
  }, [])
  return (
    <div className={styles.chats_wrapper}>
      {/* 联系人列表 */}
      <ContactList />
      {/* 聊天对话界面 */}
      <ChatMessage />
      {/* 用户信息面板 */}
      <ChatInfo />
    </div>
  )
}
