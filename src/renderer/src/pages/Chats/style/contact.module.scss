.contact_list_wrapper {
  width: 300px;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  .contact_list_header {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 82px;
    border-bottom: 1px solid #e4e7ed;
  }
  .contact_list_search_view {
    width: 100%;
    padding: 0 16px;
  }
  .contact_list_content {
    flex: 1;
    width: 100%;
    padding: 0 16px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .empty_list {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
    .contact_list_search {
      margin-top: 16px;
    }
    .search_empty {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      .desc {
        @include font12;
        color: #8c8c8c;
        margin-top: 6px;
      }
    }
    .chat_list_box {
      max-height: 382px;
      margin-top: 22px;
      display: flex;
      flex-direction: column;
      .chat_group_title {
        margin-bottom: 8px;
        font-size: 16px;
        color: #3d3d3d;
      }
      .list_box_content {
        flex: 1;
        overflow-y: auto;
        &:hover {
          &::-webkit-scrollbar-thumb {
            background: #b3b1b1;
            border-radius: 4px;
          }
        }
        //修改滚动条样式
        &::-webkit-scrollbar {
          width: 4px;
          height: 4px;
        }
        &::-webkit-scrollbar-thumb {
          background: #fff;
          border-radius: 4px;
        }
        &::-webkit-scrollbar-track {
          background: #fff;
          border-radius: 4px;
        }
      }
    }
    .supervised_list_box {
      margin-top: 22px;
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      .finish_list_wrap {
        overflow: auto;
      }
      .chat_group_module {
        display: flex;
        justify-content: space-between;
        .Collapse_up {
          cursor: pointer;
          i {
            font-size: 16px;
            color: #646569;
            transition: all 1s;
          }
        }
        .chat_group_title {
          margin-bottom: 8px;
          font-size: 16px;
          color: #3d3d3d;
          display: flex;
          align-items: center;
          i {
            margin-left: 8px;
            font-size: 16px;
            color: #d8dbe2;
            &:hover {
              color: #909399;
              cursor: pointer;
            }
          }
        }
      }
      .list_close {
        display: none;
      }
      .list_box_content {
        flex: 1;
        overflow-y: auto;
        &:hover {
          &::-webkit-scrollbar-thumb {
            background: #b3b1b1;
            border-radius: 4px;
          }
        }
        //修改滚动条样式
        &::-webkit-scrollbar {
          width: 4px;
          height: 4px;
        }
        &::-webkit-scrollbar-thumb {
          background: #fff;
          border-radius: 4px;
        }
        &::-webkit-scrollbar-track {
          background: #fff;
          border-radius: 4px;
        }
      }
    }
  }
  .contact_list_statistics {
    width: 100%;
    max-height: 130px;
    min-height: 52px;
    padding: 16px;
    box-sizing: border-box;
    background-color: #fbfbfc;
    transition: all 1s;
    .statistics_title_box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 10px;
      color: #3d3d3d;
      .Collapse_up {
        cursor: pointer;
        i {
          font-size: 16px;
          color: #646569;
          transition: all 1s;
        }
      }
    }
    .statistics_content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      transition: all 1s;
      .statistics_item {
        margin-top: 16px;
        width: 84px;
        height: 62px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-left: 8px;
        &:hover {
          cursor: pointer;
          background: #f4f7ff;
          border-radius: 4px;
        }
        .statistics_item_title {
          font-size: 24px;
          color: #3d3d3d;
          line-height: 30px;
        }
        .statistics_item_content {
          font-size: 10px;
          color: #3d3d3d;
          line-height: 16px;
        }
      }
    }
    .statistics_close {
      display: none;
    }
  }
}
