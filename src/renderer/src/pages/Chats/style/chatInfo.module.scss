.chat_info_wrapper {
  @media screen and (max-width: 1680px) {
    width: 370px;
  }
  @media screen and (min-width: 1680px) and (max-width: 1920px) {
    width: 430px;
  }
  width: 430px;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  $chat_info_wrapper_bg: #4e80ff;
  .chat_info_tab {
    height: 82px;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: center;
    align-items: end;
    .chat_info_tab_item:not(:last-child) {
      margin-right: 95px;
    }
    .chat_info_tab_item {
      padding-bottom: 14px;
      box-sizing: border-box;
      cursor: pointer;
      transition: all 0.3s;
      .chat_info_tab_item_icon {
        i {
          font-size: 34px;
          color: #aaaaaa;
        }
      }
    }
    .chat_info_tab_active {
      position: relative;
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #4e80ff;
        background-color: $chat_info_wrapper_bg;
      }
      .chat_info_tab_item_icon {
        // border-bottom: 3px solid $chat_info_wrapper_bg;
        i {
          color: $chat_info_wrapper_bg;
        }
      }
    }
  }
  .empty_wrap {
    flex: 1;
    display: flex;
    justify-content: center;
  }
}
