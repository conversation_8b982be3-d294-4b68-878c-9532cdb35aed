import FsTableSelf from '@renderer/component/FsTableSelf'
import { ColumnProps } from 'antd/lib/table'

type FsTableProps<T> = {
  className?: string
  data: T[]
  columns: ColumnProps<T>[]
  loading?: boolean
}

const FsTable = <T extends Record<string, any>>({
  className,
  data,
  columns,
  loading
}: FsTableProps<T>): JSX.Element => {
  return (
    <>
      <FsTableSelf
        dataSource={data}
        columns={columns}
        pagination={false}
        rowKey={(record) => record.id}
        className={className}
        loading={loading}
      />
    </>
  )
}

export default FsTable
