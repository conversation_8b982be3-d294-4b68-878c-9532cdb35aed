import { Input, Modal, Pagination, PaginationProps, Select, Space } from 'antd'
import React, { useEffect, useMemo, useState } from 'react'
import FsTable from '../components/FsTable'
import style from '../style/blacklist.module.scss'
import {
  getBlacklistRequest,
  deleteBlacklistById,
  getBlacklistCustomerList
} from '@/api/blacklist/index'
import { GlobalContext } from '@/context/globalContext'
type CustomerType = {
  id?: number
  name?: string
}
type ListType = {
  value?: number | string
  label?: string
}
const SearchIcon = (
  <i className="iconfont icon-searchsousuo" style={{ color: '#D8D8D8' }}></i>
)

interface ParamsData {
  operatorId?: number
  search: string
}

type DataType = {
  ipAddress: string
  customerName: string
  id: number
  customerEmail: string
  createdAt: string
  expireTimeDesc: number
  customerServiceName: string
}

function BlacklistSetting() {
  const { messageApi } = React.useContext(GlobalContext)
  const [paramsValue, setParamsValue] = useState<ParamsData>({
    operatorId: 0,
    search: ''
  })

  const [isModalOpen, setIsModalOpen] = useState(false)

  const [tableLoading, setTableLoading] = useState(false)
  const [customerList, setCustomerList] = useState<ListType[]>([])
  const [tableData, setTableData] = useState<DataType[]>([])

  const [totalNum, setTotalNum] = useState(10)
  const blacklistId = React.useRef<number>(0)

  const [paginationParams, setPaginationParams] = useState<{
    current: number
    size: number
  }>({ current: 1, size: 10 })
  const columns = useMemo(
    () => [
      {
        title: 'IP Address',
        dataIndex: 'ipAddress',
        key: 'ipAddress'
      },
      {
        title: 'Name',
        dataIndex: 'name',
        key: 'name'
      },
      {
        title: 'Email',
        dataIndex: 'email',
        key: 'email'
      },
      {
        title: 'Handler',
        dataIndex: 'operatorName',
        key: 'operatorName'
      },
      {
        title: 'Accession Date',
        dataIndex: 'createdAt',
        key: 'createdAt'
      },
      {
        title: 'Disengage Countdown',
        dataIndex: 'expireTimeDesc',
        key: 'expireTimeDesc'
      }
    ],
    []
  )
  useEffect(() => {
    getBlacklist()
  }, [paginationParams, paramsValue])
  useEffect(() => {
    fetchBlacklistHandler()
  }, [])

  //获取黑名单列表
  const getBlacklist = async () => {
    try {
      const params: BlacklistParamsType = {
        current: paginationParams.current,
        size: paginationParams.size,
        search: paramsValue.search,
        operatorId: paramsValue.operatorId
      }
      setTableLoading(true)
      const data: any = await getBlacklistRequest(params)
      if (data.records.length > 0) {
        setTotalNum(data.total)
      }
      setTableData(data.records)
      setTableLoading(false)
    } catch (error: any) {
      throw new Error(error)
    } finally {
      setTableLoading(false)
    }
  }
  const handleChange = (value: number) => {
    setParamsValue((pre) => ({ ...pre, operatorId: value }))
  }
  // 分页
  const onPaginationChange: PaginationProps['onChange'] = (page, pageSize) => {
    setPaginationParams((pre) => ({ ...pre, current: page, size: pageSize }))
  }
  //删除弹框
  const handlerRemove = (id: number) => {
    blacklistId.current = id
    setIsModalOpen(true)
  }
  const delBlacklist = async () => {
    try {
      await deleteBlacklistById({ blacklistId: blacklistId.current })
      console.log('delete success')
      messageApi.open({
        type: 'success',
        content: 'delete success'
      })
      getBlacklist()
    } catch (error: any) {
      messageApi.open({
        type: 'error',
        content: 'delete fail'
      })
      throw new Error(error)
    } finally {
      setIsModalOpen(false)
    }
  }
  //获取所有添加黑名单的客服列表
  const fetchBlacklistHandler = async () => {
    try {
      const data: any = await getBlacklistCustomerList()
      console.log(data)
      const { operators = [] } = data || {}
      const filterData = operators.map((item: CustomerType) => {
        return {
          value: item.id,
          label: item.name
        }
      })
      filterData.unshift({ value: 0, label: 'All' })
      setCustomerList(filterData)
    } catch (error: any) {
      console.log(error)
      throw new Error(error)
    }
  }
  const searchKeydown = (e: any) => {
    if (e.keyCode === 13) {
      setParamsValue((pre) => ({ ...pre, search: e.target.value }))
    }
  }
  return (
    <div className={style.blacklist_container}>
      <div style={{ padding: '0 20px' }}>
        <Space wrap>
          <Select
            size="large"
            value={paramsValue.operatorId}
            style={{ width: 120 }}
            onChange={handleChange}
            options={customerList}
          />
          <Input
            suffix={SearchIcon}
            defaultValue={paramsValue.search}
            size="large"
            placeholder="Name/IP Address/Email"
            style={{ width: 380 }}
            onKeyDown={searchKeydown}
          />
        </Space>
      </div>
      <div className={style['blacklist_table']}>
        <FsTable
          data={tableData}
          columns={[
            ...columns,
            {
              title: 'Actions',
              key: 'actions',
              render: (_: string, record: DataType) => {
                return <a onClick={() => handlerRemove(record.id)}>Remove</a>
              }
            }
          ]}
          loading={tableLoading}
        />
      </div>
      <div className={style.pagination}>
        <Pagination
          current={paginationParams.current}
          pageSize={paginationParams.size}
          pageSizeOptions={['5', '10', '20', '50']}
          showQuickJumper
          showSizeChanger
          total={totalNum}
          onChange={onPaginationChange}
          showTotal={(total) => `Total ${total} items`}
        />
      </div>
      <Modal
        title="Remove the blacklist"
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        onOk={delBlacklist}
      >
        <p>
          After the blacklist is removed, the customer service service receives
          messages from this IP address normally.
        </p>
      </Modal>
    </div>
  )
}

export default BlacklistSetting
