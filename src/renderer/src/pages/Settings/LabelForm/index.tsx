import React, { useEffect, useRef, useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>r,
  Divider,
  Form,
  Input,
  Modal,
  Radio,
  Space,
  message
} from 'antd'
import style from '../style/labelForm.module.scss'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import {
  addQuestion,
  getCategoryTree,
  getQuestionDetail,
  updateQuestion
} from '@/api/settings'
import EditableText from './components/EditText'
import { settingStatus } from '@/enum/status'

const LabelForm: React.FC = () => {
  const location = useLocation()
  const { siteId } = location.state
  const search = new URLSearchParams(location.search)
  const questionId = search.get('id')
  const params = useParams()
  const navigate = useNavigate()
  const editIndex = useRef<number>(0)
  const linkForm = useRef<any>(null)
  const [form] = Form.useForm()
  const [treeData, setTreeData] = useState<any[]>([])
  const [formData, setFormData] = useState<FormQuestionParams>({
    answerList: [{ content: '', id: 0 }],
    categoryId: null,
    status: 1,
    questionContent: '',
    questionId: 0,
    siteId,
    userId: 123
  })
  const [modalShow, setModalShow] = useState(false)
  const addAnswer = () => {
    setFormData((prevState) => ({
      ...prevState,
      answerList: [
        ...prevState.answerList,
        {
          content: '',
          id: 0
        }
      ]
    }))
  }
  //从分类树中查找指定id的所有父级的id
  const findParentId = (id: number, tree: any): any => {
    const parentId: any[] = [id]
    const find = (tree: any) => {
      for (let i = 0; i < tree.length; i++) {
        if (tree[i].id === id) {
          parentId.unshift(tree[i].parentId)
          break
        } else {
          if (tree[i].children) {
            find(tree[i].children)
          }
        }
      }
    }
    find(tree)
    return parentId
  }
  const delAnswer = (index: number) => {
    setFormData((prevState) => ({
      ...prevState,
      answerList: prevState.answerList.slice(index)
    }))
  }

  const showModal = (index: number) => {
    editIndex.current = index
    setModalShow(true)
  }
  const linkOk = () => {
    linkForm.current.validateFields().then((values: any) => {
      console.log(values)
      const newAnswerList = formData.answerList.map((item, index) => {
        if (index === editIndex.current) {
          return {
            ...item,
            content: `${item.content} <a href="${values.link}" target="_blank" rel="noreferrer">${values.linkText}</a>`
          }
        } else {
          return item
        }
      })
      setFormData((prevState) => ({
        ...prevState,
        answerList: newAnswerList
      }))
      setFormValue('answerList', newAnswerList)
      linkForm.current.resetFields()
      setModalShow(false)
    })
  }
  const handleCancel = () => {
    setModalShow(false)
  }
  //获取树形数据
  const getTreeData = async (siteId: any) => {
    try {
      const data: any = await getCategoryTree({ siteId })
      console.log(data?.trees)
      setTreeData(data?.trees)
      getDetail(Number(questionId), data?.trees)
    } catch (error) {
      console.log(error)
    }
  }
  //选择label
  const labelChange = (value: any) => {
    console.log(value)
    setFormData((prevState) => ({
      ...prevState,
      categoryId: value
    }))
    setFormValue('categoryId', value)
  }
  const setFormValue = (key: any, value: any) => {
    const prevData = form.getFieldsValue()
    form.setFieldsValue({
      ...prevData,
      [key]: value
    })
    setFormData((prevState) => ({
      ...prevState,
      [key]: value
    }))
  }
  const radioChange = (e: any) => {
    setFormData((prevState) => ({
      ...prevState,
      status: e.target.value
    }))
    setFormValue('status', e.target.value)
  }
  const formSubmit = () => {
    form.validateFields().then((values) => {
      console.log(values)
      if (params.type === 'add') {
        const params: any = { ...formData }
        params.categoryId = params.categoryId[params.categoryId.length - 1]
        addQuestionRequest(params)
      } else {
        const params: any = { ...formData }
        params.categoryId = params.categoryId[params.categoryId.length - 1]
        params.questionId = Number(questionId)
        updateQuestionRequest(params)
      }
    })
  }
  //编辑问题请求
  const updateQuestionRequest = async (params: AddQuestionParams) => {
    try {
      await updateQuestion(params)
      message.success('Edit success')
      navigate(-1)
    } catch (error) {
      console.log(error)
      message.error('Edit failed')
    }
  }
  //添加问题请求
  const addQuestionRequest = async (params: AddQuestionParams) => {
    try {
      await addQuestion(params)
      message.success('Add success')
      navigate(-1)
    } catch (error) {
      console.log(error)
      message.error('Add failed')
    }
  }
  // 获取详情
  const getDetail = async (id: number, treeData) => {
    if (params.type !== 'edit') return
    try {
      const data: any = await getQuestionDetail({ id })
      console.log(data)
      data.categoryId = findParentId(data.categoryId, treeData)
      setFormData(data)
      form.setFieldsValue(data)
      console.log(form.getFieldsValue)
    } catch (error) {
      console.log(error)
    }
  }
  useEffect(() => {
    if (!siteId) return
    getTreeData(siteId)
  }, [siteId])
  // useEffect(() => {
  //   if (params.type === 'edit') {
  //     getDetail(Number(questionId))
  //   }
  // }, [params])

  return (
    <>
      <div className={style.label_form_container}>
        <div className={style.content_box}>
          <h2 className={style.title}>Question Setting</h2>
          <Form
            form={form}
            layout="vertical"
            style={{ maxWidth: 632 }}
            initialValues={formData}
            size="large"
            autoComplete="off"
          >
            <Form.Item
              label="Label"
              name="categoryId"
              rules={[{ required: true, message: 'Please select a Label' }]}
              // initialValue={formData.categoryId}
            >
              <Cascader
                options={treeData}
                onChange={labelChange}
                placeholder="Please select a Label"
                fieldNames={{ label: 'name', value: 'id' }}
              />
            </Form.Item>

            <Form.Item
              label="Question"
              name="questionContent"
              // initialValue={formData.questionContent}
              rules={[{ required: true, message: 'Enter the question' }]}
            >
              <Input
                placeholder="Enter the question"
                value={formData.questionContent}
                onChange={(e) =>
                  setFormValue('questionContent', e.target.value)
                }
              />
            </Form.Item>
            {formData?.answerList.map((item, index) => (
              <Form.Item
                label={
                  <div className={style.label_title}>
                    <div>
                      <span>Answer {index + 1}</span>
                      <i
                        className="icon-lianjie iconfont"
                        style={{ marginLeft: 4 }}
                        onClick={() => showModal(index)}
                      ></i>
                    </div>
                    {index > 0 ? (
                      <i
                        className="icon-guanbi iconfont"
                        onClick={() => delAnswer(index)}
                      ></i>
                    ) : null}
                  </div>
                }
                name={['answerList', index, 'content']}
                rules={[{ required: true, message: 'Enter the answer' }]}
                key={index}
              >
                <EditableText
                  value={item.content}
                  onChange={(value) => {
                    setFormData((prevState) => ({
                      ...prevState,
                      answerList: prevState.answerList.map((item, i) => {
                        if (i === index) {
                          return {
                            ...item,
                            content: value
                          }
                        } else {
                          return item
                        }
                      })
                    }))
                  }}
                />
              </Form.Item>
            ))}

            <div className={style.add_btn} onClick={addAnswer}>
              <i className="icon-tianjia iconfont"></i>
              <span>Add Answer</span>
            </div>
            <Divider />
            <h2 className={style.title}>Status</h2>
            <Form.Item name="status">
              <Radio.Group onChange={radioChange} value={formData.status}>
                {settingStatus.slice(0, 2).map((item) => (
                  <Radio value={item.value} key={item.value}>
                    {item.label}
                  </Radio>
                ))}
              </Radio.Group>
            </Form.Item>
          </Form>
        </div>
      </div>
      <div className={style.foot_btn}>
        <Space>
          <Button onClick={() => navigate(-1)}>Cancel</Button>
          <Button type="primary" onClick={formSubmit}>
            Submit
          </Button>
        </Space>
      </div>
      {/* ---弹框--- */}
      <Modal
        title="Add Link"
        open={modalShow}
        onCancel={handleCancel}
        onOk={linkOk}
      >
        <Form layout="vertical" ref={linkForm}>
          <Form.Item
            label="Link Text"
            name="linkText"
            rules={[{ required: true }]}
          >
            <Input placeholder="Enter text"></Input>
          </Form.Item>
          <Form.Item label="Link" name="link" rules={[{ required: true }]}>
            <Input placeholder="Paster or enter a link"></Input>
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}

export default LabelForm
