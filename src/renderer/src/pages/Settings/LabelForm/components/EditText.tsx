import { Input } from 'antd'
import { FC, memo, useEffect, useMemo, useRef, useState } from 'react'
import './editText.scss'
interface EditableTextProps {
  value: string
  onChange: (value: string) => void
}

const EditableText: FC<EditableTextProps> = ({ value, onChange }) => {
  const inputRef = useRef<any>(null)
  const [isEditing, setIsEditing] = useState(false)
  const initRef = useRef<boolean>(false)

  const handleBlur = () => {
    setIsEditing(false)
  }
  //光标切换到内容最后
  const setEndOfContenteditable = (textArea: any) => {
    textArea.setSelectionRange(textArea.value.length, textArea.value.length)
  }
  useEffect(() => {
    initRef.current = true
    if (isEditing) {
      if (inputRef.current) {
        console.log(inputRef.current)
        const editElement: Node = inputRef.current.resizableTextArea.textArea
        inputRef.current.focus()
        setEndOfContenteditable(editElement)
      }
    }
  }, [isEditing])

  return (
    <div>
      {isEditing ? (
        <Input.TextArea
          ref={inputRef}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onBlur={handleBlur}
          rows={4}
          placeholder="Enter your answer and click on the top right corner to add a link"
          style={{ borderColor: '#40a9ff' }}
        />
      ) : (
        <div className="edit-container">
          <div
            contentEditable
            onClick={() => setIsEditing(true)}
            dangerouslySetInnerHTML={{ __html: value }}
            className="editable-text"
            placeholder="Enter your answer and click on the top right corner to add a link"
          />
          {/* {!value && (
            <span className="editable-placeholder">
              Enter your answer and click on the top right corner to add a link
            </span>
          )} */}
        </div>
      )}
    </div>
  )
}
export default memo(EditableText)
