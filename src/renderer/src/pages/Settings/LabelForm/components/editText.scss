$active-color: #40a9ff;
.edit-container {
  position: relative;
  .editable-text {
    max-width: 100%;
    height: auto;
    min-height: 32px;
    line-height: 1.5714285714285714;
    vertical-align: bottom;
    transition: all 0.3s, height 0s;
    resize: vertical;
    padding: 7px 11px;
    font-size: 16px;
    border-radius: 8px;
    background-color: #ffffff;
    background-image: none;
    border-width: 1px;
    border-style: solid;
    border-color: #d9d9d9;
    min-height: 116px;
    outline: none;
    &:hover {
      border-color: $active-color;
    }
    &:active {
      border-color: $active-color;
    }
    &[contenteditable]:empty:before {
      content: attr(placeholder);
      color: rgba(0, 0, 0, 0.25);
      font-weight: normal;
      line-height: 22px;
      font-size: 16px;
    }
    &[contenteditable]:focus {
      content: none;
    }
  }
}
