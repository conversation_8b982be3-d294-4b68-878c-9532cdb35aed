import { Outlet, useLocation, useNavigate } from 'react-router-dom'
import { Tabs } from 'antd'
import type { TabsProps } from 'antd'
import style from './style/index.module.scss'
import { useEffect, useState } from 'react'

export default function Settings() {
  const navigate = useNavigate()
  const location = useLocation()
  const [tabsPath, setTabsPath] = useState<string>('/settings/labelSetting')
  // 初始化路由判断
  useEffect(() => {
    if (location.pathname === '/settings') {
      navigate('/settings/labelSetting')
    }
    console.log(location.pathname)
    setTabsPath(location.pathname)
  }, [location.pathname])
  const items: TabsProps['items'] = [
    {
      key: '/settings/labelSetting',
      label: 'Label setting'
    },
    {
      key: '/settings/blacklistSetting',
      label: 'Blacklist setting'
    }
  ]
  const onChange = (key: string) => {
    navigate(key)
  }
  const isFormTitle = location.pathname.includes('/settings/labelForm')
  console.log(isFormTitle)
  return (
    <section className={style.settings_wrap}>
      {!isFormTitle ? (
        <Tabs
          activeKey={tabsPath}
          items={items}
          onChange={onChange}
          className={style.tabs_head}
        />
      ) : (
        <div className={style['form_head']}>
          <span>Settings /</span>
          <span>Label Setting</span>
        </div>
      )}

      <Outlet />
    </section>
  )
}
