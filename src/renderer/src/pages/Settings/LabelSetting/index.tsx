import React, { useEffect, useState } from 'react'
import style from '../style/labelSetting.module.scss'
import { Select, Space, Cascader, Input, Button } from 'antd'
import TreeList from './components/TreeList'
import QaTable from './components/QaTable'
import { useNavigate } from 'react-router-dom'
import { getQuestionList, getStationList } from '@/api/settings'
import { settingStatus } from '@/enum/status'
import useTable from '@/hooks/useTable'
import { set } from 'lodash'

interface Option {
  value: string
  label: string
  children?: Option[]
}

interface Params {
  siteId: number
  status: number
  search?: string
}

const SearchIcon = (
  <i className="iconfont icon-searchsousuo" style={{ color: '#D8D8D8' }}></i>
)
function LabelSetting() {
  const navigate = useNavigate()
  const [paramsValue, setParamsValue] = useState<Params>({
    siteId: 2,
    status: 2,
    search: ''
  })
  const handleChange = (value: number) => {
    console.log(value)
    setParamsFunc({
      siteId: value,
      cidList: []
    })
    setParamsValue((prev) => {
      return {
        ...prev,
        siteId: value
      }
    })
  }
  //获取分类组件选中id
  const parentMessage = (value: any) => {
    console.log(value)
    setParamsFunc({ cidList: value })
  }
  const [siteList, setSiteList] = useState([])
  //获取QA列表
  const getQaList = async (params: QuestionListParamsType) => {
    try {
      const data: any = await getQuestionList(params)
      return data
    } catch (error) {
      console.log(error)
      return null
    }
  }
  const {
    tableData,
    pageInfo,
    paramsInfo,
    setParamsFunc,
    setPageFunc,
    total,
    loading
  } = useTable({
    pagination: { current: 1, size: 10 },
    params: {
      siteId: 2,
      status: 2,
      search: '',
      cidList: []
    },
    getTable: getQaList,
    filterName: { data: 'records' }
  })

  //获取站点列表
  const getSiteList = async () => {
    try {
      const data: any = await getStationList()
      const filterSiteList = data?.allSite.map((item: any) => {
        return {
          value: item.id,
          label: item.name
        }
      })
      setSiteList(filterSiteList)
    } catch (error) {
      console.log(error)
    }
  }
  const keyDownSearch = (e: any) => {
    if (e.keyCode === 13) {
      setParamsFunc({ search: e.target.value })
    }
  }
  const searchChange = (e: any) => {
    setParamsValue((prev) => {
      return {
        ...prev,
        search: e.target.value
      }
    })
  }
  useEffect(() => {
    getSiteList()
  }, [])
  return (
    <>
      <div className={style.label_setting_wrap}>
        <Space wrap>
          <Select
            size="large"
            value={paramsInfo.siteId}
            style={{ width: 120 }}
            onChange={handleChange}
            options={siteList}
          />
          {/* <Cascader
            size="large"
            options={options}
            onChange={onChange}
            placeholder="Please select"
          /> */}
          <Select
            size="large"
            value={paramsInfo.status}
            style={{ width: 120 }}
            onChange={(value) => {
              setParamsFunc({
                status: value
              })
            }}
            options={settingStatus}
          />
          <Input
            suffix={SearchIcon}
            size="large"
            value={paramsValue.search}
            onChange={searchChange}
            onKeyDown={keyDownSearch}
          />
        </Space>
        <Button
          type="primary"
          onClick={() =>
            navigate('/settings/labelForm/add', {
              state: { siteId: paramsValue.siteId }
            })
          }
        >
          + Add Account
        </Button>
      </div>
      <div className={style.page_container}>
        <div className={style.page_left_wrap}>
          <TreeList
            siteId={paramsValue.siteId}
            parentMessage={parentMessage}
          ></TreeList>
        </div>
        <div className={style.page_right_wrap}>
          <QaTable
            data={tableData as any}
            pageInfo={pageInfo}
            total={total}
            loading={loading}
            setPageInfo={setPageFunc}
            siteId={paramsValue.siteId}
          ></QaTable>
        </div>
      </div>
    </>
  )
}
export default LabelSetting
