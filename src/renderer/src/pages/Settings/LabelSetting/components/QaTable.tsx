import React from 'react'
import { But<PERSON>, Pagination, Popover, Space, TableProps } from 'antd'
import style from '../../style/labelSetting.module.scss'
import FsTable from '../../components/FsTable'
import { useNavigate } from 'react-router-dom'

interface DataType {
  id: number
  question: string
  answerList: [
    {
      content: string
      id: number
    }
  ]
  label: string
  status: number
  updated_at: string
  operator: string
}
interface QaTableProps {
  data?: DataType[]
  pageInfo?: any
  total?: number
  loading?: boolean
  setPageInfo: (params: Record<string, any>) => void
  siteId?: number
}

const QaTable: React.FC<QaTableProps> = ({
  data = [],
  pageInfo,
  total,
  loading,
  setPageInfo,
  siteId
}) => {
  const navigate = useNavigate()
  const handleChange = (page: number, pageSize?: number) => {
    setPageInfo({
      current: page,
      size: pageSize
    })
  }
  const columns = [
    {
      title: 'Question',
      dataIndex: 'question',
      key: 'question'
    },
    {
      title: 'Answer',
      dataIndex: 'answerList',
      key: 'answerList',
      render: (_: string, record: DataType) => {
        const content = (
          <div style={{ width: 400 }}>
            {record?.answerList?.map((item, index) => (
              <p key={item.id}>
                {record?.answerList?.length > 1 && (
                  <p style={{ color: '#999' }}>Answer{index}</p>
                )}
                <span>{item.content}</span>
              </p>
            ))}
          </div>
        )
        return (
          <Popover content={content} title="Title">
            {record?.answerList?.map((item) => (
              <span key={item.id}>{item.content}</span>
            ))}
          </Popover>
        )
      }
    },
    {
      title: 'Label',
      dataIndex: 'label',
      key: 'label'
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (_: string, record: DataType) =>
        record.status === 1 ? (
          <span style={{ display: 'flex', alignItems: 'center' }}>
            <i
              style={{
                display: 'inline-block',
                width: '8px',
                height: '8px',
                borderRadius: '50%',
                background: '#48C43D',
                marginRight: '4px'
              }}
            ></i>
            Open
          </span>
        ) : (
          <span>Close</span>
        )
    },
    {
      title: 'Update Time',
      dataIndex: 'updatedAt',
      key: 'updatedAt'
    },
    {
      title: 'Operator',
      dataIndex: 'operator',
      key: 'operator'
    },
    {
      title: 'Action',
      key: 'action',
      render: (_: string, record: DataType) => (
        <Space size="middle">
          <Button
            type="link"
            onClick={() => {
              navigate(`/settings/labelForm/edit?id=${record.id}`, {
                state: { siteId }
              })
            }}
          >
            Edit
          </Button>
          {/* <a>Delete</a> */}
        </Space>
      )
    }
  ]
  return (
    <>
      <FsTable
        data={data}
        columns={columns}
        className={style.page_table_wrap}
        loading={loading}
      ></FsTable>
      <div className={style.pagination}>
        <Pagination
          current={pageInfo.current}
          pageSize={pageInfo.size}
          pageSizeOptions={['10', '20', '50']}
          showSizeChanger
          showTotal={(total) => `Total ${total} items`}
          total={total}
          onChange={handleChange}
        />
      </div>
    </>
  )
}

export default React.memo(QaTable)
