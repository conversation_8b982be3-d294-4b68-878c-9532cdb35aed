import {
  addCategory,
  deleteCategory,
  getCategoryTree,
  updateCategory
} from '@/api/settings'
import { Dropdown, Form, Input, MenuProps, Modal, Tree, message } from 'antd'
import type { DataNode, TreeProps } from 'antd/es/tree'
import { memo, useEffect, useState } from 'react'
import style from '../../style/labelSetting.module.scss'
interface TreeListProps {
  siteId: number
  parentMessage?: (value: any) => void
}
const TreeList: React.FC<TreeListProps> = ({ siteId, parentMessage }) => {
  const [form] = Form.useForm()
  const [editOpen, setEditOpen] = useState(false)
  const [editType, setEditType] = useState<'add' | 'edit'>(null)
  const [editNode, setEditNode] = useState<UpdateCategoryParams>({
    id: null,
    content: '',
    parentId: null,
    siteId
  })
  const [treeData, setTreeData] = useState<DataNode[]>([])
  useEffect(() => {
    getTreeData()
  }, [siteId])
  //获取树形数据
  const getTreeData = async () => {
    try {
      const data: any = await getCategoryTree({ siteId })
      console.log(data?.trees)
      setTreeData(data?.trees)
    } catch (error) {
      console.log(error)
    }
  }
  const onSelect: TreeProps['onSelect'] = (selectedKeys, info) => {
    parentMessage && parentMessage(selectedKeys)
    console.log('selected', selectedKeys, info)
  }
  //确认询问
  const confirm = (item: any) => {
    //阻止冒泡
    Modal.confirm({
      title: 'Confirm',
      icon: null,
      content: (
        <div>
          Are you sure you want to delete this label?
          <br />
          <span style={{ color: '#999', fontSize: 12 }}>
            The operation is irreversible, please operate with caution
          </span>
        </div>
      ),
      okText: 'confirm',
      cancelText: 'cancel',
      onOk() {
        console.log('OK')
        deleteNode(item)
      },
      onCancel() {
        console.log('Cancel')
      }
    })
  }
  const addNode = (item: any) => {
    setEditType('add')
    console.log('addNode', item)
    setEditNode((prev) => {
      return {
        ...prev,
        parentId: item.id
      }
    })
    setEditOpen(true)
  }
  const deleteNode = async (item: any) => {
    console.log('deleteNode', item)
    try {
      await deleteCategory({ id: item.id })
      getTreeData()
      message.success('delete success')
    } catch (error) {
      console.log(error)
      message.error('delete fail')
    }
  }
  //编辑
  const editMode = (item: any) => {
    console.log('editMode', item)
    setEditType('edit')
    setEditNode((prev) => {
      return {
        ...prev,
        id: item.id,
        parentId: item.parentId,
        content: item.name
      }
    })
    form.setFieldsValue({
      label: item.name
    })
    setEditOpen(true)
  }
  const handleOption = (key: string, item: any) => {
    switch (key) {
      case '1':
        addNode(item)
        break
      case '2':
        editMode(item)
        break
      case '3':
        confirm(item)
        break
      default:
        break
    }
  }
  const items: MenuProps['items'] = [
    {
      key: '1',
      label: 'Add'
    },
    {
      key: '2',
      label: 'Edit'
    },
    {
      key: '3',
      label: 'Delete'
    }
  ]
  const onTitleRender = (item: DataNode | any) => {
    return (
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}
      >
        <span>{item?.name}</span>
        <span
          style={{
            display: 'flex',
            marginLeft: 12,
            justifyContent: 'space-around'
          }}
        >
          {/* {item?.parentId === 0 && (
            <i
              className="iconfont icon-tianjia"
              style={{ marginLeft: 6 }}
              onClick={(e) => addNode(e, item)}
            ></i>
          )}
          <i
            className="iconfont icon-bianjixinxi"
            style={{ marginLeft: 6 }}
            onClick={(e) => editMode(e, item)}
          ></i>
          <i
            className="iconfont icon-shanchu"
            style={{ marginLeft: 6 }}
            onClick={(e) => confirm(e, item)}
          ></i> */}
          <Dropdown
            menu={{
              items: item?.parentId === 0 ? items : items.slice(1),
              onSelect: ({ key, ...rest }) => {
                console.log('rest', rest)
                handleOption(key, item)
              },
              onClick: ({ key, domEvent }) => {
                handleOption(key, item)
                domEvent.preventDefault()
                domEvent.stopPropagation()
              }
            }}
            placement="bottomRight"
          >
            <i className="iconfont icon-gengduo"></i>
          </Dropdown>
        </span>
      </div>
    )
  }
  const editChange = (e: any) => {
    console.log(e.target.value)
    setEditNode((prev) => {
      return {
        ...prev,
        content: e.target.value
      }
    })
  }
  const handleOk = () => {
    //表单验证
    form
      .validateFields()
      .then((values) => {
        console.log(values)
        if (editType === 'add') {
          addCategoryRequest(editNode)
        } else {
          updateCategoryRequest(editNode)
        }
      })
      .catch((error) => {
        console.log(error)
      })
  }
  const handleCancel = () => {
    resetEditNode()
    setEditOpen(false)
  }
  const resetEditNode = () => {
    //表单重置
    form.resetFields()
    setEditNode((prev) => {
      return {
        ...prev,
        id: null,
        parentId: null,
        content: ''
      }
    })
  }
  //添加分类请求
  const addCategoryRequest = async (params: any) => {
    try {
      await addCategory(params)
      resetEditNode()
      setEditOpen(false)
      message.success('add success')
      getTreeData()
    } catch (error) {
      console.log(error)
      message.error('add fail')
    }
  }
  //更新分类请求
  const updateCategoryRequest = async (params: any) => {
    try {
      await updateCategory(params)
      resetEditNode()
      setEditOpen(false)
      message.success('update success')
      getTreeData()
    } catch (error) {
      console.log(error)
      message.error('update fail')
    }
  }
  return (
    <>
      <div className={style.title}>
        <span>All Labels</span>
        <span className={style.title_right}>
          <i
            className="iconfont icon-tianjia"
            onClick={() => addNode({ id: 0 })}
          ></i>
        </span>
      </div>
      <Tree
        onSelect={onSelect}
        treeData={treeData}
        rootStyle={{ paddingLeft: '12px', paddingTop: '16px' }}
        icon={<i className="iconfont icon-you"></i>}
        titleRender={onTitleRender}
        fieldNames={{ title: 'name', key: 'id', children: 'children' }}
        blockNode
      ></Tree>
      <Modal
        title={editType === 'add' ? 'Add Label' : 'Edit Label'}
        open={editOpen}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Form layout="vertical" form={form}>
          <Form.Item
            label={
              editNode.parentId === 0
                ? 'First Level Label'
                : 'Second Level Label'
            }
            name="label"
            rules={[{ required: true, message: 'Please input label name!' }]}
            initialValue={editNode.content}
          >
            <Input
              value={editNode.content}
              // defaultValue={editNode.content}
              onChange={(e) => editChange(e)}
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}

export default memo(TreeList)
