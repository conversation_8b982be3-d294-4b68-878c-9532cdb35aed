.label_setting_wrap {
  padding: 0 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page_container {
  display: flex;
  flex: 1;
  overflow-y: hidden;
  .page_left_wrap {
    width: 300px;
    background-color: #fff;
    margin-right: 20px;
    position: relative;
    .title {
      padding: 23px 20px;
      font-size: 20px;
      font-weight: 500;
      line-height: 30px;
      color: #303133;
      box-shadow: inset 0px -1px 0px 0px #e4e7ed;
      display: flex;
      justify-content: space-between;
      cursor: pointer;
    }
  }
  .page_right_wrap {
    flex: 1;
    overflow: hidden;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    .page_table_wrap {
      // padding: 0 20px;
      flex: 1;
      overflow-y: auto;
      :global {
        .ant-table-thead {
          tr > th {
            background-color: #fff;
          }
        }
        .table_row {
          &:nth-child(2n) {
            td {
              background-color: #fafafa !important;
            }
          }
        }
      }
    }
    .pagination {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      padding: 24px 20px;
      background-color: #fff;
      box-shadow: 0px -3px 8px 0px rgba(0, 0, 0, 0.13);
    }
  }
}
