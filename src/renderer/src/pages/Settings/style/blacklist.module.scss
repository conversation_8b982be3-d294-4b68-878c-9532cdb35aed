.blacklist_container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
  .blacklist_table {
    margin-top: 20px;
    padding: 0 20px;
    flex: 1;
    background-color: #fff;
    overflow: auto;
    :global {
      .ant-table-thead {
        tr > th {
          background-color: #fff;
        }
      }
      .table_row {
        &:nth-child(2n) {
          td {
            background-color: #fafafa !important;
          }
        }
      }
    }
  }
  .pagination {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 24px 20px;
    background-color: #fff;
    box-shadow: 0px -3px 8px 0px rgba(0, 0, 0, 0.13);
    z-index: 1;
  }
}
