import addressMap from './addressMap'
import request from '@renderer/request'

export const getVersionList = () => {
  return request({
    url: addressMap.GET_VERSION_LIST,
    method: 'GET'
  })
}

export const addVersion = (data: VersionParams) => {
  return request({
    url: addressMap.ADD_VERSION,
    method: 'POST',
    data
  })
}

export const getVersionDetail = (id: string) => {
  return request({
    url: addressMap.GET_VERSION_DETAIL,
    method: 'GET',
    params: { id }
  })
}

export const updateVersionDetail = (data: VersionParams) => {
  return request({
    url: addressMap.PUT_VERSION_DETAIL,
    method: 'PUT',
    data
  })
}
