declare type LoginType = {
  code: string
  module: number
}
declare type CustomerStatusType = {
  statusId: number
  manageUserId: number
}
declare type EmailSendMessageType = {
  cc: string
  email: string
  groupIdStr: string
  languagesId: number
  timeZone: string
}
declare type CreateTicketType = {
  customersId: string
  customersServiceId: string
  feedbackId?: string
  groupId: string
  messageEnd?: string
}
declare type CheckEmployeeIdType = {
  employeeId: string
}
