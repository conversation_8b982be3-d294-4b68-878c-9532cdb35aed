import request from '@/request'
import addressMap from './addressMap'

export const loginByCode = (data: LoginType) => {
  return request({
    url: addressMap['LOGIN_BY_CODE'],
    method: 'POST',
    data
  })
}
//文件上传
export const uploadFile = (data: any, config: any = {}) => {
  return request({
    url: `${addressMap['UPLOAD_FILE']}?timestamp=${new Date().getTime()}`,
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    ...config
  })
}
// 设置客服状态
export const setCustomerStatus = (data: CustomerStatusType) => {
  return request({
    url: addressMap['SET_CUSTOMER_STATUS'],
    method: 'PUT',
    data
  })
}
// 邮件发送聊天记录
export const setChatMessage = (data: EmailSendMessageType) => {
  return request({
    url: addressMap['SET_CHAT_MESSAGE'],
    method: 'POST',
    data
  })
}
// 创建工单
export const createTicket = (data: CreateTicketType) => {
  return request({
    url: addressMap['CREATE_TICKET'],
    method: 'POST',
    data
  })
}
// 检查雇员id是否存在
export const checkEmployeeId = (data: CheckEmployeeIdType) => {
  return request({
    url: addressMap['CHECK_EMPLOYEE_ID'] + data.employeeId,
    method: 'GET'
  })
}
