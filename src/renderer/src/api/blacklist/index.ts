import request from '@/request'
import addressMap from './addressMap'

export const getBlacklistRequest = (params: BlacklistParamsType) => {
  return request({
    url: addressMap.GET_BLACKLIST,
    method: 'GET',
    params
  })
}
export const getBlacklistExpireTime = () => {
  return request({
    url: addressMap.GET_BLACKLIST_EXPIRE_TIME,
    method: 'GET'
  })
}

export const deleteBlacklistById = (params: DeleteBlacklistParams) => {
  return request({
    url: `${addressMap.DELETE_BLACKLIST_BY_ID}/${params.blacklistId}`,
    method: 'DELETE'
  })
}
/**
 * @description: 所有添加黑名单的客服列表
 * @param null params
 * @return: Promise
 */
export const getBlacklistCustomerList = () => {
  return request({
    url: addressMap['ADD_CUSTOM_BLACKLIST'],
    method: 'GET'
  })
}
//加入黑名单
export const createBlacklist = (data: BlackAddType) => {
  console.log(data)
  return request({
    url: addressMap['CREATE_BLACKLIST'],
    method: 'POST',
    data
  })
}
//获取过期时间列表
export const getBlacklistExpireTimes = () => {
  return request({
    url: addressMap['GET_BLACKLIST_EXPIRE_TIMES'],
    method: 'GET'
  })
}
