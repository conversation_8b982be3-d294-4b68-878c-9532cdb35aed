export const enum addressMap {
  GET_MANAGE_LIST = 'livechat/manage/list',
  GET_MANAGE_LIST_OPTION = '/livechat/manage/list/option',
  GET_RESOURCE_COUNT = '/livechat/manage/resource/count',
  GET_MANAGE_STATUS = '/livechat/manage/status/',
  PUT_MANAGE_STATUS = '/livechat/manage/status/',
  PUT_MANAGE_INFO = '/livechat/manage/',
  DEL_MANAGE_INFO = '/livechat/manage/',
  GET_EMPLOYEE_LIST = '/livechat/manage/employee/list',
  GET_GROUP_LIST = '/livechat/manage/group/list',
  GET_GROUP_ALL_MEMBER = '/livechat/manage/member/list',
  GET_SITE_LIST = '/livechat/manage/site',
  POST_GROUP_INFO = '/livechat/manage/group',
  GET_GROUP_LIST_OPTION = '/livechat/manage/group/list/option', // 获取客服组列表筛选项
  GET_GROUP_DETAILS = '/livechat/manage/group/', // 获取客服组详情
  PUT_GROUP_UPDATE = '/livechat/manage/group/', // 修改客服组信息
  GET_SITE_RECEPTION = '/livechat/manage/site/{siteId}/reception/list', //获取站点接待列表
  GET_EMPLOYEE_IS_ALREADY = '/livechat/manage/exist/employee/', //验证客服是否已经被创建
  GET_CREATE_CUSTOMER_OPTION = '/livechat/manage/create/option', // 获取创建客服的选项
  POST_CREATE_CUSTOMER = '/livechat/manage', //创建客服
  GET_CUSTOMER_DETAILS = '/livechat/manage/', //获取客服详情
  PUT_EDIT_CUSTOMER = '/livechat/manage/', //编辑客服
  GET_CHECK_ASSISTANT = '/livechat/manage/check/sale/assistant/', //检查是否可以添加该名助理
  GET_MANAGE_LIST_STATUS = '/livechat/manage/status/list' // 获取客服状态列表
}
