import request from '@/request'
import { addressMap } from './addressMap'

/**
 * @description: 获取客服列表
 * @param {any} params
 * @return {*}
 */
export const getManageList = (params: Record<string, any>) => {
  return request({
    url: addressMap.GET_MANAGE_LIST,
    method: 'GET',
    params
  })
}

/**
 * @description: 获取客服列表筛选项
 * @return {*}
 */
export const getManageListOption = () => {
  return request({
    url: addressMap.GET_MANAGE_LIST_OPTION,
    method: 'GET'
  })
}
/**
 * @description: 获取客服客服组统计
 * @return {*}
 */
export const getResourceCount = () => {
  return request({
    url: addressMap.GET_RESOURCE_COUNT,
    method: 'GET'
  })
}
/**
 * @description: 获取客服在线状态 1:在线 2:离线 3:忙碌
 * @param {number} id 客服id
 * @return {*}
 */
export const getManageStatus = (id: number) => {
  return request({
    url: addressMap.GET_MANAGE_STATUS + id,
    method: 'GET'
  })
}
/**
 * @description: 更新客服状态
 * @param {number} id
 * @return {*}
 */
export const putManageStatus = (id: number, status: number) => {
  return request({
    url: addressMap.PUT_MANAGE_STATUS + id,
    method: 'PUT',
    data: {
      status
    }
  })
}

/**
 * @description: 更新客服信息
 * @param {number} id
 * @param {any} params 客服信息体
 * @return {*}
 */
export const putManageInfo = (id: number, params: Record<string, any>) => {
  return request({
    url: addressMap.PUT_MANAGE_INFO + id,
    method: 'PUT',
    data: params
  })
}

/**
 * @description: 删除客服
 * @param {number} id
 * @return {*}
 */
export const delManageInfo = (id: number) => {
  return request({
    url: addressMap.DEL_MANAGE_INFO + id,
    method: 'DELETE'
  })
}

/**
 * @description: 获取员工列表
 * @param {any} params
 * @return {*}
 */
export const getEmployeeList = () => {
  return request({
    url: addressMap.GET_EMPLOYEE_LIST,
    method: 'GET'
  })
}

/**
 * @description: 获取客服组列表
 * @param {any} params
 * @return {*}
 */
export const getGroupList = (params: Record<string, any>) => {
  return request({
    url: addressMap.GET_GROUP_LIST,
    method: 'GET',
    params
  })
}

/**
 * @description: 获取所有客服成员
 * @return {*}
 */
export const getGroupAllMember = () => {
  return request({
    url: addressMap.GET_GROUP_ALL_MEMBER,
    method: 'GET'
  })
}

/**
 * @description: 获取所有站点信息
 * @return {*}
 */
export const getAllSite = () => {
  return request({
    url: addressMap.GET_SITE_LIST,
    method: 'GET'
  })
}

/**
 * @description: 创建客服组
 * @param {Record} data
 * @param {*} any
 * @return {*}
 */
export const creatGroup = (data: Record<string, any>) => {
  return request({
    url: addressMap.POST_GROUP_INFO,
    method: 'POST',
    data
  })
}

// 获取客服组列表筛选项
export const getGroupListOption = () => {
  return request({
    url: addressMap.GET_GROUP_LIST_OPTION,
    method: 'GET'
  })
}

// 获取客服组详情
export const getGroupDetails = (id: number) => {
  return request({
    url: addressMap.GET_GROUP_DETAILS + id,
    method: 'GET'
  })
}

// 修改客服组信息
export const putGroupUpdate = (id: number, data: UpdateGroup) => {
  return request({
    url: addressMap.PUT_GROUP_UPDATE + id,
    method: 'PUT',
    data
  })
}

// 获取站点接待列表
export const getSiteReception = (siteId: number) => {
  return request({
    url: addressMap.GET_SITE_RECEPTION.replace('{siteId}', siteId.toString()),
    method: 'GET'
  })
}

// 验证客服是否已经存在
export const getEmployeeIsAlready = (employeeId: number) => {
  return request({
    url: addressMap.GET_EMPLOYEE_IS_ALREADY + employeeId,
    method: 'GET'
  })
}

// 获取创建客服选项
export const getCreateCustomerOption = () => {
  return request({
    url: addressMap.GET_CREATE_CUSTOMER_OPTION,
    method: 'GET'
  })
}

// 创建客服
export const postCreateCustomer = (data: Record<string, any>) => {
  return request({
    url: addressMap.POST_CREATE_CUSTOMER,
    method: 'POST',
    data
  })
}

// 获取客服详情
export const getCustomerDetails = (id: number) => {
  return request({
    url: addressMap.GET_CUSTOMER_DETAILS + id,
    method: 'GET'
  })
}

// 编辑客服
export const putEditCustomer = (id: string, data: Record<string, any>) => {
  return request({
    url: addressMap.PUT_EDIT_CUSTOMER + id,
    method: 'PUT',
    data
  })
}

// 检查是否可以添加该名助理
export const getCheckAssistant = (id: number, manageId: number) => {
  return request({
    url: addressMap.GET_CHECK_ASSISTANT + id,
    method: 'GET',
    params: {
      manageId
    }
  })
}

//获取客服状态列表
export const getManageStatusList = () => {
  return request({
    url: addressMap.GET_MANAGE_LIST_STATUS,
    method: 'GET'
  })
}
