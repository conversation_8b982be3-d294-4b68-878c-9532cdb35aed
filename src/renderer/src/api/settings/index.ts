import request from '@/request'
import addressMap from './addressMap'

//获取分类树
export const getCategoryTree = (params: CategoryListType) => {
  return request({
    url: addressMap['GET_CATEGORY_TREE'],
    method: 'GET',
    params
  })
}
//获取所有站点列表
export const getStationList = () => {
  return request({
    url: addressMap['GET_STATION_LIST'],
    method: 'GET'
  })
}
//添加分类
export const addCategory = (data: AddCategoryParams) => {
  return request({
    url: addressMap['ADD_CATEGORY'],
    method: 'POST',
    data
  })
}
//删除分类
export const deleteCategory = (params: DeleteCategoryParams) => {
  return request({
    url: `${addressMap['DELETE_CATEGORY']}`,
    method: 'DELETE',
    params
  })
}
//更新分类
export const updateCategory = (data: UpdateCategoryParams) => {
  return request({
    url: addressMap['UPDATE_CATEGORY'],
    method: 'PUT',
    data
  })
}
//获取问题列表
export const getQuestionList = (data: QuestionListParamsType) => {
  return request({
    url: addressMap['GET_QUESTION_LIST'],
    method: 'POST',
    data
  })
}
//获取问题详情
export const getQuestionDetail = (params: QuestionDetailParams) => {
  return request({
    url: `${addressMap['GET_QUESTION_DETAIL']}/${params.id}`,
    method: 'GET'
  })
}
//添加问题
export const addQuestion = (data: AddQuestionParams) => {
  return request({
    url: addressMap['ADD_QUESTION'],
    method: 'POST',
    data
  })
}
//修改问题
export const updateQuestion = (data: AddQuestionParams) => {
  return request({
    url: addressMap['DELETE_QUESTION'],
    method: 'PUT',
    data
  })
}
