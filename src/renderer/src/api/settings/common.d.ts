declare type CategoryListType = {
  siteId?: number
}
declare type UpdateCategoryParams = {
  content: string
  id: number
  parentId: number
  siteId: number
}
//剔除 UpdateCategoryParams的id属性
declare type AddCategoryParams = Omit<UpdateCategoryParams, 'id'>
declare type DeleteCategoryParams = {
  id: number
}
declare type QuestionListParamsType = {
  cidList?: any[]
  current?: number
  search?: string
  siteId?: number
  size?: number
  status?: number
}
declare type QuestionDetailParams = {
  id: number
}

declare interface AnswerList {
  content?: string
  id?: number
}
declare type AddQuestionParams = {
  answerList?: AnswerList[]
  categoryId?: number
  questionContent?: string
  questionId?: number
  siteId?: number
  status?: number
  userId?: number
}
//修改AddQuestionParams的categoryId类型
declare type FormQuestionParams = Omit<AddQuestionParams, 'categoryId'> & {
  categoryId: Array<number>
}
