import request from '@/request'
import addressMap from './addressMap'

export const getFilterItem = () => {
  return request({
    url: addressMap.GET_FILTER_ITEM,
    method: 'GET'
  })
}
//获取聊天用户列表
export const getChatUserList = (data: ChatUserListType) => {
  return request({
    url: addressMap.GET_CHAT_USER_LIST,
    method: 'POST',
    data
  })
}
//获取聊天详情
export const getChatDetail = (data: ChatDetailType) => {
  return request({
    url: addressMap.GET_CHAT_DETAIL,
    method: 'POST',
    data
  })
}
