declare type ChatDetailParams = {
  groupIdStr: string
  current: number
  size: number
  appId: number
  timeZone: string
  isSupervision: boolean
  supervisionCustomerServiceId: string
  isFinished?: boolean
}
declare type SupervisedParams = {
  timeZone: string
}

declare type FinishedParams = {
  customerServiceIdStr: string
  timeZone: string
}
declare type TodayChatParams = SupervisedParams & {
  userId: string
}

declare type ShortcutType = {
  typeId: number
  search?: string
}

declare type ShortcutGroupListType = {
  typeId: number
}

declare type CreateShortcutGroupType = {
  name: string
  typeId: number
}

declare type UpdateShortcutGroupType = {
  name: string
}

declare type AddShortcutType = {
  content: string
  groupId: number
  typeId: number
  prompt: string
}

declare type EditShortcutType = {
  content: string
  groupId: number
  prompt: string
}

declare type EditShortcutGroupNameType = {
  name: string
}

declare type UpdateTicketStatusParams = {
  id: string
  ticketStatus: number
  userId: string
}
