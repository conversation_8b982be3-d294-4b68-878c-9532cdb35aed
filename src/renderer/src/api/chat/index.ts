import request from '@/request'
import addressMap from './addressMap'

export const getChatDetail = (params: ChatDetailParams) => {
  return request({
    url: addressMap.GET_CHAT_DETAIL,
    method: 'POST',
    data: params
  })
}

export const getSupervisedChatList = (params: SupervisedParams) => {
  return request({
    url: addressMap.GET_SUPERVISED_CHAT_LIST,
    method: 'GET',
    params
  })
}

export const getFinishedChatList = (params: FinishedParams) => {
  return request({
    url: addressMap.GET_FINISHED_CHAT_LIST,
    method: 'POST',
    data: params
  })
}

export const getTransferList = () => {
  return request({
    url: addressMap.GET_TRANSFER_LIST,
    method: 'GET'
  })
}

export const getTodayChatData = (params: TodayChatParams) => {
  return request({
    url: addressMap.GET_TODAY_CHAT_DATA,
    method: 'GET',
    params
  })
}

// 获取快捷语列表
export const getShortcut = (data: ShortcutType) => {
  return request({
    url: addressMap.GET_SHORTCUT,
    method: 'GET',
    params: data
  })
}

// 获取快捷语编辑权限
export const getShortcutPermission = (params: { typeId: number }) => {
  return request({
    url: addressMap.GET_SHORTCUT_PERMISSION,
    method: 'GET',
    params
  })
}

// 获取快捷语分组列表
export const getShortcutGroupList = (data: ShortcutGroupListType) => {
  return request({
    url: addressMap.GET_SHORTCUT_GROUP_LIST,
    method: 'GET',
    params: data
  })
}

// 创建快捷语分组
export const postShortcutCreateGroup = (data: CreateShortcutGroupType) => {
  return request({
    url: addressMap.POST_SHORTCUT_CREATE_GROUP,
    method: 'POST',
    data
  })
}

// 修改快捷语组名
export const putEditShortcutGroupName = (
  id: number,
  data: UpdateShortcutGroupType
) => {
  return request({
    url: addressMap.PUT_EDIT_SHORTCUT_GROUP_NAME + id,
    method: 'PUT',
    data
  })
}

// 删除快捷语分组
export const deleteShortcutGroup = (id: number) => {
  return request({
    url: addressMap.DELECT_SHORTCUT_GROUP + id,
    method: 'DELETE'
  })
}

// 删除快捷语
export const deleteShortcut = (id: number) => {
  return request({
    url: addressMap.DELECT_SHORTCUT + id,
    method: 'DELETE'
  })
}

// 添加快捷语
export const postShortcut = (data: AddShortcutType) => {
  return request({
    url: addressMap.POST_SHORTCUT,
    method: 'POST',
    data
  })
}

// 编辑快捷语
export const putEditShortcut = (id: number, data: EditShortcutType) => {
  return request({
    url: addressMap.PUT_EDIT_SHORTCUT + id,
    method: 'PUT',
    data
  })
}

// 编辑快捷语组
export const putEditShortcutGroup = (
  id: number,
  data: EditShortcutGroupNameType
) => {
  return request({
    url: addressMap.PUT_EDIT_SHORTCUT_GROUP + id,
    method: 'PUT',
    data
  })
}
// 获取快捷语提示列表
export const getShortcutPromptList = (params: { search: string }) => {
  return request({
    url: addressMap.GET_SHORTCUT_PROMPT,
    method: 'GET',
    params
  })
}

export const updateTicketStatus = (data: UpdateTicketStatusParams) => {
  return request({
    url: addressMap.PUT_TICKET_STATUS,
    method: 'PUT',
    data
  })
}
