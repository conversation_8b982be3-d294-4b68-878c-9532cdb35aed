import request from '@/request'
import addressMap from './addressMap'

// 获取ticket列表
export const postTicketList = (data: TicketListType) => {
  return request(
    {
      url: addressMap['POST_TICKET_LIST'],
      method: 'POST',
      data
    },
    true
  )
}

// 获取ticket列表筛选项
export const getTicketFilter = () => {
  return request({
    url: addressMap['GET_TICKET_FILTER'],
    method: 'GET'
  })
}

// 合并ticket
export const putMergeTicket = (data: MergeTicketType) => {
  return request({
    url: addressMap['PUT_MERGE_TICKET'],
    method: 'PUT',
    data
  })
}

// 获取ticket详情
export const postTicketDetail = (data: TicketDetailType) => {
  return request({
    url: addressMap['POST_TICKET_DETAIL'],
    method: 'POST',
    data
  })
}

// ticket所属客服变更
export const putChangeTicketCustomer = (data: ChangeTicketCustomerType) => {
  return request({
    url: addressMap['PUT_CHANGE_TICKET_CUSTOMER'],
    method: 'PUT',
    data
  })
}
