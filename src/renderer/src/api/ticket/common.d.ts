// 获取ticket列表参数类型
declare type TicketListType = {
  current?: number
  customerServiceId?: string
  keyWorld?: string
  rate?: number
  searchDate?: string[]
  siteCode?: string
  size?: number
  type?: number
  timeZone: string
}

declare type MergeTicketType = {
  idList: number[]
}

declare type TicketDetailType = {
  id: string
  current: number
  size: number
  timeZone
}

declare type ChangeTicketCustomerType = {
  id: string
  ticketStatus: number
  userId: string
}
