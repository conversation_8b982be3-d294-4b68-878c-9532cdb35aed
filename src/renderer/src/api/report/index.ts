import request from '@renderer/request'
import { addressMap } from './addressMap'

export const getTotalCharts = (data: ReportDataParams) => {
  return request(
    {
      url: addressMap.GET_TOTAL_CHATS,
      method: 'POST',
      data
    },
    true
  )
}

export const getSatisfaction = (data: ReportDataParams) => {
  return request(
    {
      url: addressMap.GET_SATISFACTION,
      method: 'POST',
      data
    },
    true
  )
}

export const getSatisfactionAgents = (data: ReportDataParams) => {
  return request(
    {
      url: addressMap.GET_SATISFACTION_AGENTS,
      method: 'POST',
      data
    },
    true
  )
}

export const getTotalHeatmap = (data: ReportDataParams) => {
  return request(
    {
      url: addressMap.GET_TOTAL_HEATMAP,
      method: 'POST',
      data
    },
    true
  )
}

export const getChatDuration = (data: ReportDataParams) => {
  return request(
    {
      url: addressMap.GET_CHAT_DURATION,
      method: 'POST',
      data
    },
    true
  )
}

export const getFirstResponseTime = (data: ReportDataParams) => {
  return request(
    {
      url: addressMap.GET_FIRST_RESPONSE_TIME,
      method: 'POST',
      data
    },
    true
  )
}

export const getResolutionTime = (data: ReportDataParams) => {
  return request(
    {
      url: addressMap.GET_RESOLUTION_TIME,
      method: 'POST',
      data
    },
    true
  )
}

export const getSyncTickets = (data: ReportDataParams) => {
  return request(
    {
      url: addressMap.GET_SYNC_TICKETS,
      method: 'POST',
      data
    },
    true
  )
}

export const getFirstResponseTickets = (data: ReportDataParams) => {
  return request(
    {
      url: addressMap.GET_FIRST_RESPONSE_TICKETS,
      method: 'POST',
      data
    },
    true
  )
}

export const getFilterItem = () => {
  return request(
    {
      url: addressMap.GET_FILTER_ITEM,
      method: 'GET'
    },
    true
  )
}

export const exportChartsData = (type: string, data: ReportDataParams) => {
  // 根据调用接口，导出CSV
  return request(
    {
      url: `/livechat/reports/${type}/export`,
      method: 'POST',
      data
    },
    true
  )
}

export const getAgentActivity = (data: AgentActivityParams) => {
  return request({
    url: addressMap.GET_AGENT_ACTIVITY,
    method: 'POST',
    data
  })
}

export const getChatAvailability = (data: ReportDataParams) => {
  return request({
    url: addressMap.GET_CHAT_AVAILABILITY,
    method: 'POST',
    data
  })
}

export const getChatNumberStatus = (data: ReportDataParams) => {
  return request({
    url: addressMap.GET_CHAT_NUMBER_STATUS,
    method: 'POST',
    data
  })
}

export const getManageList = () => {
  return request({
    url: addressMap.GET_MANAGE_LIST,
    method: 'GET'
  })
}
