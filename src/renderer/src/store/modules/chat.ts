import { TabPosition } from '@/pages/Chats/component/ContactList'
import { createSlice } from '@reduxjs/toolkit'

export type ChatRoomInfo = {
  groupId: string
}

type ChatDetailType = UserDetail & {
  [key: string]: any
}
export type SupervisedCustomerType = {
  name: string
  avatar: string
}
export type MessageCountMap = {
  [key: string]: number
}

export type EditShortcutGroupTypes = {
  name: string
  id: number
}

export interface ChatState {
  chatRoomInfo: ChatRoomInfo
  chatDetail: ChatDetailType
  messageCountMap?: MessageCountMap
  chatMode: TabPosition
  supervisionList: SupervisedCustomerType[]
  isShowAddOrEditShortcutModal?: boolean
  editShortcutGroup?: EditShortcutGroupTypes
  selectedShortcut?: string
  applyStatus?: false
  draftBox?: {
    [key: string]: string
  }
  isFinished?: boolean
}

const initialState: ChatState = {
  chatRoomInfo: {
    groupId: '0'
  },
  chatDetail: {},
  messageCountMap: {},
  chatMode: 'chatting',
  supervisionList: [],
  isShowAddOrEditShortcutModal: false,
  editShortcutGroup: {
    name: '',
    id: 0
  },
  selectedShortcut: '',
  applyStatus: false,
  draftBox: {},
  isFinished: false
}

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    setChatRoomInfo(state, { payload }) {
      state.chatRoomInfo = payload
    },
    setChatDetail(state, { payload }) {
      state.chatDetail = payload
    },
    setMessageCountMap(state, { payload }) {
      const hasId = Object.keys(state.messageCountMap).includes(payload.groupId)
      if (hasId) {
        state.messageCountMap[payload.groupId] =
          state.messageCountMap[payload.groupId] + 1
      } else {
        state.messageCountMap[payload.groupId] = 1
      }
    },
    initMessageCountMap(state, { payload }) {
      state.messageCountMap[payload.groupId] = payload.unreadMsgCount
    },
    resetMessageCountMap(state, { payload }) {
      state.messageCountMap[payload.groupId] = 0
    },
    setGlobalChatMode(state, { payload }) {
      state.chatMode = payload
    },
    setSupervisionList(state, { payload }) {
      state.supervisionList = payload
    },
    setIsShowAddOrEditShortcutModal(state, { payload }) {
      state.isShowAddOrEditShortcutModal = payload
    },
    setEditShortcutGroupId(state, { payload }) {
      state.editShortcutGroup = payload
    },
    setSelectedShortcut(state, { payload }) {
      state.selectedShortcut = payload
    },
    setApplyRetentionStatus(state, { payload }) {
      state.applyStatus = payload
    },
    changeDraftMessage(state, { payload }) {
      state.draftBox[payload.id] = payload.draftMessage
    },
    updateChatDetail(state, { payload }) {
      state.chatDetail = { ...state.chatDetail, ...payload }
    },
    setChatStatus(state, { payload }) {
      state.isFinished = payload
    }
  }
})
export const {
  setChatRoomInfo,
  setChatDetail,
  setMessageCountMap,
  resetMessageCountMap,
  initMessageCountMap,
  setGlobalChatMode,
  setSupervisionList,
  setIsShowAddOrEditShortcutModal,
  setEditShortcutGroupId,
  setSelectedShortcut,
  setApplyRetentionStatus,
  changeDraftMessage,
  updateChatDetail,
  setChatStatus
} = chatSlice.actions

export default chatSlice.reducer
