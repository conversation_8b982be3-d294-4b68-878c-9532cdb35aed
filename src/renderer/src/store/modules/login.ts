import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
export interface UserInfoType {
  avatar?: string
  email?: string
  id?: number
  job?: string
  name?: string
  nickname?: string
  openId?: string
  permissions?: Array<Record<string, any>>
  token?: string
}
declare interface CustomerInfo {
  exp?: number
  iss?: string
  userId?: number
  deviceId?: number
  name?: string
  email?: string
  mobile?: string
  avatar?: string
}
export interface LoginState {
  userInfo: UserInfoType
  customerInfo: CustomerInfo
}

// 这里统一加载缓存的一些数据
export const loadLocalLogin = createAsyncThunk(
  'login/loadLocalLogin',
  (_, { dispatch }) => {
    const userInfo = localStorage.getItem('userInfo')
    if (userInfo) {
      dispatch(changeUserInfoAction(JSON.parse(userInfo)))
    }
  }
)
const initialState: LoginState = {
  userInfo: {},
  customerInfo: {}
}

const loginSlice = createSlice({
  name: 'login',
  initialState,
  reducers: {
    changeUserInfoAction(state, { payload }) {
      // 把数据存到redux里面，有点类似vuex
      state.userInfo = payload
      localStorage.setItem('userInfo', JSON.stringify(payload))
    },
    setCustomerInfo(state, { payload }) {
      state.customerInfo = payload
    }
  }
})

export const { changeUserInfoAction, setCustomerInfo } = loginSlice.actions

export default loginSlice.reducer
