import { setCustomerStatus } from '@/api/common'
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'

export interface CommonState {
  loading: boolean
  messageApi: string | null
  customerStatus: number | null
  socketStatus: SocketStatus
  globalAlertStatus: boolean
}
type SocketStatus = 'open' | 'error' | 'close' | 'networkError'

const initialState: CommonState = {
  loading: false,
  messageApi: null,
  customerStatus: 1,
  socketStatus: 'close',
  globalAlertStatus: false
}
export const setAsyncCustomerStatus = createAsyncThunk(
  'common/setAsyncCustomerStatus',
  async (params: CustomerStatusType, { dispatch }) => {
    try {
      await setCustomerStatus(params)
      dispatch(setCustomerLineStatus(params.statusId))
    } catch (error) {
      console.log(error)
    }
  }
)

const commonSlice = createSlice({
  name: 'common',
  initialState,
  reducers: {
    setLoading(state, { payload }) {
      state.loading = payload
    },
    setMessageApi(state, { payload }) {
      state.messageApi = payload
    },
    setCustomerLineStatus(state, { payload }) {
      state.customerStatus = payload
    },
    setSocketStatus(state, { payload }) {
      state.socketStatus = payload
      if (payload === 'open') {
        state.globalAlertStatus = false
      }
    },
    setGlobalAlertStatus(state, { payload }) {
      state.globalAlertStatus = payload
    }
  }
})

export const {
  setLoading,
  setMessageApi,
  setCustomerLineStatus,
  setSocketStatus,
  setGlobalAlertStatus
} = commonSlice.actions

export default commonSlice.reducer
