import { createSlice } from '@reduxjs/toolkit'

export interface MessageState {
  historyMessage: any[] //历史消息，用来筛选出状态未发送成功的消息
  messageList: MessageItemProps[] //消息列表
  currentReceiveMsg: MessageItemProps //当前接收到的消息
  currentSendMsg: MessageItemProps //当前发送的消息
  userFormData: MessageItemProps
}
const initialState: MessageState = {
  historyMessage: [],
  messageList: [],
  currentReceiveMsg: {},
  currentSendMsg: {},
  userFormData: {}
}

const messageSlice = createSlice({
  name: 'message',
  initialState,
  reducers: {
    setHistoryMessage(state, { payload }) {
      state.historyMessage = payload
    },
    setMessageList(state, { payload }) {
      state.messageList.push(payload)
    },
    setCurrentReceiveMsg(state, { payload }) {
      state.currentReceiveMsg = payload
    },
    setCurrentSendMsg(state, { payload }) {
      state.currentSendMsg = payload
    },
    updateUserFormData(state, { payload }) {
      state.userFormData = payload
    }
  }
})

export const {
  setHistoryMessage,
  setMessageList,
  setCurrentReceiveMsg,
  setCurrentSendMsg,
  updateUserFormData
} = messageSlice.actions

export default messageSlice.reducer
