import { configureStore } from '@reduxjs/toolkit'
import login, { loadLocalLogin } from './modules/login'
import common from './modules/common'
import message from './modules/message'
import chat from './modules/chat'

// 创建一个 Redux
const store = configureStore({
  reducer: {
    login,
    common,
    message,
    chat
  },
  devTools: process.env.NODE_ENV !== 'production'
})

// 统一在这里初始化一些缓存的数据
export function setupStore() {
  // 这里是缓存数据，程序加载会先调用这个
  store.dispatch(loadLocalLogin() as any)
}

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
export default store
