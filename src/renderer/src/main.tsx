import ReactDOM from 'react-dom/client'
import { Provider } from 'react-redux'
import store, { setupStore } from '@/store'
import { HashRouter } from 'react-router-dom'
import App from './App'
import '@/assets/iconfont/iconfont.css'
import './style/index.scss'
import { ConfigProvider } from 'antd'
import Empty from '@/component/Empty'
import ErrorBoundary from './utils/errorReporting'

const selfRenderEmpty = (componentName: any) => {
  //table设置空数据，自定义空数据组件
  if (componentName === 'Table') {
    return <Empty />
  }
  return null
}
setupStore()
ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <Provider store={store}>
    <HashRouter>
      <ConfigProvider renderEmpty={selfRenderEmpty}>
        <ErrorBoundary>
          <App />
        </ErrorBoundary>
      </ConfigProvider>
    </HashRouter>
  </Provider>
)

postMessage({ payload: 'removeLoading' }, '*')
