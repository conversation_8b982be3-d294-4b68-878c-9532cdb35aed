//自定义hooks，从redux里面拿去chatinfo，然后返回数据和修改数据的方法
import { TabPosition } from '@/pages/Chats/component/ContactList'
import { RootState } from '@/store'
import {
  ChatRoomInfo,
  MessageCountMap,
  SupervisedCustomerType,
  EditShortcutGroupTypes
} from '@/store/modules/chat'
import { useSelector, useDispatch } from 'react-redux'

type ChatInfoHook = {
  chatRoomInfo: ChatRoomInfo
  chatDetail: UserDetail
  messageCountMap: MessageCountMap
  chatMode: TabPosition
  supervisionList: SupervisedCustomerType[]
  isShowAddOrEditShortcutModal: boolean
  editShortcutGroup: EditShortcutGroupTypes
  selectedShortcut: string
  setChatInfo: (payload: ChatRoomInfo) => void
  setChatDetail: (payload: UserDetail) => void
  setMessageCountMap: (payload: MessageItemProps) => void
  resetMessageCountMap: (id: string) => void
  setGlobalChatMode: (mode: string) => void
  setSupervisionList: (payload: SupervisedCustomerType[]) => void
  setIsShowAddOrEditShortcutModal: (payload: boolean) => void
  setEditShortcutGroup: (payload: EditShortcutGroupTypes) => void
  setSelectedShortcut: (payload: string) => void
  setChatStatus: (payload: boolean) => void
}
export default function useChatInfo(): ChatInfoHook {
  const dispatch = useDispatch()
  const chatRoomInfo = useSelector(
    (state: RootState) => state.chat.chatRoomInfo
  )
  const chatDetail = useSelector((state: RootState) => state.chat.chatDetail)
  const messageCountMap = useSelector(
    (state: RootState) => state.chat.messageCountMap
  )
  const chatMode = useSelector((state: RootState) => state.chat.chatMode)
  const supervisionList = useSelector(
    (state: RootState) => state.chat.supervisionList
  )
  const isShowAddOrEditShortcutModal = useSelector(
    (state: RootState) => state.chat.isShowAddOrEditShortcutModal
  )
  const editShortcutGroup = useSelector(
    (state: RootState) => state.chat.editShortcutGroup
  )
  const selectedShortcut = useSelector(
    (state: RootState) => state.chat.selectedShortcut
  )

  const setGlobalChatMode = (mode: string) => {
    dispatch({ type: 'chat/setGlobalChatMode', payload: mode })
  }
  const setChatInfo = (payload: ChatRoomInfo) => {
    dispatch({ type: 'chat/setChatRoomInfo', payload })
  }
  const setChatDetail = (payload: UserDetail) => {
    dispatch({ type: 'chat/setChatDetail', payload })
  }
  const setMessageCountMap = (payload: MessageItemProps) => {
    dispatch({ type: 'chat/setMessageCountMap', payload })
  }
  const resetMessageCountMap = (id: string) => {
    dispatch({
      type: 'chat/resetMessageCountMap',
      payload: { groupId: id ?? '0' }
    })
  }
  const setSupervisionList = (payload: SupervisedCustomerType[]) => {
    dispatch({ type: 'chat/setSupervisionList', payload })
  }
  const setIsShowAddOrEditShortcutModal = (payload: boolean) => {
    dispatch({ type: 'chat/setIsShowAddOrEditShortcutModal', payload })
  }
  const setEditShortcutGroup = (payload: EditShortcutGroupTypes) => {
    dispatch({ type: 'chat/setEditShortcutGroupId', payload })
  }
  const setSelectedShortcut = (payload: string) => {
    dispatch({ type: 'chat/setSelectedShortcut', payload })
  }
  const setChatStatus = (payload: boolean) => {
    dispatch({ type: 'chat/setChatStatus', payload })
  }
  return {
    chatRoomInfo,
    chatMode,
    chatDetail,
    messageCountMap,
    supervisionList,
    isShowAddOrEditShortcutModal,
    editShortcutGroup,
    selectedShortcut,
    setGlobalChatMode,
    setChatInfo,
    setChatDetail,
    setMessageCountMap,
    resetMessageCountMap,
    setSupervisionList,
    setIsShowAddOrEditShortcutModal,
    setEditShortcutGroup,
    setSelectedShortcut,
    setChatStatus
  }
}
