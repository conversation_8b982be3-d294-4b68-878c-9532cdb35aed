import { useEffect, useState } from 'react'
interface FilterType {
  data: string
}
interface TableProps {
  //分页信息
  pagination: Record<string, any>
  //条件参数信息
  params: Record<string, any>
  //请求方法
  getTable: (params: any) => Promise<Record<string, any>>
  filterName?: FilterType
  isPolling?: boolean
}

export default function useTable(props: TableProps) {
  const { pagination, params, getTable, filterName, isPolling } = props
  const [loading, setLoading] = useState<boolean>(false)
  const [pageInfo, setPageInfo] = useState<Record<string, any>>({
    ...pagination
  })
  const [paramsInfo, setParamsInfo] = useState<Record<string, any>>({
    ...params
  })
  const [total, setTotal] = useState<number>(0)
  const [tableData, setTableData] = useState<Array<Record<string, any>>>([])
  const fetchTable = async (params: any, page: any, isFirst = true) => {
    isFirst && setLoading(true)
    try {
      const resData: any = await getTable({ ...params, ...page })
      setLoading(false)
      //为data添加别名，根据filterName的data属性
      const dataName: any = filterName ? filterName.data : 'data'
      const { [dataName]: data, total } = resData
      console.log('data', data)
      setTotal(total)
      setTableData(data)
    } catch (err) {
      setLoading(false)
    } finally {
      setLoading(false)
    }
  }
  useEffect(() => {
    fetchTable(paramsInfo, pageInfo)
    let timer = null
    if (isPolling) {
      timer = setInterval(() => {
        fetchTable(paramsInfo, pageInfo, false)
      }, 5000)
    }
    return () => {
      clearInterval(timer)
    }
  }, [])
  const setParamsFunc = (params: Record<string, any>) => {
    setParamsInfo((pre) => {
      const newParams = { ...pre, ...params }
      const newPage = { ...pageInfo, current: 1 }
      setPageInfo(newPage)
      fetchTable(newParams, newPage)
      return newParams
    })
  }
  const setPageFunc = (page: Record<string, any>) => {
    setPageInfo((pre) => {
      const newPage = { ...pre, ...page }
      fetchTable(paramsInfo, newPage)
      return newPage
    })
  }

  return {
    tableData,
    pageInfo,
    paramsInfo,
    setParamsFunc,
    setPageFunc,
    total,
    loading
  }
}
