import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'

export default function useLogin() {
  const ipcRenderer = window.ipcRenderer
  //react跳转路由
  const navigate = useNavigate()
  const [login, setLogin] = useState<boolean>(false)
  useEffect(() => {
    ipcRenderer.on('login_success', (_e, data) => {
      const { str } = data
      setLogin(true)
      navigate(`/preview?${str}`)
    })
    return () => {
      ipcRenderer.removeAllListeners('login_success')
    }
  }, [])
  return [login]
}
