import eventBus from '@renderer/utils/eventBus'
import { useEffect, useState } from 'react'

export default function useApplicationUpdate() {
  const ipcRenderer = window.ipcRenderer
  const [updateProgress, setUpdateProgress] = useState(0) // 下载进度
  const [updateStatus, setUpdateStatus] = useState<StatusType>('noUpdate') // 更新状态
  const [info, setInfo] = useState(null) // 更新信息

  //检测到新版本信息
  const updateInfo = (_e: any, info: any) => {
    console.log(info)
    setInfo(info)
    setUpdateStatus('hasUpdate')
    eventBus.emit('hasUpdate')
  }
  //确认更新
  const confirmUpdate = () => {
    ipcRenderer.send('confirmUpdate')
    setUpdateStatus('updateLoading')
  }
  //下载进度
  // const downLoadObserver = (_e: any, info: any) => {
  //   const { percent } = info
  //   setUpdateProgress(percent.toFixed(2))
  // }
  //下载完成
  const downloaded = (_e: any, info: any) => {
    console.log(info)
    localStorage.clear()
    setUpdateStatus('updateDownloaded')
  }
  //更新发生错误
  const updateErrorCallback = (_e: any, info: any) => {
    setUpdateStatus('updateError')
  }
  //重新下载
  const reDownload = () => {
    ipcRenderer.send('confirmUpdate')
    setUpdateStatus('updateLoading')
  }
  //获取当前版本
  const getCurrentVersion = (e, info: any) => {
    localStorage.setItem('currentVersion', info)
  }
  //更新完成

  //打印更新日志
  const handleLog = (_e: any, info: any) => {
    console.log(_e)
    console.log(info)
  }
  useEffect(() => {
    //检查到新版本
    ipcRenderer.on('updateAvailable', updateInfo)
    // ipcRenderer.on('downloadProgress', downLoadObserver)
    ipcRenderer.on('updateDownloaded', downloaded)
    ipcRenderer.on('updateError', updateErrorCallback)
    ipcRenderer.on('printUpdaterMessage', handleLog)
    ipcRenderer.on('currentVersion', getCurrentVersion)
    return () => {
      ipcRenderer.removeListener('updateAvailable', updateInfo)
      ipcRenderer.removeListener('updateDownloaded', downloaded)
      // ipcRenderer.removeListener('downloadProgress', downLoadObserver)
      ipcRenderer.removeListener('updateError', updateErrorCallback)
      ipcRenderer.removeListener('printUpdaterMessage', handleLog)
      ipcRenderer.removeListener('currentVersion', getCurrentVersion)
    }
  }, [])
  return {
    updateProgress,
    info,
    reDownload,
    updateStatus,
    confirmUpdate
  }
}
