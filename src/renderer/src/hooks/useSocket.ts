import { useEffect, useRef } from 'react'
import Socket from '@/utils/socket'
import { useDispatch, useSelector } from 'react-redux'
import { setCurrentReceiveMsg } from '@/store/modules/message'
import { RootState } from '@/store'
import { parseJwt } from '@/utils/util'
import { setCustomerInfo } from '@/store/modules/login'
import { setGlobalAlertStatus } from '@renderer/store/modules/common'
import { useLocation } from 'react-router-dom'
import { setMessageCountMap } from '@renderer/store/modules/chat'
import eventBus from '@renderer/utils/eventBus'

type SocketHooks = {
  socketInstance: React.MutableRefObject<Socket>
}
export default function useSocket(): SocketHooks {
  const socketInstance = useRef<Socket>(null) //全局socket实例
  const dispatch = useDispatch()
  const location = useLocation()
  const currentPath = useRef(location.pathname)
  const userInfo = useSelector((state: RootState) => state.login.userInfo)
  useEffect(() => {
    currentPath.current = location.pathname
  }, [location.pathname])
  useEffect(() => {
    //实例化socket
    if (!userInfo?.token) return
    //如果localsotrage中有token，直接解析token，存入客服信息
    if (localStorage.getItem('live_chat_token')) {
      const token = localStorage.getItem('live_chat_token')
      dispatch(setCustomerInfo(parseJwt(token as string)))
    }
    console.log('-----实例化socket')
    const socket = Socket.getInstance({
      url: import.meta.env.RENDERER_VITE_SOCKET_URL as string,
      requestHeader: {
        token: userInfo.token.replace('Bearer ', '')
      },
      params: {
        appId: 3,
        language: 1,
        timezone: 'Asia/Shanghai',
        licenseId: '111293',
        isoCode: 'US'
      }
    })
    socketInstance.current = socket
    socket.on('message', handleOnMessage)
    socket.on('reconnect', reconnectCallback)
    return () => {
      socket.close()
      socketInstance.current && socketInstance.current?.close()
    }
  }, [userInfo.token])

  //如果重连次数到达最大值，且socket状态不是连接状态，提示用户重连
  function reconnectCallback(reconnectCount: number) {
    const status = socketInstance.current.getStatus()
    console.log('------', reconnectCount)
    if (reconnectCount >= 9 && !['已连接'].includes(status)) {
      //提示用户
      dispatch(setGlobalAlertStatus(true))
    }
  }
  const handleOnMessage = (data: ItemType) => {
    if (data.messageType === 15) {
      //解析token，存入客服信息
      dispatch(setCustomerInfo(parseJwt(data.token as string)))
    }
    dispatch(setCurrentReceiveMsg(data))
    eventBus.emit('receiveMsg', data)
    // 设置全局未读消息
    setMessageCount(data)
    if ([32, 68].includes(data.messageType)) {
      const messageObj = {
        title: 'New Message!',
        body: 'You have a new message!',
        // senderName: 'Customer',
        content: 'New Message!',
        silent: false, // 播放通知声音
        subtitle: 'Customer Message' // macOS 特有字段
      }
      window.ipcRenderer.send('receive-message', messageObj)
    }
  }
  const setMessageCount = (msg: MessageItemProps) => {
    if (!currentPath.current.includes('chats') && msg.messageType === 32) {
      dispatch(setMessageCountMap(msg))
    }
  }
  return { socketInstance }
}
