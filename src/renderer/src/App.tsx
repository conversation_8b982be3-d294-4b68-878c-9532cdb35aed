import { useRoutes } from 'react-router-dom'
import useSynRouter from './hooks/useSynRouter'
import styles from './style/app.module.scss'
import 'antd/dist/reset.css'
import { Spin, message } from 'antd'
import { useSelector } from 'react-redux'
import { useEffect, useMemo, useState } from 'react'
import useApplicationUpdate from './hooks/useApplicationUpdate'
import { GlobalContext } from './context/globalContext'
import useSocket from '@/hooks/useSocket'
import UpdateVersion from './component/UpdateVersion'
import { RootState } from './store'
import GlobalAlert from './component/GlobalAlert'
import eventBus from './utils/eventBus'

function App() {
  const [messageApi, contextHolder] = message.useMessage()
  const routes = useSynRouter()
  const { updateProgress, info, reDownload, updateStatus, confirmUpdate } =
    useApplicationUpdate()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const { socketInstance } = useSocket()
  const loading = useSelector((state: RootState) => state.common.loading)
  const globalAlertStatus = useSelector(
    (state: RootState) => state.common.globalAlertStatus
  )
  //取消更新
  const handleCancel = () => {
    // cancelUpdate()
    setIsModalOpen(false)
  }
  const alertStatus = useMemo(() => {
    return globalAlertStatus
  }, [globalAlertStatus])

  const socketReconnect = () => {
    socketInstance.current.createWebSocket()
  }

  useEffect(() => {
    // 监听系统更新事件
    const updateHandler = () => {
      setIsModalOpen(true)
    }

    console.log('App 注册 hasUpdate 监听器');
    const unsubscribeUpdate = eventBus.on('hasUpdate', updateHandler)

    // 监听通知失败事件，使用应用内通知作为备份
    window.ipcRenderer.on('notification_failed', (_, notificationData) => {
      messageApi.info({
        content: notificationData.body,
        duration: 5,
        key: `notification-${notificationData.timestamp}`
      })
    })

    return () => {
      console.log('App 组件卸载，移除监听器');
      unsubscribeUpdate()
      window.ipcRenderer.removeAllListeners('notification_failed')
    }
  }, [messageApi])

  // useEffect(() => {
  //   console.log(updateStatus, 'updateStatus')
  //   if (updateStatus === 'hasUpdate') {
  //     setIsModalOpen(true)
  //   }
  // }, [updateStatus])
  return (
    <GlobalContext.Provider
      value={{
        socketInstance,
        messageApi,
        updateApp: {
          updateStatus,
          info
        }
      }}
    >
      {contextHolder}
      <div className={styles.app_container}>
        <Spin
          spinning={loading}
          delay={500}
          style={{ height: '100%', width: '100%' }}
        >
          {alertStatus && <GlobalAlert onRetry={socketReconnect} />}
          {useRoutes(routes)}
        </Spin>
      </div>
      <UpdateVersion
        visible={isModalOpen}
        version={info?.version}
        updateStatus={updateStatus}
        percent={updateProgress}
        confirmUpdate={confirmUpdate}
        cancelUpdate={handleCancel}
        reDownload={reDownload}
      />
    </GlobalContext.Provider>
  )
}

export default App
