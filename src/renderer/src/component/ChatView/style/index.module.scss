.chat_view_wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f9ff;
  position: relative;
  .chat_view_content {
    flex: 1;
    overflow-y: auto;
    position: relative;
    &:hover {
      &::-webkit-scrollbar-thumb {
        background: #eaecf3;
        border-radius: 4px;
      }
    }
    //修改滚动条样式
    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }
    &::-webkit-scrollbar-thumb {
      background: #f5f9ff;
      border-radius: 4px;
    }
    &::-webkit-scrollbar-track {
      background: #f5f9ff;
      border-radius: 4px;
    }
    .message_box {
      height: 100%;
      padding: 32px 20px 20px 20px;
      box-sizing: border-box;
    }
    .global_loading {
      position: absolute;
      top: 50%;
      left: 50%;
    }
  }
}
