import { useEffect, useRef, useImperativeHandle, memo } from 'react'
import MessageList from './component/MessageList'
import styles from './style/index.module.scss'
import { debounce } from 'lodash'
import { Spin } from 'antd'
interface ChatViewProps {
  headerBox?: React.ReactNode //聊天头部插槽
  footerBox?: React.ReactNode //聊天底部插槽
  messageData?: Array<MessageItemProps> //聊天数据
  loadMore?: (el: React.MutableRefObject<HTMLDivElement>) => void //滚动加载更多回调函数
  globalLoading?: boolean
  historyLoading?: boolean
  chatWrapRef?: React.MutableRefObject<unknown>
  children?: React.ReactNode
}
const ChatView = function ChatView(props: ChatViewProps) {
  const {
    headerBox,
    footerBox,
    messageData = [],
    loadMore,
    globalLoading,
    historyLoading,
    chatWrapRef,
    children
  } = props
  const chatViewRef = useRef<HTMLDivElement | undefined>(null)
  useImperativeHandle(chatWrapRef, () => ({
    scrollToBottom,
    chatViewRef,
    imgLoad
  }))
  //滚动到底部，用于新消息到来时
  const scrollToBottom = () => {
    if (chatViewRef.current) {
      chatViewRef.current.scrollTo({
        top: chatViewRef.current.scrollHeight,
        behavior: 'smooth'
      })
      //  chatViewRef.current.scrollTop = chatViewRef.current.scrollHeight
    }
  }
  const imgLoad = (type = 'init') => {
    if (document.readyState === 'complete') {
      const images = document.querySelectorAll('.ant-image-img')
      let imgList = []
      if (type === 'init') {
        imgList = Array.from(images) as HTMLImageElement[]
      } else {
        // 获取数组最后一个元素
        imgList = Array.from(images).slice(-1) as HTMLImageElement[]
      }
      const promise = imgList.map((item: HTMLImageElement) => {
        return new Promise((resolve) => {
          item.onload = () => {
            resolve(item)
          }
        })
      })
      Promise.all(promise).then(() => {
        scrollToBottom()
      })
    } else {
      Promise.reject('document not ready')
    }
  }
  //滚动加载更多
  const scrollLoadMore = debounce(() => {
    if (chatViewRef.current) {
      //判断是否滚动到顶部
      loadMore && loadMore(chatViewRef)
    }
  }, 500)
  useEffect(() => {
    const chatRef = chatViewRef.current
    if (chatRef && messageData.length > 0) {
      chatRef?.addEventListener('scroll', scrollLoadMore)
    }
    return () => {
      if (chatRef) {
        chatRef?.removeEventListener('scroll', scrollLoadMore)
      }
    }
  }, [messageData])
  return (
    <div className={styles.chat_view_wrapper}>
      {children}
      <div className={styles.chat_view_header}>{headerBox}</div>
      <div className={styles.chat_view_content} ref={chatViewRef}>
        {globalLoading && (
          <div className={styles.global_loading}>
            <Spin size="large"></Spin>
          </div>
        )}

        <MessageList
          messageList={messageData}
          historyLoading={historyLoading}
        ></MessageList>
      </div>
      <div className={styles.chat_view_footer}>{footerBox}</div>
    </div>
  )
}

export default memo(ChatView)
