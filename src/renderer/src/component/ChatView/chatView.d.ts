type FileTypes =
  | 'word'
  | 'excel'
  | 'pdf'
  | 'ppt'
  | 'image'
  | 'video'
  | 'txt'
  | 'text'

declare interface MessageItemProps extends ItemType {
  code?: number
  clientMessageId?: string
  ackMessageId?: string
  fileType?: FileTypes
  isRead?: boolean
  isSelf?: boolean
  sendStatus?: number
  retryCount?: number
  score?: number
  comments?: string
  fileInfo?: FileInfoType
  pre?: number
  customerServiceType?: number
  isChatting?: boolean
  requestType?: number
  customerName?: string
  email?: string
  mobile?: string
  fullTime?: string
  url?: string
  ticketId?: number
  memberList?: any
  isTakeOver?: boolean
  isSuccess?: boolean
  effectiveTime?: number
}
//
// sendStatus: 0, //0:发送中 1:发送成功 2:发送失败
declare interface MsgSendParams {
  msg?: string
  groupId: string
  clientMessageId: string
  fileInfo?: FileInfoType
}

declare type FileInfoType = {
  fileName: string
  fileUrl: string
  fileExt: string
  fileSize: string
  formatName?: string
}
