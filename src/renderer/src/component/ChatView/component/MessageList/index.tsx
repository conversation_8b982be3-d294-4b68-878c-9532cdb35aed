import MessageItem from '../MessageItem'
import styles from '../../style/index.module.scss'
import { Spin } from 'antd'
import { memo } from 'react'
interface MessageListProps {
  messageList: Array<MessageItemProps>
  historyLoading?: boolean
}
export default memo(function MessageList(props: MessageListProps) {
  const { messageList, historyLoading } = props

  return (
    <div className={styles.message_box}>
      {historyLoading && (
        <div style={{ textAlign: 'center' }}>
          <Spin></Spin>
        </div>
      )}
      {messageList.map((item, index) => {
        return <MessageItem key={index} itemData={item}></MessageItem>
      })}
    </div>
  )
})
