.message_item_wrapper {
  padding-bottom: 40px;
  box-sizing: border-box;
  .message_sys_wrap {
    display: flex;
    justify-content: center;
    .message_sys_text {
      @include font12;
      color: #89898c;
      text-align: center;
      max-width: 100%;
    }
    .message_sys_privacy {
      @include font12;
      color: #89898c;
      text-align: center;
      width: 470px;
    }
    .message_sys_wrap {
      display: flex;
      flex-direction: column;
      .message_admin_time {
        text-align: center;
        color: #999999;
        @include font12;
        margin-bottom: 8px;
      }
      .message_admin_msg_box {
        display: flex;
        align-items: center;
        max-width: 470px;
        width: auto;
        background: #e6f7ff;
        padding: 3px;
        min-height: 26px;
        border-radius: 24px;
        margin: 0 auto;
        position: relative;
        padding: 8px 12px;
        span {
          display: inline-block;
          width: 18px;
          height: 18px;
          font-size: 18px;
          color: #13c2c2;
        }
        .message_admin_msg {
          @include font12;
          color: #434343;
          text-align: left;
          margin-left: 8px;
        }
      }
    }
  }
  .feedback_wrap {
    width: 272px;
    margin: 0 auto;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(195, 195, 206, 0.24);
    border-radius: 3px;
    border: 1px solid #f2f2f2;
    padding: 20px 16px;
    .rate_wrap {
      display: flex;
      padding-bottom: 8px;
      align-items: center;
      border-bottom: 1px solid #f2f2f2;
      .label {
        @include font14;
        color: #a8abb2;
        margin-right: 12px;
      }
      i {
        &.good {
          color: #10a300;
        }
        &.bad {
          color: #ff4d4f;
        }
      }
    }
    .comment_wrap {
      padding-top: 8px;
      .label {
        display: block;
        @include font14;
        color: #a8abb2;
        margin-bottom: 8px;
      }
      .content {
        min-height: 70px;
        border: 1px solid #e5e5e5;
        border-radius: 3px;
        padding: 10px 12px;
        background-color: #f7f7f7;
        word-break: break-all;
        @include font13;
      }
    }
    .input_wrap {
      margin-bottom: 12px;
      width: 230px;
      &:last-of-type {
        margin-bottom: 0;
      }
      .label {
        display: inline-block;
        margin-bottom: 4px;
      }
    }
  }
  .sg_message_info {
    display: flex;
    flex-direction: column;
    align-items: center;
    .message_chat {
      @include font12;
      color: #89898c;
      text-align: center;
      max-width: 100%;
    }
    .top {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .cs_avator {
        display: inline-block;
        width: 48px;
        height: 48px;
        flex-shrink: 0;
        background: #f5f5f9;
        border: 1.5px solid #eeeeee;
        border-radius: 36px;
        background-repeat: no-repeat;
        background-size: contain;
        background-position: contain;
        position: relative;
      }
      .cs_online_dot {
        position: absolute;
        right: 3px;
        bottom: 0;
        width: 9px;
        height: 9px;
        border-radius: 50%;
        background-color: #10a300;
        border: 1.5px solid #eeeeee;
      }
      .cs_name {
        @include font12;
        color: #19191a;
        margin-top: 4px;
        font-weight: 600;
      }
    }
  }
  .email_wrap {
    text-align: end;
    margin-right: 58px;
    margin-top: 40px;
    .email_input {
      display: inline-flex;
      flex-direction: column;
      width: 300px;
      font-size: 14px;
      padding: 12px 20px;
      color: #3d3d3d;
      border-radius: 12px 0px 12px 12px;
      background-color: #e4edfa;
      .email_input_label {
        text-align: left;
        margin-bottom: 4px;
      }
    }
  }
  .ticket_btn_wrap {
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    background-color: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(195, 195, 206, 0.24);
    border-radius: 3px;
    border: 1px solid #f2f2f2;
    padding: 20px 16px;
  }
  .message_item_content {
    display: flex;
    align-items: center;
    .message_item_avatar {
      margin-right: 16px;
    }
    .message_item_info {
      .img_warp {
        max-width: 300px;
      }
      .video_wrap {
        max-width: 450px;
        max-height: 280px;
      }
      .message_item_name_time {
        display: flex;
        justify-content: flex-end;
        &.left {
          justify-content: flex-start;
        }
        .message_item_time {
          color: #a8abb2;
          font-size: 12px;
        }
        .message_item_name {
          color: #a8abb2;
          font-size: 12px;
          margin-right: 4px;
        }
        margin-bottom: 6px;
      }
      .message_body_file {
        position: relative;
        width: 395px;
        height: 70px;
        padding: 12px 20px 16px 20px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-radius: 4px;
        background-color: #fff;
        border: 1px solid #d9d9d9;
        .file_left {
          display: flex;
          align-items: center;
          .file_info {
            margin-left: 12px;
            .file_name {
              font-size: 14px;
              color: #262626;
              margin-bottom: 8px;
            }
            .file_size {
              font-size: 12px;
              color: #8c8c8c;
            }
          }
          i {
            font-size: 36px;
            color: #4e80ff;
          }
        }
        .file_right {
          cursor: pointer;
          i {
            color: #8c8c8c;
            font-size: 14px;
          }
        }
        .upload_progress {
          position: absolute;
          width: 100%;
          left: 0;
          bottom: 0;
          display: flex;
          :global {
            .ant-progress-line {
              margin-bottom: 0;
              margin-inline-end: 0;
            }
            .ant-progress-outer {
              display: flex;
            }
            .ant-progress-inner {
              border-radius: 0px 0px 4px 4px;
            }
          }
        }
      }
      .message_item_message {
        @media screen and (max-width: 1680px) {
          max-width: 500px;
        }
        @media screen and (max-width: 1920px) and (min-width: 1681px) {
          max-width: 760px;
        }
        font-size: 14px;
        padding: 12px 20px;
        box-sizing: border-box;
        white-space: pre-line;
        word-wrap: break-word;
        // width: fit-content;
      }
      .message_self {
        color: #3d3d3d;
        border-radius: 0px 12px 12px 12px;
        background-color: #fff;
      }
    }
  }
  //客服消息
  .message_counterpart {
    flex-direction: row-reverse;
    .message_item_avatar {
      margin: 0;
      margin-left: 16px;
    }
    .message_item_info {
      display: flex;
      flex-direction: column;
      .message_self {
        align-self: flex-end;
        width: fit-content;
        // display: inline-block;
        color: #434343;
        border-radius: 12px 0px 12px 12px;
        background-color: #e4edfa;
      }
      .message_supervised {
        background-color: #6c7479;
        color: #fff;
      }
    }
    .message_read_status {
      margin-right: 12px;
      align-self: flex-end;
      color: #4ed142;
      i {
        font-size: 18px;
      }
      .message_read_status_unread {
        width: 18px;
        height: 18px;
        border-radius: 50%;
        border: 1px solid #a7a8ab;
      }
    }
  }
}
:global {
  .toolbar-wrapper {
    position: fixed;
    bottom: 32px;
    left: 50%;
    padding: 0px 24px;
    color: #fff;
    font-size: 20px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 100px;
    transform: translateX(-50%);
  }
  .toolbar-wrapper .anticon {
    padding: 12px;
    cursor: pointer;
  }

  .toolbar-wrapper .anticon[disabled] {
    cursor: not-allowed;
    opacity: 0.3;
  }

  .toolbar-wrapper .anticon:hover {
    opacity: 0.3;
  }
}
