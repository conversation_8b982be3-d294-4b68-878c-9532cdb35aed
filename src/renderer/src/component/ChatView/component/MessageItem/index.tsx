import FsAvatar from '@/component/FsAvatar'
import MessageBody from '../MessageBody'
import styles from './index.module.scss'
import { Button, Input, Tooltip, message } from 'antd'
import { RootState } from '@/store'
import { useSelector } from 'react-redux'
import { updateTicketStatus } from '@renderer/api/chat'
import { useEffect, useState } from 'react'
import eventBus from '@renderer/utils/eventBus'
interface ItemProps {
  itemData: MessageItemProps
}
export type TicketBtnProps = {
  name: string
  type: number
}
const ticketTemplate =
  '{"clickable":[{"name":"Synchronize it to the ERP","type":5},{"name":"Later processing","type":1},{"name":"Mark it as spam","type":4}],"isClick":true,"ticketId":"21","unClickable":{"name":"Chat details have been synchronized to ERP","type":1}}'
export default function MessageItem(props: ItemProps) {
  const { itemData } = props
  const [ticketBtn, setTicketBtn] = useState(ticketTemplate)

  useEffect(() => {
    itemData.messageType === 67 && itemData.msg && setTicketBtn(itemData.msg)
  }, [itemData.messageType])

  const [ticketBtnLoading, setTicketBtnLoading] = useState(false)
  const chatDetail = useSelector((state: RootState) => state.chat.chatDetail)
  const customerInfo = useSelector(
    (state: RootState) => state.login.customerInfo
  )
  const handleRequestTypeTemplate = (data: MessageItemProps) => {
    if (data.requestType === 64) {
      return (
        <div className={styles.feedback_wrap}>
          <div className={styles.input_wrap}>
            <span className={styles.label}>Name</span>
            <Input
              disabled
              style={{ width: '230px' }}
              value={data?.customerName}
            ></Input>
          </div>
          <div className={styles.input_wrap}>
            <span className={styles.label}>Mobile</span>
            <Input
              disabled
              style={{ width: '230px' }}
              value={data?.mobile}
            ></Input>
          </div>
          <div className={styles.input_wrap}>
            <span className={styles.label}>Email</span>
            <Input
              disabled
              style={{ width: '230px' }}
              value={data?.email}
            ></Input>
          </div>
        </div>
      )
    } else if (data.requestType === 54) {
      return (
        <div className={styles.feedback_wrap}>
          <div className={styles.input_wrap}>
            <span className={styles.label}>Name</span>
            <Input
              disabled
              style={{ width: '230px' }}
              value={data?.customerName}
            ></Input>
          </div>
          {data?.mobile && (
            <div className={styles.input_wrap}>
              <span className={styles.label}>Mobile</span>
              <Input
                disabled
                style={{ width: '230px' }}
                value={data?.mobile}
              ></Input>
            </div>
          )}
          {data?.email && (
            <div className={styles.input_wrap}>
              <span className={styles.label}>Email</span>
              <Input
                disabled
                style={{ width: '230px' }}
                value={data?.email}
              ></Input>
            </div>
          )}
          <div className={styles.input_wrap}>
            <span className={styles.label}>Comments</span>
            <Input.TextArea
              disabled
              value={data?.msg}
              style={{ width: '230px' }}
              autoSize={{ minRows: 2, maxRows: 6 }}
            />
          </div>
        </div>
      )
    } else {
      return null
    }
  }
  const statusTemplate = (data: MessageItemProps) => {
    switch (data.sendStatus) {
      case 0:
        return <div className={styles.message_read_status_unread}></div>
      case 1:
        if (data.messageReadStatus) {
          return <i className="iconfont icon-yidu" />
        } else {
          return <div className={styles.message_read_status_unread}></div>
        }
      case 2:
        return (
          <i
            className="iconfont icon-tishi-jingshi"
            style={{ color: '#F74A44' }}
          />
        )
      default:
        return null
    }
  }
  const rightTemplate = (data: MessageItemProps) => {
    //2, 21客服消息，33, 38, 46, 47, 49, 54系统消息，展示在右边
    return (
      [2, 21, 33, 38, 46, 47, 49, 54].includes(data.messageType) && (
        <>
          <div
            className={[
              styles.message_item_content,
              styles.message_counterpart
            ].join(' ')}
          >
            <div className={styles.message_item_avatar}>
              <FsAvatar src={itemData.avatar} />
            </div>
            <div className={styles.message_item_info}>
              <div className={styles.message_item_name_time}>
                <div className={styles.message_item_name}>{itemData.name}</div>
                <Tooltip title={itemData.fullTime}>
                  <div className={styles.message_item_time}>
                    {itemData.messageTime}
                  </div>
                </Tooltip>
              </div>
              <MessageBody data={itemData} />
            </div>
            {/* 自己的消息，可查看消息读取状态 */}
            <div className={styles.message_read_status}>
              {/* 0发送中，1发送成功，2发送失败 */}
              {statusTemplate(itemData)}
            </div>
            <div style={{ flex: 1 }}></div>
          </div>
        </>
      )
    )
  }
  const leftTemplate = (data: MessageItemProps) => {
    // 32客户消息，展示在左边
    return (
      [32].includes(data.messageType) && (
        <div className={styles.message_item_content}>
          <div className={styles.message_item_avatar}>
            <FsAvatar
              name={itemData.name || itemData.fromUserId}
              backgroundColor={chatDetail.colour}
            />
          </div>
          <div className={styles.message_item_info}>
            <div className={`${styles.message_item_name_time} ${styles.left}`}>
              <div className={styles.message_item_name}>
                {itemData.name || 'ID' + itemData.fromUserId}
              </div>
              <Tooltip title={itemData.fullTime}>
                <div className={styles.message_item_time}>
                  {itemData.messageTime}
                </div>
              </Tooltip>
            </div>
            <MessageBody data={itemData} />
          </div>
          <div style={{ flex: 1 }}></div>
        </div>
      )
    )
  }
  const systemTemplate = (data: MessageItemProps) => {
    //其他类型消息
    return (
      [3, 20, 26, 30, 37, 43, 62, 64, 83, 90].includes(data.messageType) && (
        <div className={styles.message_sys_wrap}>{systemContent(data)}</div>
      )
    )
  }
  const systemContent = (data: MessageItemProps) => {
    switch (data.messageType) {
      case 3:
        return (
          <p className={styles.message_sys_text}>
            {data.msg + ' ' + data.messageTime}
          </p>
        )
      case 20:
        return (
          <p
            className={styles.message_sys_privacy}
            dangerouslySetInnerHTML={{ __html: data.msg }}
          ></p>
        )
      case 64:
        return (
          <div className={styles.message_sys_wrap}>
            <p className={styles.message_admin_time}>{data.messageTime}</p>
            <div className={styles.message_admin_msg_box}>
              <span className="iconfont icon-xiaoxitishi"></span>
              <p className={styles.message_admin_msg}>
                Successfully sent the retention form
              </p>
            </div>
          </div>
        )
      case 90:
          return (
            <div className={styles.message_sys_wrap}>
              {/* <p className={styles.message_admin_time}>{data.messageTime}</p> */}
              <div className={styles.message_admin_msg_box}>
                <span className="iconfont icon-xiaoxitishi"></span>
                <p className={styles.message_admin_msg}>
                  {`${data.msg} · ${data.messageTime}`}
                </p>
              </div>
            </div>
          )
      default:
        return (
          <div className={styles.message_sys_wrap}>
            <p className={styles.message_admin_time}>{data.messageTime}</p>
            <div className={styles.message_admin_msg_box}>
              <span className="iconfont icon-xiaoxitishi"></span>
              <p className={styles.message_admin_msg}>{data.msg}</p>
            </div>
          </div>
        )
    }
  }
  const othersTemplate = (data: MessageItemProps) => {
    switch (data.messageType) {
      case 48:
        return (
          <div className={styles.sg_message_info}>
            <div className={styles.top}>
              <span
                className={styles.cs_avator}
                style={{ backgroundImage: `url(${data.avatar})` }}
              >
                <div className={styles.cs_online_dot}></div>
              </span>
              <span className={styles.cs_name}>{data.customerServiceName}</span>
            </div>
          </div>
        )
      case 49:
        return (
          <div className={styles.email_wrap}>
            <div className={styles.email_input}>
              <span className={styles.email_input_label}>email:</span>
              <Input disabled style={{ width: '260px' }}></Input>
            </div>
          </div>
        )
      case 42:
        return (
          <div className={styles.feedback_wrap}>
            <div className={styles.rate_wrap}>
              <span className={styles.label}>Rate:</span>
              {data.score === 1 ? (
                <i className={`${styles.good} iconfont icon-a-rongqi19301`}></i>
              ) : (
                <i className={`${styles.bad} iconfont icon-a-rongqi1`}></i>
              )}
            </div>
            <div className={styles.comment_wrap}>
              <span className={styles.label}>Comment:</span>
              <div
                className={styles.content}
                dangerouslySetInnerHTML={{ __html: data.comments }}
              ></div>
            </div>
          </div>
        )
      case 51:
        return handleRequestTypeTemplate(data)
      case 67:
        return (
          <div className={styles.ticket_btn_wrap}>
            {JSON.parse(ticketBtn).isClick ? (
              JSON.parse(ticketBtn).clickable.map((item: TicketBtnProps) => (
                <div className={styles.btn_item} key={item.type}>
                  <Button
                    disabled={ticketBtnLoading}
                    type="primary"
                    ghost
                    onClick={() => changeTicket(item)}
                  >
                    {item.name}
                  </Button>
                </div>
              ))
            ) : (
              <div className={styles.btn_item}>
                <Button type="primary" ghost disabled>
                  {JSON.parse(ticketBtn).unClickable.name}
                </Button>
              </div>
            )}
          </div>
        )
    }
  }
  const changeTicket = async (item: TicketBtnProps) => {
    setTicketBtnLoading(true)
    const data: any = await updateTicketStatus({
      ticketStatus: item.type,
      id: `${itemData.ticketId}`,
      userId: `${customerInfo.userId}`
    })
    setTicketBtnLoading(false)
    if (data.result) {
      setTicketBtn(JSON.stringify(data.ticketButtonVO))
      eventBus.emit('updateTicketBtn', data.ticketButtonVO)
    } else {
      message.error(data.message)
    }
  }
  return (
    <div className={styles.message_item_wrapper}>
      {systemTemplate(itemData)}
      {leftTemplate(itemData)}
      {rightTemplate(itemData)}
      {othersTemplate(itemData)}
    </div>
  )
}
