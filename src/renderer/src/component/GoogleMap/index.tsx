import React, { useEffect, useRef, useState } from 'react'
import { Wrapper, Status } from '@googlemaps/react-wrapper'

interface Props {
  center: google.maps.LatLngLiteral //中心点
  zoom: number //缩放级别
}

interface MapProps extends Props {
  children?: React.ReactNode
}

export default React.memo(function GoogleMap(props: Props) {
  const { center, zoom } = props
  const render = (status: Status) => {
    if (status === Status.LOADING) return <h3>{status} ..</h3>
    if (status === Status.FAILURE) return <h3>{status} ...</h3>
    return null
  }

  const MyMapComponent = (props: MapProps) => {
    const { center, zoom, children } = props
    const ref = useRef<HTMLDivElement>(null)
    const [map, setMap] = React.useState<google.maps.Map>()
    useEffect(() => {
      if (ref.current && !map) {
        setMap(new window.google.maps.Map(ref.current, { center, zoom }))
      }
    }, [ref, map])
    return (
      <>
        <div ref={ref} id="map" style={{ height: '100%' }}></div>
        {React.Children.map(children, (child) => {
          if (React.isValidElement(child)) {
            // set the map prop on the child component
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            return React.cloneElement(child, { map })
          }
        })}
      </>
    )
  }

  // 谷歌地图中的标记点组件
  const Marker: React.FC<google.maps.MarkerOptions> = (options) => {
    const [marker, setMarker] = useState<google.maps.Marker>()
    useEffect(() => {
      if (!marker) {
        setMarker(new google.maps.Marker())
      }
      return () => {
        if (marker) {
          marker.setMap(null)
        }
      }
    }, [marker])

    useEffect(() => {
      if (marker) {
        marker.setOptions(options)
      }
    }, [marker, options])

    return null
  }

  return (
    <Wrapper apiKey={'AIzaSyBujsYdD5eTiKweM4sHAlMCPvZQO1MjxWg'} render={render}>
      <MyMapComponent center={center} zoom={zoom}>
        <Marker position={center} />
      </MyMapComponent>
    </Wrapper>
  )
})
