import { uploadFile } from '@/api/common'
import { Upload, UploadProps } from 'antd'
import { UploadRequestOption } from 'rc-upload/lib/interface'
type FsUploadProps = {
  uploadCallback?: (data: any) => void
} & UploadProps

function FsUpload(props: FsUploadProps) {
  const { uploadCallback, children, ...rest } = props
  const customRequest = (options: UploadRequestOption) => {
    const { file, onProgress, onError, onSuccess } = options
    const formData = new FormData()
    formData.append('file', file)
    uploadFile(formData, {
      onUploadProgress: (progressEvent: any) => {
        // 上传进度获取
        const percent = ((progressEvent.loaded / progressEvent.total) * 100) | 0 //上传进度百分比
        onProgress({ percent })
      }
    })
      .then((res) => {
        uploadCallback && uploadCallback(res)
        // 上传成功
        onSuccess(res)
      })
      .catch((err) => {
        // 上传失败
        onError(err, file)
      })
  }
  return (
    <>
      <Upload customRequest={customRequest} {...rest}>
        {children}
      </Upload>
    </>
  )
}

export default FsUpload
