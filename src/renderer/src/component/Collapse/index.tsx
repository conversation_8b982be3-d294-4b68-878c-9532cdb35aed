import { useEffect, useRef, useState } from 'react'
import styles from './index.module.scss'

type CollapseProps = {
  children: React.ReactNode
  open: boolean
}
function Collapse(props: CollapseProps) {
  const { children, open } = props
  const [initHeight, setInitHeight] = useState<number | null>(null)
  const collapseRef = useRef<HTMLDivElement>(null)
  useEffect(() => {
    if (collapseRef.current) {
      const height = collapseRef.current.scrollHeight
      setInitHeight(height)
    }
  }, [children])
  return (
    <div
      className={styles.collapse_wrap}
      style={{ height: open ? `${initHeight}px` : '0px' }}
      ref={collapseRef}
    >
      {children}
    </div>
  )
}

export default Collapse
