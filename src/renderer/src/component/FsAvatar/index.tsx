import styles from './index.module.scss'
// 在线:1,忙碌：2，离线：3
enum OnlineStatusType {
  online = 1,
  busy = 2,
  offline = 3
}
type FsAvatarProps = {
  name?: string //用户名称
  onlineStatus?: OnlineStatusType //在线状态
  src?: string //头像地址
  style?: React.CSSProperties //样式
  backgroundColor?: string //背景颜色
  //消息数量
  messageCount?: number
  customClassName?: string
  busy?: boolean
}

export default function FsAvatar(props: FsAvatarProps) {
  const {
    name,
    src,
    onlineStatus,
    style = {
      fontSize: 14
    },
    backgroundColor,
    messageCount,
    customClassName,
    ...other
  } = props
  const onlineStatusMap: Record<number, string> = {
    [OnlineStatusType.online]: 'online_status',
    [OnlineStatusType.busy]: 'busy_status',
    [OnlineStatusType.offline]: 'offline_status'
  }
  return (
    <div
      className={`${styles.fs_avatar_wrapper} ${customClassName || ''}`}
      style={style}
    >
      <div className={styles.avatar_box}>
        {name ? (
          <div className={styles.avatar_name} style={{ backgroundColor }}>
            {name?.slice(0, 2)}
          </div>
        ) : (
          <img src={src} alt="" />
        )}
        {onlineStatus !== undefined && (
          <div className={styles.status_box}>
            <div className={styles[onlineStatusMap[onlineStatus]]}></div>
          </div>
        )}
        {/* 消息数量 */}
        {messageCount !== undefined && messageCount > 0 && (
          <div className={styles.message_count}>
            {messageCount > 99 ? '99+' : messageCount}
          </div>
        )}
      </div>
    </div>
  )
}
