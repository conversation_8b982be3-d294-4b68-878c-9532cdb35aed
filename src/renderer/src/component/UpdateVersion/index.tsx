import { Button, Modal, Progress, Spin } from 'antd'
import './index.scss'
import { useMemo } from 'react'
interface UpdateVersionProps {
  version: string
  visible: boolean
  updateStatus?: StatusType
  percent?: number
  confirmUpdate?: () => void
  cancelUpdate?: () => void
  reDownload?: () => void
}
export default function UpdateVersion(props: UpdateVersionProps) {
  const {
    version = '1.0.0',
    visible,
    updateStatus = 'noUpdate',
    percent = 0,
    confirmUpdate,
    cancelUpdate,
    reDownload
  } = props
  const titleInfo = useMemo(() => {
    const titleObj: Record<string, any> = {
      noUpdate: {
        title: 'Application Update',
        icon: 'icon-huojian',
        color: '#4B7EFF'
      },
      cancelUpdate: {
        title: 'Application Update',
        icon: 'icon-huojian',
        color: '#4B7EFF'
      },
      hasUpdate: {
        title: 'Application Update',
        icon: 'icon-huojian',
        color: '#4B7EFF'
      },
      updateLoading: {
        title: 'Application Update',
        icon: 'icon-huojian',
        color: '#4B7EFF'
      },
      updateDownloaded: {
        title: 'Update Completed',
        icon: 'icon-Completed',
        color: '#48C43D'
      },
      updateError: {
        title: 'Update Failed',
        icon: 'icon-fail',
        color: '#FF4D4F'
      }
    }
    return titleObj
  }, [updateStatus])
  return (
    <>
      <Modal
        onCancel={cancelUpdate}
        centered
        open={visible}
        footer={null}
        maskClosable={false}
      >
        <div className="update_wrapper">
          <div className="update_title_box">
            <div className="update_icon">
              <i
                className={['iconfont', titleInfo[updateStatus].icon].join(' ')}
                style={{ color: titleInfo[updateStatus].color }}
              ></i>
            </div>
            <div className="update_title">{titleInfo[updateStatus].title}</div>
            <div className="update_version">version {version}</div>
          </div>
          <div className="update_content">
            {updateStatus === 'hasUpdate' && (
              <>
                <div className="update_content_tip">
                  Found a new version, do you want to update now?
                </div>
                <div className="update_content_btn_box">
                  <Button onClick={cancelUpdate}>Cancel</Button>
                  <Button type="primary" onClick={confirmUpdate}>
                    OK
                  </Button>
                </div>
              </>
            )}
            {updateStatus === 'updateLoading' && (
              <>
                {/* <Progress percent={percent} /> */}
                <Spin spinning></Spin>
                <div className="update_content_updating">Updating...</div>
              </>
            )}
            {updateStatus === 'updateDownloaded' && (
              <>
                <div className="update_content_tip">
                  The version has been updated and you can now experience the
                  latest version.
                </div>
              </>
            )}
            {updateStatus === 'updateError' && (
              <>
                <div className="update_content_tip">
                  Please check the network or re-update.
                </div>
                <Button type="primary" onClick={reDownload}>
                  Retry
                </Button>
              </>
            )}
          </div>
        </div>
      </Modal>
    </>
  )
}
