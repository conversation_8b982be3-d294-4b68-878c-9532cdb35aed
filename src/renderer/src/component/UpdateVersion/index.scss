$fontColor: #262626;
.update_wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  .update_title_box {
    .update_icon {
      text-align: center;
      line-height: 48px;
      i {
        font-size: 48px;
        color: #4b7eff;
      }
    }
    .update_title {
      font-size: 16px;
      color: $fontColor;
      margin-bottom: 8px;
      font-weight: 500;
      line-height: 24px;
    }
    .update_version {
      display: flex;
      justify-content: center;
      padding: 4px 8px;
      background: rgba(134, 154, 185, 0.05);
      box-sizing: border-box;
      border: 1px solid rgba(134, 154, 185, 0.3);
      color: #869ab9;
      @include font12;
    }
  }
  .update_content {
    width: 352px;
    margin: 16px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    .ant-progress {
      width: 220px;
    }
    .update_content_tip {
      margin-bottom: 16px;
      text-align: center;
      font-size: 14px;
      line-height: 22px;
      text-align: center;
      color: $fontColor;
    }
    .update_content_updating {
      text-align: center;
      font-size: 14px;
      font-weight: normal;
      line-height: 22px;
      text-align: center;
      color: #8c8c8c;
    }
    .update_content_btn_box {
      width: 50%;
      display: flex;
      justify-content: space-around;
    }
  }
}
