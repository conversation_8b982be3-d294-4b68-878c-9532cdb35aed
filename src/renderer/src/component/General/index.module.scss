.general {
  padding: 0 20px 28px;
  width: 100%;
  border-bottom: 1px solid #e4e7ed;
  .title {
    font-size: 18px;
    line-height: 26px;
    font-weight: 500;
    color: #262626;
    margin-bottom: 20px;
  }
  .info {
    .user {
      display: flex;
      align-items: center;
      margin-bottom: 5px;
      .avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        border: 2px solid #fff;
        color: #fff;
        font-size: 22px;
        line-height: 44px;
        text-align: center;
        margin-right: 12px;
      }
      .content {
        .name {
          font-size: 16px;
          line-height: 24px;
          color: #262626;
          margin-right: 8px;
        }
        .email {
          font-size: 16px;
          line-height: 24px;
          color: #595959;
        }
      }
      .edit_box {
        margin-left: 8px;
        color: #4e80f4;
        cursor: pointer;
      }
    }
    .apply_btn {
      margin: 4px 0;
    }
    .address {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      .time,
      .city {
        font-size: 12px;
        line-height: 18px;
        display: flex;
        color: #8c8c8c;
        gap: 4px;
      }
      .line {
        width: 1px;
        height: 12px;
        background: #e4e7ed;
      }
    }
    .map {
      width: 100%;
      height: 146px;
    }
  }
}
.form {
  padding: 16px 0;
  .form_item {
    display: flex;
    flex-direction: column;
    gap: 4px;
    &:first-child {
      margin-bottom: 16px;
    }
  }
}
