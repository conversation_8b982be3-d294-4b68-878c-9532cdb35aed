import { useState } from 'react'
import styles from './index.module.scss'
import { Button } from 'antd'
type GlobalAlertProps = {
  onRetry: () => void
}
function GlobalAlert(props: GlobalAlertProps) {
  const { onRetry } = props
  const [loading, setLoading] = useState(false)
  const retryClick = () => {
    setLoading(true)
    onRetry()
    setTimeout(() => {
      setLoading(false)
    }, 1500)
  }
  return (
    <div className={styles.global_alert}>
      <div className={styles.global_alert_wrap}>
        <span
          className={`${styles.alert_icon} iconfont icon-tishi-cuowu`}
        ></span>
        <p className={styles.alert_content}>
          Connection has been disconnected or unauthentication, please try again
          later
        </p>
        <Button
          type="primary"
          size="small"
          ghost
          onClick={retryClick}
          loading={loading}
        >
          Retry
        </Button>
      </div>
    </div>
  )
}

export default GlobalAlert
