import { Modal, Select, Spin, message } from 'antd'
import styles from './index.module.scss'
import { useEffect, useState } from 'react'
import { createBlacklist, getBlacklistExpireTimes } from '@/api/blacklist'
interface BlacklistProps {
  customerId: number
  ipAddress: string
  openAddBlackListModal: boolean
  setOpenAddBlackListModal: (value: boolean) => void
  successCallback?: () => void
}
interface BlackItem {
  name: string
  id: number
}
//空数组或者BlackItem类型的数组
type BlackList = Array<BlackItem | null>
// 加入黑名单组件
function AddBlacklist(props: BlacklistProps) {
  const {
    openAddBlackListModal,
    setOpenAddBlackListModal,
    customerId,
    ipAddress,
    successCallback
  } = props
  const [loading, setLoading] = useState<boolean>(false)
  const [blackList, setBlackList] = useState<BlackList>([])
  const [blackExpireId, setBlackExpireId] = useState<number>(2)

  const onAddBlackList = async () => {
    //添加黑名单
    if (!blackExpireId) {
      message.error('Please select a blacklist cycle')
      return
    }
    setLoading(true)
    try {
      console.log(blackExpireId)
      console.log(customerId)
      await createBlacklist({
        userId: customerId,
        ipAddress,
        expireType: blackExpireId
      })
      setBlackExpireId(2)
      setLoading(false)
      message.success('Add blacklist successfully')
      successCallback && successCallback()
    } catch (error: any) {
      console.log(error)
      setLoading(false)
      throw new Error(error)
    } finally {
      setLoading(false)
    }
    setOpenAddBlackListModal(false)
  }
  const handleClose = () => {
    setOpenAddBlackListModal(false)
  }
  const blackItemChange = (value: any) => {
    setBlackExpireId(value)
  }
  useEffect(() => {
    //获取黑名单过期时间列表
    console.log(openAddBlackListModal)
    if (!openAddBlackListModal || blackList.length > 0) return
    setLoading(true)
    getBlacklistExpireTimes()
      .then((res: any) => {
        console.log(res)
        setBlackList(res)
      })
      .catch((error: any) => {
        throw new Error(error)
      })
      .finally(() => {
        setLoading(false)
      })
  }, [openAddBlackListModal])
  return (
    <>
      <Modal
        title="Add Blacklist"
        wrapClassName={styles.add_blacklist_modal}
        open={openAddBlackListModal}
        onOk={onAddBlackList}
        onCancel={handleClose}
        okButtonProps={{ disabled: loading }}
        okText="Confirm"
        cancelText="Cancel"
        width={480}
        centered
      >
        <Spin spinning={loading} delay={500}>
          <div>
            <p className={styles.black_label}>Select blacklist cycle</p>
            <Select
              style={{ width: '100%' }}
              placeholder="Select a blacklist cycle"
              optionFilterProp="children"
              onChange={blackItemChange}
              options={blackList}
              loading={loading}
              value={blackExpireId}
              fieldNames={{ label: 'name', value: 'id' }}
            />
            <p className={styles.black_descriptor}>
              Customers with blacklisted IP addresses will not be assigned
              customer service again.
            </p>
          </div>
        </Spin>
      </Modal>
    </>
  )
}

export default AddBlacklist
