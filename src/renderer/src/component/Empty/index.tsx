//自定义空状态组件
import styles from './index.module.scss'
import empty from '@/assets/image/empty.svg'
import chatEmpty from '@/assets/image/chatEmpty.svg'
interface EmptyProps {
  style?: React.CSSProperties
  type?: string
  isShowText?: boolean
  wrapperClassName?: string
}
export default function Empty(props: EmptyProps) {
  const { style, type, isShowText, wrapperClassName } = props
  return (
    <div className={[styles.empty_wrapper, wrapperClassName].join(' ')}>
      <img
        style={style}
        src={type === 'chatEmpty' ? chatEmpty : empty}
        alt=""
      />
      {isShowText && <p className={styles.desc}>There is nothing~</p>}
    </div>
  )
}
