//表情组件
import emoji from './emoji.json'
import styles from './index.module.scss'

type EmojiProps = {
  emojiClick: (emoji: string) => void
}
export default function FsEmoji(props: EmojiProps) {
  const { emojiClick } = props
  const emojiKeyList = Object.values(emoji)

  const handleEmojiClick = (emoji: string) => {
    emojiClick(emoji)
  }
  return (
    <div className={styles.picker_wrap}>
      <div className={styles.category}>
        <div className={styles.emojis_container}>
          {emojiKeyList.map((emoji, index) => {
            return (
              <span onClick={() => handleEmojiClick(emoji)} key={index}>
                {emoji}
              </span>
            )
          })}
        </div>
      </div>
    </div>
  )
}
