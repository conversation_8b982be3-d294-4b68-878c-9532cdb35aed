import { useEffect, useState } from 'react'
import { Button } from 'antd'
import { siteColorMap } from '@/utils/util'

import styles from './index.module.scss'
import AddBlacklist from '../AddBlacklist'

type PropsType = UserDetail & {
  [key: string]: any
}

export default function OtherInfo(props: PropsType) {
  const {
    userId,
    ipAddress,
    fromPage,
    isoCode,
    os,
    browser,
    isBlacklist,
    looking,
    isAdministrators
  } = props

  const [showAddBlackListModal, setShowAddBlackListModal] = useState(false)
  const [blackBtnStatus, setBlackBtnStatus] = useState(false)
  const setOpenAddBlackListModal = (show: boolean) => {
    setShowAddBlackListModal(show)
  }
  // 打开黑名单弹框
  const handleOpenBlackListModal = async () => {
    setShowAddBlackListModal(true)
  }
  const addBlackListSuccess = () => {
    setBlackBtnStatus(true)
  }

  useEffect(() => {
    setBlackBtnStatus(isBlacklist)
  }, [isBlacklist])
  return (
    <>
      <div className={styles.otherInfo}>
        <h2 className={styles.title}>Other Info</h2>
        <div className={styles.infoRow}>
          <p className={styles.infoRowTitle}>Came from:</p>
          <a
            className={styles.infoRowContent}
            href={fromPage}
            target="_blank"
            rel="noreferrer"
          >
            {fromPage}
          </a>
        </div>
        <div className={styles.infoRow}>
          <p className={styles.infoRowTitle}>Site source:</p>
          <span
            className={styles.infoRowSite}
            style={{
              backgroundColor:
                isoCode && siteColorMap[isoCode.toLocaleLowerCase()]
            }}
          >
            {isoCode && isoCode.toUpperCase()}
          </span>
        </div>
        <div className={styles.infoRow}>
          <p className={styles.infoRowTitle}>IP address:</p>
          <p className={styles.infoRowContent}>{ipAddress}</p>
          {isAdministrators &&
            (!blackBtnStatus ? (
              <Button
                size="small"
                className={styles.addBlackList}
                onClick={handleOpenBlackListModal}
              >
                Add BlackList
              </Button>
            ) : (
              <Button disabled type="primary" ghost size="small">
                Have been blacklisted
              </Button>
            ))}
        </div>
        <div className={`${styles.infoRow} ${styles.infoRowTwoContent}`}>
          <div className={styles.infoRowBox}>
            <p className={styles.infoRowTitle}>OS/Device:</p>
            <p className={styles.infoRowContent}>{os}</p>
          </div>
          <div className={styles.infoRowBox}>
            <p className={styles.infoRowTitle}>Browser:</p>
            <p className={styles.infoRowContent}>{browser}</p>
          </div>
        </div>
        {/* 正在浏览的页面 */}
        {looking && (
          <div className={styles.looking_wrap}>
            <div className={styles.title}>Looking through:</div>
            <div className={styles.content}>
              <a href={looking} target="_blank" rel="noreferrer">
                {looking}
              </a>
            </div>
          </div>
        )}
      </div>
      {/* 询问加入黑名单的天数弹框 */}
      <AddBlacklist
        customerId={userId}
        ipAddress={ipAddress}
        openAddBlackListModal={showAddBlackListModal}
        setOpenAddBlackListModal={setOpenAddBlackListModal}
        successCallback={addBlackListSuccess}
      ></AddBlacklist>
    </>
  )
}
