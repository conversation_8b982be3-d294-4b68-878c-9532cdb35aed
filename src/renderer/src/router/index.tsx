import { Spin } from 'antd'
import { lazy, ReactNode, Suspense } from 'react'
import { Navigate, RouteObject } from 'react-router-dom'
import iconUrl from '../assets/image/favicon.ico'

export type RouterProps = {
  path?: string
  name?: string
  icon?: React.ReactNode
  children?: RouterProps[]
  index?: boolean
  redirect?: string
  isMenu?: boolean
  component?: string
  meta?: {
    title?: string
    needLogin?: boolean
  }
}
const routes: RouterProps[] = [
  {
    path: '/',
    component: '/Layout',
    children: []
  },
  { path: 'preview', component: '/Preview' },
  { path: 'login', component: '/Login', meta: { title: 'Login' } },
  { path: 'notAuth', component: '/NotAuth' },
  { path: '404', component: '/NotFound' },
  { path: '*', component: '/NotFound' }
]

const lazyLoad = (children: ReactNode): ReactNode => {
  return (
    <Suspense fallback={<Spin spinning={true}></Spin>}>{children}</Suspense>
  )
}
//路由守卫
const RequireAuth = (props: { route: any; children: any }) => {
  if (props?.route?.meta?.title) {
    //设置网站图标
    document.title =
      import.meta.env.RENDERER_VITE_PATH === 'production'
        ? 'Live Chat-FS'
        : 'Live Chat-FS-Beta'
  }
  const { route } = props
  //白名单设置
  const whiteList = ['login', 'preview', '404', 'notAuth']
  const isLogin = localStorage.getItem('token')
  if (whiteList.includes(route.path)) {
    return <>{props.children}</>
  }
  if (!isLogin) {
    return <Navigate to="/login" />
  }
  return <>{props.children}</>
}

//懒加载处理
export const syncRouter = (routes: Array<RouterProps> = []): any => {
  if (routes.find((route) => route.path === 'labelSetting/:id')) {
    console.log(routes)
  }
  const mRouteTable: RouteObject[] = []
  const comps = import.meta.glob('../pages/**/*.tsx')
  routes?.forEach((route) => {
    const RouteComponent = lazy(
      comps[`../pages${route.component}/index.tsx`] as any
    )
    mRouteTable.push({
      path: route?.path,
      index: route?.index,
      element: lazyLoad(
        <RequireAuth route={route}>
          {route?.index ? (
            <Navigate to={route?.redirect} />
          ) : (
            <RouteComponent />
          )}
        </RequireAuth>
      ),
      children: route.children && syncRouter(route.children)
    })
  })
  return mRouteTable
}

export default () => syncRouter(routes)
