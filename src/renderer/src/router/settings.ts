import { RouterProps } from './index'

const settingsRoutes: Array<RouterProps> = [
  {
    path: 'labelSetting',
    name: 'labelSetting',
    component: '/Settings/LabelSetting'
    // index: true
  },
  {
    path: 'labelForm/:type',
    name: 'labelForm',
    component: '/Settings/LabelForm'
    // index: true
  },
  {
    path: 'blacklistSetting',
    name: 'blacklistSetting',
    component: '/Settings/BlacklistSetting'
  }
]

export default settingsRoutes
