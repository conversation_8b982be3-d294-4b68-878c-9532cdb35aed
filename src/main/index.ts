import { app, shell, BrowserWindow, ipcMain } from 'electron'
import contextMenu from 'electron-context-menu'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?asset'
import {
  addUpdateApp,
  feishuLogin,
  loadReactExtension,
  setTray,
  systemMessage,
  registerWakeUpEvent,
  quitApp,
  downloadChatFile,
  spellCheck
} from './utils'
const Env = (import.meta as any).env
const mode = Env.MODE
const iconPath =
  mode === 'test'
    ? '../../resources/beta_icon.png'
    : '../../resources/favicon.ico'
process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true'
// console.log(join(__dirname))
// process.env.DIST_ELECTRON = join(__dirname, '../')
// process.env.DIST = join(process.env.DIST_ELECTRON, '../dist')
// process.env.PUBLIC = process.env.ELECTRON_RENDERER_URL
//   ? join(process.env.DIST_ELECTRON, '../resources')
//   : process.env.DIST
function createWindow(): void {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 700,
    show: false,
    // fullscreen: true,
    icon: join(__dirname, iconPath),
    autoHideMenuBar: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      // sandbox: false,
      contextIsolation: false,
      nodeIntegration: true,
      nodeIntegrationInWorker: true
    }
  })
  spellCheck(mainWindow)
  contextMenu({
    window: mainWindow,
    showCopyImage: false, // 隐藏复制图像选项
    showSaveVideo: true, // 保存视频选项
    showSearchWithGoogle: false, // 隐藏使用 Google 搜索选项
    showInspectElement: false, // 隐藏检查元素选项
    showSelectAll: false // 隐藏全选选项
  })
  // setContextMenu(mainWindow)
  setTray(mainWindow, app)
  downloadChatFile(mainWindow)
  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
    systemMessage(mainWindow)
    // addUpdateApp(mainWindow)
    //主动检查更新
    ipcMain.on('listenerCheckUpdate', () => {
      addUpdateApp(mainWindow)
    })
  })
  feishuLogin(mainWindow)
  quitApp()
  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })
  //windows系统下
  if (process.platform != 'darwin') {
    //注册系统唤醒事件
    registerWakeUpEvent()
  }
  // 窗口获取焦点时，检查更新
  mainWindow.on('focus', () => {
    console.log('focus')
    //开发环境下，不检查更新
    if (is.dev) return
    //如果被销毁，重新创建
    addUpdateApp(mainWindow)
  })
  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  console.log('----mode---', mode)

  if (
    ['development', 'test'].includes(mode) &&
    process.env['ELECTRON_RENDERER_URL']
  ) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
    loadReactExtension('/ReactDevTools')
    loadReactExtension('/ReduxDevTools')
    mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
  //保持一个实例
  const gotTheLock = app.requestSingleInstanceLock()
  if (!gotTheLock) {
    app.quit()
  } else {
    //windows系统下
    app.on('second-instance', (_event, argv, _workingDirectory) => {
      // Someone tried to run a second instance, we should focus our window.
      // argv: An array of the second instance"s (command line / deep linked) arguments
      // Usually argv.slice(1) is a good start point for additional/complex parsing
      if (mainWindow) {
        if (mainWindow.isMinimized()) mainWindow.restore()
        mainWindow.focus()
      }
    })
  }
  //监听缩放，保持宽高比
  // mainWindow.on('will-resize', (e, newBounds) => {
  //   mainWindow.setAspectRatio(1920 / 1080)
  // })
  //监听关闭
  mainWindow.on('close', (e) => {
    e.preventDefault()
    mainWindow.destroy()
    app.quit()
  })
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.fs.liveChat')

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})
// In this file you can include the rest of your app"s specific main process
// code. You can also put them in separate files and require them here.
