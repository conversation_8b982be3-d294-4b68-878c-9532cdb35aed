import {
  ipcMain,
  session,
  Notification,
  nativeImage,
  Tray,
  app,
  dialog,
  App,
  BrowserWindow,
  Menu,
  MenuItem
} from 'electron'
import { join } from 'node:path'
import { homedir } from 'node:os'
import AdmZip from 'adm-zip'
import fs from 'fs'
import axios from 'axios'
import { download } from 'electron-dl'
const Env = (import.meta as any).env
const config = {
  latestUrl: isMac()
    ? Env.MAIN_VITE_DEV_MAC_LAST_URL
    : Env.MAIN_VITE_DEV_WIN_LAST_URL,
  downloadUrl: isMac() ? Env.MAIN_VITE_DEV_MAC_URL : Env.MAIN_VITE_DEV_WIN_URL
}
//判断当前客户端是mac还是windows
export function isMac() {
  return process.platform === 'darwin'
}
//飞书登录
export function feishuLogin(win) {
  //拦截请求，完成飞书登录
  const filter = {
    urls: ['*://rng.fs.com/*']
  }
  session.defaultSession.webRequest.onBeforeSendHeaders(
    filter,
    (details, callback) => {
      const { url } = details
      const code = /code=([^&]*)/.exec(url) || null
      if (code) {
        const str = url.split('?')[1]
        win.webContents.send('login_success', {
          str
        })
      }
      callback({})
    }
  )
}
//系统消息提示
export function systemMessage(win: BrowserWindow) {
  // 为不同平台设置合适的图标
  const icon = join(__dirname, '../../resources/favicon.ico')
  const image = nativeImage.createFromPath(icon)

  // Windows 平台特定设置
  if (process.platform === 'win32') {
    // 确保应用ID设置正确，这对于Windows通知非常重要
    app.setAppUserModelId(
      app.isPackaged ? 'com.livechat.app' : process.execPath
    )
  }

  // 对每个通知请求创建新的通知实例
  ipcMain.on('receive-message', (_e, messageObj) => {
    const title = messageObj.senderName || messageObj.title || 'New Message!'
    const body = 'You have a new message!'

    // 为每个消息创建一个新的通知实例
    const messageNotification = new Notification({
      title,
      body,
      icon: image,
      // macOS 特有设置
      subtitle: isMac() ? 'Customer Message' : undefined,
      // 根据消息类型决定是否静音
      silent: messageObj.silent !== undefined ? messageObj.silent : false,
      // 确保通知显示在前台
      urgency: 'normal',
      hasReply: false
    })

    try {
      // 显示通知
      messageNotification.show()

      // 点击通知的事件处理
      messageNotification.once('click', () => {
        // 如果窗口最小化或不可见，恢复窗口
        if (win.isMinimized() || !win.isVisible()) {
          win.restore()
          win.focus()
        }

        // 通知渲染进程消息被点击
        win.webContents.send('message_click', messageObj)

        // 关闭通知
        messageNotification.close()
      })

      // 确保通知在一段时间后自动关闭，避免堆积
      setTimeout(() => {
        try {
          if (messageNotification) {
            messageNotification.close()
          }
        } catch (error) {
          console.error('Error closing notification:', error)
        }
      }, 10000) // 10秒后自动关闭
    } catch (error) {
      console.error('Error showing notification:', error)

      // 如果通知显示失败，尝试使用备用方式提醒用户
      if (win && !win.isDestroyed()) {
        win.webContents.send('notification_failed', {
          title,
          body,
          timestamp: new Date().getTime()
        })
      }
    }
  })
}
//设置系统托盘
export function setTray(win: BrowserWindow, app: App) {
  let count = 0
  const icon = isMac()
    ? join(__dirname, '../../resources/tray1.png')
    : join(__dirname, '../../resources/tray.png')
  console.log(icon)
  const contextMenu = Menu.buildFromTemplate([
    {
      label: 'Close',
      click: () => {
        app.quit()
      }
    }
  ])
  const image = nativeImage.createFromPath(icon)
  const tray = new Tray(image)
  tray.setContextMenu(contextMenu)
  let toggle = true
  let timer
  ipcMain.on('setTrayCount', (_e, totalCount) => {
    count = totalCount
    const badgeText = count > 0 ? count.toString() : ''
    console.log(badgeText)
    if (isMac()) {
      tray.setTitle(badgeText)
      app.setBadgeCount(count)
    } else {
      if (timer) {
        clearInterval(timer)
      }
      if (count === 0) return
      win.once('focus', () => win.flashFrame(false))
      win.flashFrame(true)
      timer = setInterval(() => {
        toggle = !toggle
        if (toggle) {
          tray.setImage(nativeImage.createEmpty())
        } else {
          tray.setImage(icon)
        }
      }, 600)
    }
  })
  tray.setToolTip('LiveChat')
  tray.on('click', () => {
    count = 0
    if (timer) {
      clearInterval(timer)
      tray.setImage(icon)
    }
    if (isMac() && tray.getTitle()) {
      //清除角标
      tray.setTitle('')
    }
    if (win.isVisible() || win.isMinimized()) {
      win.show()
    }
  })
  tray.on('right-click', () => {
    tray.popUpContextMenu()
  })
}
// 将日志在渲染进程里面打印出来
function printUpdaterMessage(arg, mainWindow) {
  const message = {
    error: '更新出错',
    checking: '正在检查更新',
    updateAvailable: '检测到新版本',
    downloadProgress: '下载中',
    updateNotAvailable: '无新版本'
  }
  mainWindow.webContents.send('printUpdaterMessage', message[arg] ?? arg)
}
// 比较版本号
function compareVersions(version1, version2) {
  const v1 = version1.split('.')
  const v2 = version2.split('.')

  for (let i = 0; i < v1.length; i++) {
    if (parseInt(v1[i]) < parseInt(v2[i])) {
      return -1
    } else if (parseInt(v1[i]) > parseInt(v2[i])) {
      return 1
    }
  }

  return 0
}
//检查latest.yml文件，判断是否有更新
export async function checkUpdate(mainWindow) {
  //获取当前版本号
  const oldVersion = app.getVersion()
  mainWindow.webContents.send('currentVersion', oldVersion)
  //获取latest.yml文件
  try {
    const res = await axios({
      url: config.latestUrl,
      method: 'get',
      params: {
        timestamp: new Date().getTime()
      }
    })
    const { data } = res
    const version = data.split('\n')[0].replace(/\s/g, '')
    const latestVersion = version.split(':')[1]
    console.log(latestVersion)
    console.log(oldVersion)
    mainWindow.webContents.send('printUpdaterMessage', latestVersion)
    mainWindow.webContents.send('printUpdaterMessage', oldVersion)
    //判断是否有更新
    if (compareVersions(latestVersion, oldVersion) > 0) {
      console.log('有更新')
      console.log(mainWindow)
      printUpdaterMessage('updateAvailable', mainWindow)
      //告知渲染进程有更新
      mainWindow.webContents.send('updateAvailable', {
        version: latestVersion,
        oldVersion
      })
      ipcMain.on('userUpdateApp', () => {
        console.log('userUpdateApp')
        mainWindow.webContents.send('updateAvailable', {
          version: latestVersion,
          oldVersion
        })
      })
    } else {
      printUpdaterMessage('updateNotAvailable', mainWindow)
    }
  } catch (error) {
    console.log(error)
    printUpdaterMessage('error', mainWindow)
  }
}
//增量更新
export async function addUpdateApp(mainWindow) {
  if (mainWindow.isDestroyed()) return
  console.log('addUpdateApp')
  //检查更新
  await checkUpdate(mainWindow)
  //收到确认更新提示，执行下载
  ipcMain.on('confirmUpdate', () => {
    //下载文件
    const resourcesPath = process.resourcesPath //获取资源路径
    downloadPre(mainWindow, resourcesPath, 'app.zip')
  })
}
const downloadPre = async (mainWindow, resourcesPath, name) => {
  try {
    await downloadFile(mainWindow, resourcesPath, name)
    console.log('下载完成')
    //解压文件
    const tempPath = join(resourcesPath, name)
    console.log(tempPath)
    if (!fs.existsSync(tempPath)) return
    console.log('解压文件')
    const unzip = new AdmZip(tempPath)
    console.log(unzip)
    const appPath = join(resourcesPath, 'app')
    if (!fs.existsSync(appPath)) {
      fs.mkdirSync(appPath)
    }
    await unzip.extractAllTo(appPath, true)
    //删除暂存文件
    await fs.unlinkSync(tempPath)
    //告知渲染进程下载完成
    mainWindow.webContents.send('updateDownloaded', { downloaded: true })
    setTimeout(() => {
      restartApp()
    }, 1000)
  } catch (error) {
    console.log(error)
  }
}
//重启electron
export function restartApp() {
  app.relaunch()
  app.exit()
}
//下载文件
export async function downloadFile(mainWindow, resourcesPath, name) {
  //创建暂存文件夹
  if (!fs.existsSync(resourcesPath)) {
    fs.mkdirSync(resourcesPath)
  }
  const tempPath = join(resourcesPath, name)
  const writer = fs.createWriteStream(tempPath)
  //下载进度
  const response = await axios({
    url: config.downloadUrl,
    method: 'get',
    responseType: 'stream',
    params: {
      timestamp: new Date().getTime()
    }
    // onDownloadProgress: (progressEvent) => {
    //   const { loaded, total = 0 } = progressEvent
    //   const percent = Math.round((loaded * 100) / total)
    //   mainWindow.webContents.send('downloadProgress', { percent })
    // }
  })
  response.data.pipe(writer)
  return new Promise<void>((resolve, reject) => {
    writer.on('finish', () => {
      resolve()
    })
    writer.on('error', (err) => {
      reject(err)
    })
  })
}

// 载入React DevTools
// 由于版本冲突，目前使用手动引入插件包

export const loadReactExtension = async (userPath: string) => {
  const reactDevToolsPath = join(
    homedir(), // macOs返回当前用户主目录的字符串路径。
    userPath // 路径是本地插件包路径
  )
  await session.defaultSession
    .loadExtension(reactDevToolsPath, { allowFileAccess: true })
    .catch((e) => console.log(e))
}
//注册windows唤醒事件
export function registerWakeUpEvent() {
  app.setAsDefaultProtocolClient('livechat')
}
//退出应用
export function quitApp() {
  ipcMain.on('quitApp', () => {
    app.quit()
  })
}

export function downloadChatFile(mainWindow) {
  ipcMain.on('download-chat-file', async (event, file) => {
    const fileURL = file.url
    const fileName = file.name
    const downloadOptions = {
      saveAs: true,
      filename: fileName
    }
    try {
      await download(mainWindow, fileURL, downloadOptions)
    } catch (error) {
      console.error('Download error:', error)
    }
  })
}
/**
 * 开启electron拼写检查
 */
export function spellCheck(win: BrowserWindow) {
  // console.log(win.webContents.session.availableSpellCheckerLanguages)
  win.webContents.session.setSpellCheckerLanguages(['en-US'])
}
