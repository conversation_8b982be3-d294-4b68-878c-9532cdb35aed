import { app, BrowserWindow, shell } from 'electron'
import { release } from 'node:os'
import { join } from 'node:path'
import {
  feishuLogin,
  systemMessage,
  loadReactExtension,
  setTray,
  registerWakeUpEvent
} from './util'

process.env.DIST_ELECTRON = join(__dirname, '../')
process.env.DIST = join(process.env.DIST_ELECTRON, '../dist')
process.env.PUBLIC = process.env.VITE_DEV_SERVER_URL
  ? join(process.env.DIST_ELECTRON, '../public')
  : process.env.DIST
if (release().startsWith('6.1')) app.disableHardwareAcceleration()

// Set application name for Windows 10+ notifications
if (process.platform === 'win32') app.setAppUserModelId(app.getName())

if (!app.requestSingleInstanceLock()) {
  app.quit()
  process.exit(0)
}

let win: BrowserWindow | null = null
// Here, you can also use other preload
const preload = join(__dirname, '../preload/index.js')
const url = process.env.VITE_DEV_SERVER_URL
const indexHtml = join(process.env.DIST, 'index.html')

async function createWindow() {
  win = new BrowserWindow({
    title: 'Main window',
    icon: join(process.env.PUBLIC, 'favicon.ico'),
    width: 1200,
    height: 700,
    fullscreen: true,
    show: true,
    minWidth: 1200,
    minHeight: 700,
    autoHideMenuBar: true,
    frame: true,
    webPreferences: {
      preload,
      nodeIntegration: true,
      contextIsolation: false
    }
  })
  setTray(win)
  if (process.env.VITE_DEV_SERVER_URL) {
    win.loadURL(url)
    // loadReactExtension('/ReactDevTools')
    // loadReactExtension('/ReduxDevTools')
    // Open devTool if the app is not packaged
    win.webContents.openDevTools()
  } else {
    win.webContents.openDevTools()
    win.loadFile(indexHtml)
  }
  win.on('ready-to-show', () => {
    win.show()
    systemMessage(win)
    // addUpdateApp(win)
  })
  //飞书登录
  feishuLogin(win)

  // Test actively push message to the Electron-Renderer
  win.webContents.on('did-finish-load', () => {
    win?.webContents.send('main-process-message', new Date().toLocaleString())
  })

  // Make all links open with the browser, not with the application
  win.webContents.setWindowOpenHandler(({ url }) => {
    if (url.startsWith('https:')) shell.openExternal(url)
    return { action: 'deny' }
  })
}

app.whenReady().then(createWindow)
registerWakeUpEvent()

app.on('window-all-closed', () => {
  win = null
  if (process.platform !== 'darwin') app.quit()
})

app.on('second-instance', () => {
  if (win) {
    // Focus on the main window if the user tried to open another
    if (win.isMinimized()) win.restore()
    win.focus()
  }
})

app.on('activate', () => {
  const allWindows = BrowserWindow.getAllWindows()
  if (allWindows.length) {
    allWindows[0].focus()
  } else {
    createWindow()
  }
})
