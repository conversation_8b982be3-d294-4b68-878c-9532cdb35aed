/* eslint-disable @typescript-eslint/no-var-requires */
const { notarize } = require('@electron/notarize')

module.exports = async (context) => {
  if (process.platform !== 'darwin') return

  console.log('aftersign hook triggered, start to notarize app.')

  if (!process.env.CI) {
    // console.log(`skipping notarizing, not in CI.`)
    return
  }

  if (!('APPLE_ID' in process.env && 'APPLE_ID_PASS' in process.env)) {
    console.warn(
      'skipping notarizing, APPLE_ID and APPLE_ID_PASS env variables must be set.'
    )
    return
  }

  const appId = 'com.feisu.service'

  const { appOutDir } = context

  const appName = context.packager.appInfo.productFilename

  try {
    await notarize({
      appBundleId: 'com.feisu.service',
      appPath: `${appOutDir}/${appName}.app`,
      appleId: '<EMAIL>',
      appleIdPassword: 'oxur-whib-hjmv-sqri'
    })
  } catch (error) {
    console.error(error)
  }

  console.log(`done notarizing ${appId}.`)
}
