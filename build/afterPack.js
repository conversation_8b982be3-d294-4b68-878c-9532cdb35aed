/* eslint-disable @typescript-eslint/no-var-requires */
const path = require('path')
const AdmZip = require('adm-zip')
//将app文件夹打包成app.zip
exports.default = async (context) => {
  console.log('afterPack')
  console.log(context)
  let targetPath
  if (context.packager.platform.nodeName === 'darwin') {
    targetPath = path.join(
      context.appOutDir,
      `${context.packager.appInfo.productName}.app/Contents/Resources`
    )
  } else {
    targetPath = path.join(context.appOutDir, './resources')
  }
  const unpacked = path.join(targetPath, './app')
  const zip = new AdmZip()
  zip.addLocalFolder(unpacked)
  zip.writeZip(path.join(context.outDir, 'app.zip'))
}
