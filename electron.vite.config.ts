import path from 'node:path'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import react from '@vitejs/plugin-react'

export default defineConfig(({ mode }) => {
  return {
    main: {
      plugins: [externalizeDepsPlugin()]
    },
    preload: {
      plugins: [externalizeDepsPlugin()]
    },
    renderer: {
      css: {
        preprocessorOptions: {
          scss: {
            additionalData:
              '@import "./src/renderer/src/assets/scss/global.scss";'
          }
        }
      },
      resolve: {
        alias: {
          '@renderer': path.resolve('src/renderer/src'),
          '@': path.resolve('src/renderer/src')
        }
      },
      plugins: [react()],
      esbuild: {
        drop: mode === 'production' ? ['console', 'debugger'] : []
      }
      // server: {
      //   port: 6666
      // }
    }
  }
})
